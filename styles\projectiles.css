:root {
  --bg: #0b1020;
  --card: #121933;
  --accent: #3aa0ff;
  --accent-2: #7bd389;
  --text: #e9eefb;
  --muted: #a9b4d0;
  --danger: #ff5c5c;
  --shadow: 0 10px 30px rgba(0,0,0,0.35);
}

* { box-sizing: border-box; }
html, body {
  margin: 0;
  padding: 0;
  background: linear-gradient(180deg, #0b1020, #0b1329 40%, #0b1020);
  color: var(--text);
  font-family: "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, "Noto Kufi Arabic", "Cairo", sans-serif;
}

body[dir="rtl"] { direction: rtl; }
body[dir="ltr"] { direction: ltr; }

a { color: var(--accent); text-decoration: none; }
a:hover { text-decoration: underline; }

.site-header {
  position: sticky; top: 0; z-index: 1000;
  background: rgba(11,16,32,0.7);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255,255,255,0.06);
}
.navbar {
  max-width: 1200px; margin: 0 auto; padding: 12px 16px;
  display: flex; align-items: center; justify-content: space-between; gap: 16px;
}
.brand-title { font-size: 1.2rem; font-weight: 700; letter-spacing: 0.5px; }
.brand-sub { color: var(--muted); display: block; font-size: 0.78rem; }

.nav-links { list-style: none; display: flex; gap: 12px; padding: 0; margin: 0; flex-wrap: wrap; }
.nav-links a {
  display: inline-block; padding: 8px 12px; border-radius: 8px; color: var(--text);
}
.nav-links a:hover { background: rgba(255,255,255,0.06); }

.lang-toggle {
  border: 1px solid rgba(255,255,255,0.2);
  background: transparent; color: var(--text);
  padding: 8px 10px; border-radius: 8px; cursor: pointer;
}
.lang-toggle:hover { background: rgba(255,255,255,0.08); }

.container { max-width: 1200px; margin: 24px auto; padding: 0 16px; }

.breadcrumbs {
  color: var(--muted); font-size: 0.9rem; margin-bottom: 10px;
}
.breadcrumbs .sep { opacity: 0.6; margin: 0 6px; }

.lesson-hero {
  background: radial-gradient(1200px 400px at 50% -20%, rgba(58,160,255,0.15), transparent 60%);
  padding: 16px 0 8px 0;
}
.lesson-hero h1 { margin: 0 0 8px 0; font-size: 1.6rem; }
.lesson-hero .lead { margin: 0; color: var(--muted); }

.card {
  background: linear-gradient(180deg, rgba(18,25,51,0.9), rgba(18,25,51,0.8));
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 14px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: var(--shadow);
}

.theory-grid {
  display: grid; grid-template-columns: 1fr; gap: 16px;
}
@media (min-width: 900px) {
  .theory-grid { grid-template-columns: 1.1fr 0.9fr; }
}

.formula {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", monospace;
  background: rgba(58,160,255,0.12);
  border: 1px solid rgba(58,160,255,0.35);
  color: #d9eeff;
  display: inline-block;
  padding: 10px 12px;
  border-radius: 10px;
  margin: 8px 0 10px 0;
}

.bullets { margin-top: 6px; padding-inline-start: 1.2em; color: var(--muted); }

.diagram-box {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 12px; padding: 8px;
}
.p5-container { width: 100%; height: 360px; }

.legend { display: flex; gap: 10px; align-items: center; flex-wrap: wrap; margin-bottom: 6px; color: var(--muted); }
.legend-item { display: inline-flex; align-items: center; gap: 6px; }
.legend .dot { width: 10px; height: 10px; border-radius: 50%; display: inline-block; }
.legend .dot.v { background: var(--text); }
.legend .dot.vx { background: var(--accent); }
.legend .dot.vy { background: var(--accent-2); }
.legend .dot.drag { background: var(--danger); }

.lab-grid { display: grid; grid-template-columns: 1fr; gap: 16px; }
@media (min-width: 1100px) {
  .lab-grid { grid-template-columns: 0.9fr 1.1fr; }
}
.lab-controls .field { margin-bottom: 12px; }
.lab-controls label { display: inline-block; min-width: 140px; }
.lab-controls output { color: var(--accent-2); font-weight: 700; margin-inline-start: 6px; }
.field.inline { display: grid; grid-template-columns: 1fr; gap: 8px; }
@media (min-width: 800px) {
  .field.inline { grid-template-columns: repeat(2, minmax(0,1fr)); }
}
.toggle input { margin-inline-end: 6px; }

.btn {
  background: var(--accent); color: #001428; border: none; border-radius: 10px;
  padding: 8px 12px; cursor: pointer; font-weight: 700;
}
.btn:hover { filter: brightness(1.05); }
.btn.outline {
  background: transparent; color: var(--accent);
  border: 1px solid var(--accent); padding: 8px 12px;
}
.presets { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 8px; }

.dash { margin-top: 10px; }
.dash-row { display: grid; grid-template-columns: repeat(3,1fr); gap: 8px; }
@media (min-width: 800px) {
  .dash-row { grid-template-columns: repeat(5,1fr); }
}
.dash-item {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 8px; padding: 8px; text-align: center; font-family: ui-monospace, monospace;
}

.site-footer {
  border-top: 1px solid rgba(255,255,255,0.06);
  background: rgba(11,16,32,0.7);
}
.footer-content {
  max-width: 1200px; margin: 0 auto; padding: 14px 16px;
  display: grid; gap: 10px;
}
@media (min-width: 800px) {
  .footer-content { grid-template-columns: 1fr 1fr; }
}
.footer-content .left, .footer-content .right { color: var(--muted); }
