/**
 * PhysicsHub - States of Matter Simulator
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: temperature, pressure, molecule count
 * - p5.js visualization: molecular motion in different states
 * - Live dashboard: kinetic energy, phase info, molecular motion
 * - Chart.js: energy distribution visualization
 * - Phase transitions: solid, liquid, gas states
 */

// -------------------- i18n (AR/EN) --------------------
const SOMDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "محاكي الحالات الثلاث للمادة",
    lesson_title: "محاكي الحالات الثلاث للمادة: تحولات الطور",
    lesson_lead: "استكشف كيف تتحول المادة بين الحالات الثلاث (صلبة، سائلة، غازية) بتأثير درجة الحرارة والضغط. شاهد حركة الجزيئات وتفاعلها في كل حالة وتعلم نقاط التحول بين الأطوار.",
    theory_title: "المفهوم الأساسي",
    t1: "الحالة الصلبة: جزيئات مترابطة بقوة، حركة اهتزازية محدودة",
    t2: "الحالة السائلة: جزيئات متحركة، روابط أضعف من الصلب",
    t3: "الحالة الغازية: جزيئات حرة الحركة، روابط ضعيفة جداً",
    t4: "نقاط التحول: انصهار، تبخر، تجمد، تكثف",
    t5: "الطاقة الحركية للجزيئات تزداد مع درجة الحرارة",
    lab_title: "المعلمات التفاعلية",
    state_title: "حالة المادة",
    state_solid: "صلبة",
    state_liquid: "سائلة",
    state_gas: "غازية",
    temperature_label: "درجة الحرارة T (K)",
    pressure_label: "الضغط P (atm)",
    molecules_label: "عدد الجزيئات",
    interaction_label: "قوة التفاعل بين الجزيئات",
    transitions_title: "نقاط التحول",
    melting_point: "نقطة الانصهار",
    boiling_point: "نقطة الغليان",
    reset: "إعادة ضبط",
    start: "بدء المحاكاة",
    kinetic_energy_title: "الطاقة الحركية",
    phase_info_title: "معلومات الطور",
    molecular_motion_title: "الحركة الجزيئية",
    dash_avg_ke: "KE_avg",
    dash_avg_speed: "v_avg",
    dash_current_state: "الحالة",
    dash_density: "الكثافة",
    dash_vibration: "الاهتزاز",
    dash_translation: "الانتقال",
    state_solid_text: "صلبة",
    state_liquid_text: "سائلة",
    state_gas_text: "غازية",
    motion_high: "عالي",
    motion_medium: "متوسط",
    motion_low: "منخفض",
    cta_title: "الهدف التعليمي",
    cta_text: "فهم العلاقة بين درجة الحرارة والضغط وحالة المادة. اكتشاف كيف تؤثر الطاقة الحركية للجزيئات على نوع الحركة (اهتزازية، انتقالية، دورانية) وكيف تحدث تحولات الطور عند نقاط حرجة معينة.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "States of Matter Simulator",
    lesson_title: "States of Matter Simulator: Phase Transitions",
    lesson_lead: "Explore how matter transforms between the three states (solid, liquid, gas) under the influence of temperature and pressure. Watch molecular motion and interactions in each state and learn about phase transition points.",
    theory_title: "Core Concept",
    t1: "Solid state: strongly bonded molecules, limited vibrational motion",
    t2: "Liquid state: mobile molecules, weaker bonds than solid",
    t3: "Gas state: freely moving molecules, very weak bonds",
    t4: "Transition points: melting, vaporization, freezing, condensation",
    t5: "Molecular kinetic energy increases with temperature",
    lab_title: "Interactive Parameters",
    state_title: "State of Matter",
    state_solid: "Solid",
    state_liquid: "Liquid",
    state_gas: "Gas",
    temperature_label: "Temperature T (K)",
    pressure_label: "Pressure P (atm)",
    molecules_label: "Number of molecules",
    interaction_label: "Intermolecular interaction strength",
    transitions_title: "Transition Points",
    melting_point: "Melting point",
    boiling_point: "Boiling point",
    reset: "Reset",
    start: "Start Simulation",
    kinetic_energy_title: "Kinetic Energy",
    phase_info_title: "Phase Information",
    molecular_motion_title: "Molecular Motion",
    dash_avg_ke: "KE_avg",
    dash_avg_speed: "v_avg",
    dash_current_state: "State",
    dash_density: "Density",
    dash_vibration: "Vibration",
    dash_translation: "Translation",
    state_solid_text: "Solid",
    state_liquid_text: "Liquid",
    state_gas_text: "Gas",
    motion_high: "High",
    motion_medium: "Medium",
    motion_low: "Low",
    cta_title: "Learning Objective",
    cta_text: "Understand the relationship between temperature, pressure, and state of matter. Discover how molecular kinetic energy affects motion types (vibrational, translational, rotational) and how phase transitions occur at specific critical points.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let somLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyStatesOfMatterI18n() {
  const t = SOMDICT[somLang];
  document.documentElement.lang = somLang === "ar" ? "ar" : "en";
  document.documentElement.dir = somLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  
  updateStateText();
  updateMotionText();
}

function setupLangToggleStatesOfMatter() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      somLang = somLang === "ar" ? "en" : "ar";
      applyStatesOfMatterI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let somParams = {
  state: "solid",        // solid, liquid, gas
  temperature: 273,      // Kelvin
  pressure: 1.0,         // atm
  moleculeCount: 100,
  interactionStrength: 5.0,
  meltingPoint: 273,     // K
  boilingPoint: 373      // K
};

// -------------------- Simulation State --------------------
let molecularState = {
  molecules: [],
  avgKineticEnergy: 0,
  avgSpeed: 0,
  currentState: "solid",
  density: 1.0,
  vibrationLevel: "high",
  translationLevel: "low",
  isRunning: false
};

// Molecule class
class Molecule {
  constructor(x, y) {
    this.x = x;
    this.y = y;
    this.vx = 0;
    this.vy = 0;
    this.radius = 3;
    this.mass = 1;
    this.homeX = x; // For solid state
    this.homeY = y;
  }
  
  update(state, temperature, interactionStrength) {
    const kT = temperature * 0.01; // Simplified Boltzmann factor
    
    if (state === "solid") {
      // Vibrational motion around home position
      const restoreForceX = (this.homeX - this.x) * 0.1;
      const restoreForceY = (this.homeY - this.y) * 0.1;
      
      this.vx += restoreForceX + (Math.random() - 0.5) * kT;
      this.vy += restoreForceY + (Math.random() - 0.5) * kT;
      
      // Damping
      this.vx *= 0.9;
      this.vy *= 0.9;
      
    } else if (state === "liquid") {
      // More freedom but still some attraction to neighbors
      this.vx += (Math.random() - 0.5) * kT * 2;
      this.vy += (Math.random() - 0.5) * kT * 2;
      
      // Moderate damping
      this.vx *= 0.95;
      this.vy *= 0.95;
      
    } else if (state === "gas") {
      // Free motion
      this.vx += (Math.random() - 0.5) * kT * 0.5;
      this.vy += (Math.random() - 0.5) * kT * 0.5;
      
      // Minimal damping
      this.vx *= 0.99;
      this.vy *= 0.99;
    }
    
    // Limit velocity
    const maxSpeed = state === "solid" ? 2 : state === "liquid" ? 4 : 6;
    const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
    if (speed > maxSpeed) {
      this.vx = (this.vx / speed) * maxSpeed;
      this.vy = (this.vy / speed) * maxSpeed;
    }
    
    // Update position
    this.x += this.vx;
    this.y += this.vy;
  }
  
  checkBounds(width, height) {
    if (this.x < this.radius) {
      this.x = this.radius;
      this.vx *= -0.8;
    }
    if (this.x > width - this.radius) {
      this.x = width - this.radius;
      this.vx *= -0.8;
    }
    if (this.y < this.radius) {
      this.y = this.radius;
      this.vy *= -0.8;
    }
    if (this.y > height - this.radius) {
      this.y = height - this.radius;
      this.vy *= -0.8;
    }
  }
  
  getKineticEnergy() {
    return 0.5 * this.mass * (this.vx * this.vx + this.vy * this.vy);
  }
  
  getSpeed() {
    return Math.sqrt(this.vx * this.vx + this.vy * this.vy);
  }
}

// -------------------- p5.js Sketch --------------------
let p5Instance;
let workspace;

function initP5StatesOfMatter() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  // Initialize workspace manager
  workspace = initializeWorkspace('states_of_matter');

  p5Instance = new p5((p) => {
    p.setup = () => {
      const dimensions = workspace.getCanvasDimensions();
      const canvas = p.createCanvas(dimensions.width, dimensions.height);
      canvas.parent(container);
      initializeMolecules(p);
      calculateMolecularProperties();
    };

    p.windowResized = () => {
      const dimensions = workspace.getCanvasDimensions();
      p.resizeCanvas(dimensions.width, dimensions.height);
      initializeMolecules(p);
    };

    p.draw = () => {
      p.background(15, 20, 35);

      // Draw workspace grid
      workspace.drawGrid(p, 0.02);

      // Draw container based on state
      drawContainer(p);

      // Update and draw molecules
      if (molecularState.isRunning) {
        updateMolecules(p);
      }
      drawMolecules(p);

      // Draw state information
      drawStateInfo(p);
    };
  });
}

function initializeMolecules(p) {
  molecularState.molecules = [];
  
  const cols = Math.ceil(Math.sqrt(somParams.moleculeCount));
  const rows = Math.ceil(somParams.moleculeCount / cols);
  
  const spacing = Math.min(p.width / (cols + 1), p.height / (rows + 1));
  const startX = (p.width - (cols - 1) * spacing) / 2;
  const startY = (p.height - (rows - 1) * spacing) / 2;
  
  for (let i = 0; i < somParams.moleculeCount; i++) {
    const col = i % cols;
    const row = Math.floor(i / cols);
    
    let x, y;
    
    if (somParams.state === "solid") {
      x = startX + col * spacing;
      y = startY + row * spacing;
    } else if (somParams.state === "liquid") {
      x = startX + col * spacing + (Math.random() - 0.5) * spacing * 0.3;
      y = startY + row * spacing + (Math.random() - 0.5) * spacing * 0.3;
    } else { // gas
      x = Math.random() * (p.width - 20) + 10;
      y = Math.random() * (p.height - 20) + 10;
    }
    
    molecularState.molecules.push(new Molecule(x, y));
  }
}

function drawContainer(p) {
  // Draw container walls
  p.stroke(255, 255, 255, 100);
  p.strokeWeight(2);
  p.noFill();
  p.rect(5, 5, p.width - 10, p.height - 10);
  
  // Add state-specific visual effects
  if (somParams.state === "solid") {
    // Draw lattice structure hints
    p.stroke(99, 102, 241, 50);
    p.strokeWeight(1);
    const spacing = 30;
    for (let x = spacing; x < p.width; x += spacing) {
      p.line(x, 0, x, p.height);
    }
    for (let y = spacing; y < p.height; y += spacing) {
      p.line(0, y, p.width, y);
    }
  } else if (somParams.state === "liquid") {
    // Draw fluid surface
    p.stroke(59, 130, 246, 100);
    p.strokeWeight(3);
    const waveHeight = 5;
    const time = p.millis() * 0.002;
    
    p.beginShape();
    p.noFill();
    for (let x = 0; x <= p.width; x += 5) {
      const y = p.height * 0.8 + Math.sin(x * 0.02 + time) * waveHeight;
      p.vertex(x, y);
    }
    p.endShape();
  }
}

function updateMolecules(p) {
  for (let molecule of molecularState.molecules) {
    molecule.update(somParams.state, somParams.temperature, somParams.interactionStrength);
    molecule.checkBounds(p.width, p.height);
  }
  
  // Handle intermolecular interactions
  if (somParams.state !== "gas") {
    handleMolecularInteractions();
  }
  
  calculateMolecularProperties();
}

function handleMolecularInteractions() {
  const interactionRange = somParams.state === "solid" ? 20 : 15;
  const interactionStrength = somParams.interactionStrength * 0.001;
  
  for (let i = 0; i < molecularState.molecules.length; i++) {
    for (let j = i + 1; j < molecularState.molecules.length; j++) {
      const mol1 = molecularState.molecules[i];
      const mol2 = molecularState.molecules[j];
      
      const dx = mol2.x - mol1.x;
      const dy = mol2.y - mol1.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < interactionRange && distance > 0) {
        const force = interactionStrength / (distance * distance);
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;
        
        // Repulsive force at short distances
        if (distance < 10) {
          mol1.vx -= fx * 10;
          mol1.vy -= fy * 10;
          mol2.vx += fx * 10;
          mol2.vy += fy * 10;
        } else {
          // Attractive force at medium distances
          mol1.vx += fx;
          mol1.vy += fy;
          mol2.vx -= fx;
          mol2.vy -= fy;
        }
      }
    }
  }
}

function drawMolecules(p) {
  for (let molecule of molecularState.molecules) {
    // Color based on speed (temperature visualization)
    const speed = molecule.getSpeed();
    const maxSpeed = somParams.state === "solid" ? 2 : somParams.state === "liquid" ? 4 : 6;
    const intensity = Math.min(speed / maxSpeed, 1);
    
    const r = 100 + intensity * 155;
    const g = 100 + intensity * 100;
    const b = 255 - intensity * 155;
    
    p.fill(r, g, b, 200);
    p.noStroke();
    p.circle(molecule.x, molecule.y, molecule.radius * 2);
    
    // Draw velocity vector for gas molecules
    if (somParams.state === "gas" && speed > 1) {
      p.stroke(255, 255, 255, 100);
      p.strokeWeight(1);
      const scale = 3;
      p.line(molecule.x, molecule.y, 
             molecule.x + molecule.vx * scale, 
             molecule.y + molecule.vy * scale);
    }
  }
}

function drawStateInfo(p) {
  // Draw temperature and pressure info
  p.fill(255);
  p.noStroke();
  p.textAlign(p.LEFT);
  p.textSize(12);
  
  p.text(`T: ${somParams.temperature} K`, 10, 20);
  p.text(`P: ${somParams.pressure.toFixed(1)} atm`, 10, 35);
  
  // Draw state indicator
  const stateColors = {
    solid: [99, 102, 241],
    liquid: [59, 130, 246],
    gas: [139, 92, 246]
  };
  
  const color = stateColors[somParams.state];
  p.fill(color[0], color[1], color[2]);
  p.circle(p.width - 30, 20, 20);
  
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(10);
  const stateLabels = {
    solid: "S",
    liquid: "L", 
    gas: "G"
  };
  p.text(stateLabels[somParams.state], p.width - 30, 25);
}

function calculateMolecularProperties() {
  if (molecularState.molecules.length === 0) return;
  
  let totalKE = 0;
  let totalSpeed = 0;
  
  for (let molecule of molecularState.molecules) {
    totalKE += molecule.getKineticEnergy();
    totalSpeed += molecule.getSpeed();
  }
  
  molecularState.avgKineticEnergy = totalKE / molecularState.molecules.length;
  molecularState.avgSpeed = totalSpeed / molecularState.molecules.length;
  
  // Determine current state based on temperature
  if (somParams.temperature < somParams.meltingPoint) {
    molecularState.currentState = "solid";
  } else if (somParams.temperature < somParams.boilingPoint) {
    molecularState.currentState = "liquid";
  } else {
    molecularState.currentState = "gas";
  }
  
  // Calculate density (simplified)
  const area = p5Instance ? p5Instance.width * p5Instance.height : 10000;
  molecularState.density = (molecularState.molecules.length / area) * 1000;
  
  // Determine motion levels
  const avgSpeed = molecularState.avgSpeed;
  if (avgSpeed < 1) {
    molecularState.vibrationLevel = "low";
    molecularState.translationLevel = "low";
  } else if (avgSpeed < 3) {
    molecularState.vibrationLevel = "medium";
    molecularState.translationLevel = "medium";
  } else {
    molecularState.vibrationLevel = "high";
    molecularState.translationLevel = "high";
  }
  
  updateStatesOfMatterDashboard();
  updateEnergyDistributionChart();
}

// -------------------- Controls --------------------
function bindStatesOfMatterControls() {
  // State buttons
  document.querySelectorAll("[data-state]").forEach(btn => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      document.querySelectorAll("[data-state]").forEach(b => b.classList.remove("active"));
      // Add active class to clicked button
      btn.classList.add("active");
      
      somParams.state = btn.getAttribute("data-state");
      
      // Reinitialize molecules for new state
      if (p5Instance) {
        initializeMolecules(p5Instance);
      }
      
      calculateMolecularProperties();
    });
  });
  
  // Sliders
  const sliders = [
    { id: "temperature", param: "temperature", output: "temperatureOut", decimals: 0 },
    { id: "pressure", param: "pressure", output: "pressureOut", decimals: 1 },
    { id: "moleculeCount", param: "moleculeCount", output: "moleculeCountOut", decimals: 0 },
    { id: "interactionStrength", param: "interactionStrength", output: "interactionStrengthOut", decimals: 1 }
  ];
  
  sliders.forEach(({ id, param, output, decimals }) => {
    const slider = document.getElementById(id);
    const outputEl = document.getElementById(output);
    
    if (slider && outputEl) {
      slider.addEventListener("input", () => {
        somParams[param] = parseFloat(slider.value);
        outputEl.textContent = somParams[param].toFixed(decimals);
        
        // Reinitialize molecules if count changed
        if (param === "moleculeCount" && p5Instance) {
          initializeMolecules(p5Instance);
        }
        
        calculateMolecularProperties();
      });
    }
  });
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", () => {
      somParams = {
        state: "solid",
        temperature: 273,
        pressure: 1.0,
        moleculeCount: 100,
        interactionStrength: 5.0,
        meltingPoint: 273,
        boilingPoint: 373
      };
      
      // Update UI
      document.getElementById("temperature").value = 273;
      document.getElementById("pressure").value = 1.0;
      document.getElementById("moleculeCount").value = 100;
      document.getElementById("interactionStrength").value = 5.0;
      
      // Update outputs
      document.getElementById("temperatureOut").textContent = "273";
      document.getElementById("pressureOut").textContent = "1.0";
      document.getElementById("moleculeCountOut").textContent = "100";
      document.getElementById("interactionStrengthOut").textContent = "5.0";
      
      // Reset state
      document.querySelectorAll("[data-state]").forEach(b => b.classList.remove("active"));
      document.querySelector("[data-state='solid']").classList.add("active");
      
      molecularState.isRunning = false;
      
      if (p5Instance) {
        initializeMolecules(p5Instance);
      }
      calculateMolecularProperties();
    });
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      molecularState.isRunning = !molecularState.isRunning;
      startBtn.textContent = molecularState.isRunning ? 
        (somLang === "ar" ? "إيقاف المحاكاة" : "Stop Simulation") : 
        (somLang === "ar" ? "بدء المحاكاة" : "Start Simulation");
    });
  }
}

function updateStateText() {
  const el = document.getElementById("dashCurrentState");
  if (el) {
    const t = SOMDICT[somLang];
    const stateKey = `state_${molecularState.currentState}_text`;
    el.textContent = t[stateKey] || molecularState.currentState;
  }
}

function updateMotionText() {
  const vibEl = document.getElementById("dashVibration");
  const transEl = document.getElementById("dashTranslation");
  
  if (vibEl && transEl) {
    const t = SOMDICT[somLang];
    vibEl.textContent = t[`motion_${molecularState.vibrationLevel}`] || molecularState.vibrationLevel;
    transEl.textContent = t[`motion_${molecularState.translationLevel}`] || molecularState.translationLevel;
    
    // Apply color classes
    vibEl.className = `${molecularState.vibrationLevel}-motion`;
    transEl.className = `${molecularState.translationLevel}-motion`;
  }
}

function updateStatesOfMatterDashboard() {
  // Kinetic energy values
  const avgKEEl = document.getElementById("dashAvgKE");
  const avgSpeedEl = document.getElementById("dashAvgSpeed");
  
  if (avgKEEl) avgKEEl.textContent = (molecularState.avgKineticEnergy * 1000).toFixed(1);
  if (avgSpeedEl) avgSpeedEl.textContent = molecularState.avgSpeed.toFixed(1);
  
  // Phase info
  const densityEl = document.getElementById("dashDensity");
  
  if (densityEl) densityEl.textContent = molecularState.density.toFixed(2);
  
  // Update transition points
  const meltingEl = document.getElementById("meltingPoint");
  const boilingEl = document.getElementById("boilingPoint");
  
  if (meltingEl) meltingEl.textContent = `${somParams.meltingPoint} K`;
  if (boilingEl) boilingEl.textContent = `${somParams.boilingPoint} K`;
  
  updateStateText();
  updateMotionText();
}

// -------------------- Chart --------------------
let energyDistributionChart;

function initEnergyDistributionChart() {
  const ctx = document.getElementById("energyDistributionChart");
  if (!ctx) return;
  
  energyDistributionChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: ['Solid', 'Liquid', 'Gas'],
      datasets: [{
        label: 'Relative Energy',
        data: [1, 2, 4],
        backgroundColor: [
          'rgba(99, 102, 241, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(139, 92, 246, 0.8)'
        ],
        borderColor: [
          '#6366f1',
          '#3b82f6',
          '#8b5cf6'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Energy Level', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          display: false
        }
      }
    }
  });
}

function updateEnergyDistributionChart() {
  if (!energyDistributionChart) return;
  
  // Update based on current temperature
  const tempRatio = somParams.temperature / 373; // Normalize to boiling point
  
  energyDistributionChart.data.datasets[0].data = [
    tempRatio * 0.5,  // Solid
    tempRatio * 1.0,  // Liquid
    tempRatio * 2.0   // Gas
  ];
  
  energyDistributionChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleStatesOfMatter();
  applyStatesOfMatterI18n();
  bindStatesOfMatterControls();
  initP5StatesOfMatter();
  initEnergyDistributionChart();
  calculateMolecularProperties();
});
