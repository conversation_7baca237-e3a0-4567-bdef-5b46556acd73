<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>البندول البسيط | منصة الفيزياء</title>
  <link rel="stylesheet" href="styles/pendulum.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <header class="app-header">
    <div class="brand">
      <a href="index.html" class="logo" aria-label="العودة للرئيسية">PhysicsHub</a>
      <span class="divider"></span>
      <span class="page-title">البندول البسيط</span>
    </div>
    <nav class="nav">
      <a href="circular_motion.html">الحركة الدائرية</a>
      <a href="projectiles.html">المقذوفات</a>
      <a href="ideal_gas.html">الغاز المثالي</a>
      <a class="active" href="pendulum.html" aria-current="page">البندول</a>
    </nav>
  </header>

  <main class="container">
    <section class="panel controls">
      <h2>المدخلات</h2>
      <div class="grid">
        <label class="field">
          <span>طول الخيط L (م)</span>
          <input id="len" type="range" min="0.1" max="2.5" step="0.01" value="1.0" />
          <input id="lenVal" type="number" min="0.1" max="2.5" step="0.01" value="1.0" />
        </label>

        <label class="field">
          <span>التسارع g (م/ث²)</span>
          <input id="grav" type="range" min="1" max="20" step="0.1" value="9.81" />
          <input id="gravVal" type="number" min="1" max="20" step="0.1" value="9.81" />
        </label>

        <label class="field">
          <span>زاوية البدء θ₀ (°)</span>
          <input id="theta0" type="range" min="-60" max="60" step="1" value="20" />
          <input id="theta0Val" type="number" min="-60" max="60" step="1" value="20" />
        </label>

        <label class="field">
          <span>إخماد (نسبي)</span>
          <input id="damp" type="range" min="0" max="0.2" step="0.005" value="0.01" />
          <input id="dampVal" type="number" min="0" max="0.2" step="0.005" value="0.01" />
        </label>

        <label class="field">
          <span>سرعة المحاكاة</span>
          <input id="speed" type="range" min="0.1" max="2" step="0.1" value="1" />
          <input id="speedVal" type="number" min="0.1" max="2" step="0.1" value="1" />
        </label>
      </div>

      <div class="actions">
        <button id="startBtn" class="btn primary">بدء</button>
        <button id="pauseBtn" class="btn">إيقاف مؤقت</button>
        <button id="resetBtn" class="btn danger">إعادة تعيين</button>
      </div>

      <div class="info">
        <div>الفترة التقريبية (زاوية صغيرة): <strong id="Tsmall">—</strong> ث</div>
        <div>الفترة العددية (من المحاكاة): <strong id="Tnum">—</strong> ث</div>
        <div>الطاقة الميكانيكية (مقيسة): <strong id="Em">—</strong></div>
      </div>
    </section>

    <section class="panel scene">
      <h2>المحاكاة</h2>
      <canvas id="pendulumCanvas" width="520" height="360" aria-label="مشهد البندول"></canvas>
    </section>

    <section class="panel charts">
      <h2>رسوم بيانية</h2>
      <div class="charts-grid">
        <canvas id="angleCanvas" width="520" height="180" aria-label="زاوية مقابل الزمن"></canvas>
        <canvas id="phaseCanvas" width="520" height="180" aria-label="طور θ-ω"></canvas>
      </div>
    </section>

    <section class="panel theory">
      <h2>الخلفية النظرية</h2>
      <p>
        البندول البسيط يتكون من كتلة مخروطية صغيرة مربوطة بخيط خفيف غير قابل للتمدد. عند الزوايا الصغيرة، يتم تقريب المعادلة التفاضلية
        θ'' + (g/L) θ = 0، وتكون الفترة التقريبية T ≈ 2π√(L/g). للمجالات الأكبر من الزاوية، تصبح الحركة غير خطية ويختلف الزمن
        الفعلي قليلاً عن التقريب السابق.
      </p>
    </section>
  </main>

  <footer class="app-footer">
    <span>© 2025 منصة الفيزياء التفاعلية</span>
  </footer>

  <script src="scripts/pendulum.js"></script>
</body>
</html>
