/**
 * PhysicsHub - Specific Heat Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: material type, masses, temperatures
 * - p5.js visualization: calorimeter with heat exchange
 * - Live dashboard: heat exchange, thermal equilibrium, energy conservation
 * - Chart.js: temperature vs time graph
 * - Different materials with specific heat capacities
 */

// -------------------- i18n (AR/EN) --------------------
const SHDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "مختبر السعة الحرارية",
    lesson_title: "مختبر السعة الحرارية: التوازن الحراري والمسعر",
    lesson_lead: "استكشف مفهوم السعة الحرارية النوعية من خلال تجارب المسعر. تعلم كيف تختلف المواد في قدرتها على امتصاص وتخزين الحرارة، وكيف يحدث التوازن الحراري بين الأجسام.",
    theory_title: "المفهوم الأساسي",
    t1: "السعة الحرارية النوعية: كمية الحرارة لرفع درجة حرارة 1 kg بمقدار 1°C",
    t2: "معادلة الحرارة: Q = mcΔT",
    t3: "التوازن الحراري: Q_مفقودة = Q_مكتسبة",
    t4: "المسعر: جهاز لقياس كمية الحرارة المتبادلة",
    t5: "كل مادة لها سعة حرارية نوعية مميزة",
    lab_title: "المعلمات التفاعلية",
    material_title: "نوع المادة",
    material_water: "ماء",
    material_aluminum: "ألومنيوم",
    material_copper: "نحاس",
    material_iron: "حديد",
    mass_label: "الكتلة m (kg)",
    initial_temp_label: "درجة الحرارة الابتدائية T₁ (°C)",
    water_temp_label: "درجة حرارة الماء T₂ (°C)",
    water_mass_label: "كتلة الماء (kg)",
    properties_title: "خصائص المادة",
    specific_heat: "السعة الحرارية النوعية",
    thermal_capacity: "السعة الحرارية",
    reset: "إعادة ضبط",
    start: "بدء التجربة",
    heat_exchange_title: "تبادل الحرارة",
    equilibrium_title: "التوازن الحراري",
    energy_conservation_title: "حفظ الطاقة",
    dash_heat_lost: "Q_مفقودة",
    dash_heat_gained: "Q_مكتسبة",
    dash_final_temp: "T_نهائية",
    dash_time_to_equilibrium: "زمن التوازن",
    dash_energy_balance: "توازن الطاقة",
    dash_efficiency: "الكفاءة",
    balanced: "متوازن",
    unbalanced: "غير متوازن",
    cta_title: "الهدف التعليمي",
    cta_text: "فهم مفهوم السعة الحرارية النوعية وكيف تختلف بين المواد. تطبيق مبدأ حفظ الطاقة في التوازن الحراري واستخدام معادلة Q = mcΔT لحساب كمية الحرارة المتبادلة بين الأجسام.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Specific Heat Lab",
    lesson_title: "Specific Heat Lab: Thermal Equilibrium and Calorimetry",
    lesson_lead: "Explore the concept of specific heat capacity through calorimeter experiments. Learn how materials differ in their ability to absorb and store heat, and how thermal equilibrium occurs between objects.",
    theory_title: "Core Concept",
    t1: "Specific heat capacity: amount of heat to raise 1 kg by 1°C",
    t2: "Heat equation: Q = mcΔT",
    t3: "Thermal equilibrium: Q_lost = Q_gained",
    t4: "Calorimeter: device to measure heat exchange",
    t5: "Each material has a characteristic specific heat capacity",
    lab_title: "Interactive Parameters",
    material_title: "Material Type",
    material_water: "Water",
    material_aluminum: "Aluminum",
    material_copper: "Copper",
    material_iron: "Iron",
    mass_label: "Mass m (kg)",
    initial_temp_label: "Initial temperature T₁ (°C)",
    water_temp_label: "Water temperature T₂ (°C)",
    water_mass_label: "Water mass (kg)",
    properties_title: "Material Properties",
    specific_heat: "Specific heat capacity",
    thermal_capacity: "Thermal capacity",
    reset: "Reset",
    start: "Start Experiment",
    heat_exchange_title: "Heat Exchange",
    equilibrium_title: "Thermal Equilibrium",
    energy_conservation_title: "Energy Conservation",
    dash_heat_lost: "Q_lost",
    dash_heat_gained: "Q_gained",
    dash_final_temp: "T_final",
    dash_time_to_equilibrium: "Equilibrium time",
    dash_energy_balance: "Energy balance",
    dash_efficiency: "Efficiency",
    balanced: "Balanced",
    unbalanced: "Unbalanced",
    cta_title: "Learning Objective",
    cta_text: "Understand the concept of specific heat capacity and how it differs between materials. Apply the principle of energy conservation in thermal equilibrium and use the equation Q = mcΔT to calculate heat exchange between objects.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let shLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applySpecificHeatI18n() {
  const t = SHDICT[shLang];
  document.documentElement.lang = shLang === "ar" ? "ar" : "en";
  document.documentElement.dir = shLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  
  updateEnergyBalanceText();
}

function setupLangToggleSpecificHeat() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      shLang = shLang === "ar" ? "en" : "ar";
      applySpecificHeatI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let shParams = {
  material: "water",
  mass: 0.5,           // kg
  initialTemp: 80,     // °C
  waterTemp: 20,       // °C
  waterMass: 0.3       // kg
};

// Material properties (specific heat in J/kg·°C)
const materials = {
  water: { c: 4186, name_ar: "ماء", name_en: "Water", color: [59, 130, 246] },
  aluminum: { c: 900, name_ar: "ألومنيوم", name_en: "Aluminum", color: [107, 114, 128] },
  copper: { c: 385, name_ar: "نحاس", name_en: "Copper", color: [245, 158, 11] },
  iron: { c: 449, name_ar: "حديد", name_en: "Iron", color: [220, 38, 38] }
};

// -------------------- Simulation State --------------------
let calorimetryState = {
  heatLost: 0,         // J
  heatGained: 0,       // J
  finalTemp: 0,        // °C
  equilibriumTime: 0,  // s
  isBalanced: false,
  efficiency: 100,     // %
  isRunning: false,
  currentTime: 0,
  temperatureData: []
};

// -------------------- p5.js Sketch --------------------
let p5Instance;
let workspace;

function initP5SpecificHeat() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  // Initialize workspace manager
  workspace = initializeWorkspace('specific_heat');

  p5Instance = new p5((p) => {
    p.setup = () => {
      const dimensions = workspace.getCanvasDimensions();
      const canvas = p.createCanvas(dimensions.width, dimensions.height);
      canvas.parent(container);
      calculateHeatExchange();
    };

    p.windowResized = () => {
      const dimensions = workspace.getCanvasDimensions();
      p.resizeCanvas(dimensions.width, dimensions.height);
    };

    p.draw = () => {
      p.background(15, 20, 35);

      // Draw workspace grid
      workspace.drawGrid(p, 0.02);

      // Draw calorimeter
      drawCalorimeter(p);

      // Draw heat exchange visualization
      drawHeatExchange(p);

      // Update simulation
      if (calorimetryState.isRunning) {
        updateHeatExchange();
      }
    };
  });
}

function drawCalorimeter(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2;
  
  // Draw calorimeter container
  p.stroke(107, 114, 128);
  p.strokeWeight(3);
  p.fill(30, 30, 30);
  p.rect(centerX - 80, centerY - 60, 160, 120, 10);
  
  // Draw insulation
  p.stroke(60, 60, 60);
  p.strokeWeight(2);
  p.noFill();
  p.rect(centerX - 85, centerY - 65, 170, 130, 12);
  
  // Draw water
  const waterLevel = centerY + 40;
  p.fill(59, 130, 246, 150);
  p.noStroke();
  p.rect(centerX - 75, waterLevel - 30, 150, 30, 0, 0, 5, 5);
  
  // Draw hot object
  const material = materials[shParams.material];
  p.fill(material.color[0], material.color[1], material.color[2]);
  p.noStroke();
  
  // Object size based on mass
  const objectSize = Math.max(20, Math.min(40, shParams.mass * 40));
  p.rect(centerX - objectSize/2, centerY - 10, objectSize, objectSize/2, 5);
  
  // Draw temperature indicators
  drawTemperatureIndicators(p, centerX, centerY);
  
  // Draw stirrer
  p.stroke(139, 69, 19);
  p.strokeWeight(3);
  p.line(centerX + 60, centerY - 80, centerX + 60, centerY + 20);
  
  // Stirrer handle
  p.fill(139, 69, 19);
  p.noStroke();
  p.circle(centerX + 60, centerY - 85, 8);
}

function drawTemperatureIndicators(p, centerX, centerY) {
  // Hot object temperature
  const hotTemp = calorimetryState.isRunning ? 
    interpolateTemperature(shParams.initialTemp, calorimetryState.finalTemp, calorimetryState.currentTime) :
    shParams.initialTemp;
  
  // Water temperature  
  const waterTemp = calorimetryState.isRunning ?
    interpolateTemperature(shParams.waterTemp, calorimetryState.finalTemp, calorimetryState.currentTime) :
    shParams.waterTemp;
  
  // Hot object thermometer
  p.fill(220, 38, 38);
  p.noStroke();
  p.rect(centerX - 60, centerY - 40, 8, 30, 4);
  p.circle(centerX - 56, centerY - 5, 12);
  
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(10);
  p.text(`${hotTemp.toFixed(0)}°C`, centerX - 56, centerY - 50);
  
  // Water thermometer
  p.fill(59, 130, 246);
  p.noStroke();
  p.rect(centerX + 52, centerY + 10, 8, 30, 4);
  p.circle(centerX + 56, centerY + 45, 12);
  
  p.fill(255);
  p.text(`${waterTemp.toFixed(0)}°C`, centerX + 56, centerY + 55);
}

function interpolateTemperature(startTemp, endTemp, time) {
  const progress = Math.min(time / calorimetryState.equilibriumTime, 1);
  return startTemp + (endTemp - startTemp) * progress;
}

function drawHeatExchange(p) {
  if (!calorimetryState.isRunning) return;
  
  const centerX = p.width / 2;
  const centerY = p.height / 2;
  
  // Draw heat flow arrows
  const time = p.millis() * 0.003;
  
  // Heat leaving hot object
  for (let i = 0; i < 3; i++) {
    const angle = (i * Math.PI * 2 / 3) + time;
    const x1 = centerX + Math.cos(angle) * 25;
    const y1 = centerY + Math.sin(angle) * 15;
    const x2 = centerX + Math.cos(angle) * 40;
    const y2 = centerY + Math.sin(angle) * 25;
    
    p.stroke(245, 158, 11, 150);
    p.strokeWeight(2);
    drawArrow(p, x1, y1, x2, y2);
  }
  
  // Heat entering water
  for (let i = 0; i < 5; i++) {
    const x = centerX - 60 + i * 30;
    const y = centerY + 25 + Math.sin(time + i) * 5;
    
    p.stroke(59, 130, 246, 100);
    p.strokeWeight(1);
    p.line(x, y - 10, x, y + 10);
  }
}

function drawArrow(p, x1, y1, x2, y2) {
  p.line(x1, y1, x2, y2);
  
  const angle = Math.atan2(y2 - y1, x2 - x1);
  const arrowLength = 5;
  
  p.line(x2, y2, 
         x2 - arrowLength * Math.cos(angle - 0.3), 
         y2 - arrowLength * Math.sin(angle - 0.3));
  p.line(x2, y2, 
         x2 - arrowLength * Math.cos(angle + 0.3), 
         y2 - arrowLength * Math.sin(angle + 0.3));
}

function updateHeatExchange() {
  calorimetryState.currentTime += 0.1;
  
  if (calorimetryState.currentTime >= calorimetryState.equilibriumTime) {
    calorimetryState.isRunning = false;
    calorimetryState.currentTime = calorimetryState.equilibriumTime;
  }
  
  updateSpecificHeatDashboard();
  updateTemperatureTimeChart();
}

function calculateHeatExchange() {
  // Calculate final temperature using energy conservation
  // Q_lost = Q_gained
  // m1 * c1 * (T1 - Tf) = m2 * c2 * (Tf - T2)
  
  const m1 = shParams.mass;
  const c1 = materials[shParams.material].c;
  const T1 = shParams.initialTemp;
  
  const m2 = shParams.waterMass;
  const c2 = materials.water.c;
  const T2 = shParams.waterTemp;
  
  // Solve for final temperature
  calorimetryState.finalTemp = (m1 * c1 * T1 + m2 * c2 * T2) / (m1 * c1 + m2 * c2);
  
  // Calculate heat exchange
  calorimetryState.heatLost = m1 * c1 * (T1 - calorimetryState.finalTemp);
  calorimetryState.heatGained = m2 * c2 * (calorimetryState.finalTemp - T2);
  
  // Check energy balance
  const energyDifference = Math.abs(calorimetryState.heatLost - calorimetryState.heatGained);
  calorimetryState.isBalanced = energyDifference < 1; // Within 1 J
  calorimetryState.efficiency = Math.max(0, 100 - (energyDifference / calorimetryState.heatLost) * 100);
  
  // Calculate equilibrium time (simplified model)
  const tempDifference = Math.abs(T1 - T2);
  calorimetryState.equilibriumTime = Math.max(5, tempDifference * 0.2);
  
  updateSpecificHeatDashboard();
  updateTemperatureTimeChart();
}

// -------------------- Controls --------------------
function bindSpecificHeatControls() {
  // Material buttons
  document.querySelectorAll("[data-material]").forEach(btn => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      document.querySelectorAll("[data-material]").forEach(b => b.classList.remove("active"));
      // Add active class to clicked button
      btn.classList.add("active");
      
      shParams.material = btn.getAttribute("data-material");
      updateMaterialProperties();
      calculateHeatExchange();
    });
  });
  
  // Sliders
  const sliders = [
    { id: "mass", param: "mass", output: "massOut", decimals: 1 },
    { id: "initialTemp", param: "initialTemp", output: "initialTempOut", decimals: 0 },
    { id: "waterTemp", param: "waterTemp", output: "waterTempOut", decimals: 0 },
    { id: "waterMass", param: "waterMass", output: "waterMassOut", decimals: 1 }
  ];
  
  sliders.forEach(({ id, param, output, decimals }) => {
    const slider = document.getElementById(id);
    const outputEl = document.getElementById(output);
    
    if (slider && outputEl) {
      slider.addEventListener("input", () => {
        shParams[param] = parseFloat(slider.value);
        outputEl.textContent = shParams[param].toFixed(decimals);
        
        // Ensure initial temp > water temp
        if (param === "initialTemp" && shParams.initialTemp <= shParams.waterTemp) {
          shParams.initialTemp = shParams.waterTemp + 10;
          slider.value = shParams.initialTemp;
          outputEl.textContent = shParams.initialTemp.toFixed(decimals);
        }
        if (param === "waterTemp" && shParams.waterTemp >= shParams.initialTemp) {
          shParams.waterTemp = shParams.initialTemp - 10;
          slider.value = shParams.waterTemp;
          outputEl.textContent = shParams.waterTemp.toFixed(decimals);
        }
        
        updateMaterialProperties();
        calculateHeatExchange();
      });
    }
  });
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", () => {
      shParams = {
        material: "water",
        mass: 0.5,
        initialTemp: 80,
        waterTemp: 20,
        waterMass: 0.3
      };
      
      // Update UI
      document.getElementById("mass").value = 0.5;
      document.getElementById("initialTemp").value = 80;
      document.getElementById("waterTemp").value = 20;
      document.getElementById("waterMass").value = 0.3;
      
      // Update outputs
      document.getElementById("massOut").textContent = "0.5";
      document.getElementById("initialTempOut").textContent = "80";
      document.getElementById("waterTempOut").textContent = "20";
      document.getElementById("waterMassOut").textContent = "0.3";
      
      // Reset material
      document.querySelectorAll("[data-material]").forEach(b => b.classList.remove("active"));
      document.querySelector("[data-material='water']").classList.add("active");
      
      calorimetryState.isRunning = false;
      calorimetryState.currentTime = 0;
      
      updateMaterialProperties();
      calculateHeatExchange();
    });
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      if (!calorimetryState.isRunning) {
        calorimetryState.isRunning = true;
        calorimetryState.currentTime = 0;
        startBtn.textContent = shLang === "ar" ? "إيقاف التجربة" : "Stop Experiment";
      } else {
        calorimetryState.isRunning = false;
        startBtn.textContent = shLang === "ar" ? "بدء التجربة" : "Start Experiment";
      }
    });
  }
}

function updateMaterialProperties() {
  const material = materials[shParams.material];
  const specificHeatEl = document.getElementById("specificHeatValue");
  const thermalCapacityEl = document.getElementById("thermalCapacity");
  
  if (specificHeatEl) specificHeatEl.textContent = material.c.toFixed(0);
  if (thermalCapacityEl) {
    const thermalCapacity = shParams.mass * material.c;
    thermalCapacityEl.textContent = thermalCapacity.toFixed(0);
  }
}

function updateEnergyBalanceText() {
  const el = document.getElementById("dashEnergyBalance");
  if (el) {
    const t = SHDICT[shLang];
    el.textContent = calorimetryState.isBalanced ? t.balanced : t.unbalanced;
    el.className = calorimetryState.isBalanced ? "balanced" : "unbalanced";
  }
}

function updateSpecificHeatDashboard() {
  // Heat exchange values
  const heatLostEl = document.getElementById("dashHeatLost");
  const heatGainedEl = document.getElementById("dashHeatGained");
  
  if (heatLostEl) heatLostEl.textContent = calorimetryState.heatLost.toFixed(0);
  if (heatGainedEl) heatGainedEl.textContent = calorimetryState.heatGained.toFixed(0);
  
  // Equilibrium values
  const finalTempEl = document.getElementById("dashFinalTemp");
  const equilibriumTimeEl = document.getElementById("dashEquilibriumTime");
  
  if (finalTempEl) finalTempEl.textContent = calorimetryState.finalTemp.toFixed(1);
  if (equilibriumTimeEl) equilibriumTimeEl.textContent = calorimetryState.equilibriumTime.toFixed(1);
  
  // Energy conservation
  const efficiencyEl = document.getElementById("dashEfficiency");
  
  if (efficiencyEl) efficiencyEl.textContent = calorimetryState.efficiency.toFixed(0);
  
  updateEnergyBalanceText();
}

// -------------------- Chart --------------------
let temperatureTimeChart;

function initTemperatureTimeChart() {
  const ctx = document.getElementById("temperatureTimeChart");
  if (!ctx) return;
  
  temperatureTimeChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Hot Object',
          data: [],
          borderColor: '#dc2626',
          backgroundColor: 'rgba(220, 38, 38, 0.1)',
          tension: 0.4
        },
        {
          label: 'Water',
          data: [],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          title: { display: true, text: 'Time (s)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Temperature (°C)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          labels: { color: '#a9b4d0' }
        }
      }
    }
  });
}

function updateTemperatureTimeChart() {
  if (!temperatureTimeChart) return;
  
  // Generate temperature curves
  const data = [];
  const labels = [];
  
  const timeStep = 0.5;
  const maxTime = calorimetryState.equilibriumTime;
  
  for (let t = 0; t <= maxTime; t += timeStep) {
    labels.push(t.toFixed(1));
    
    const hotTemp = interpolateTemperature(shParams.initialTemp, calorimetryState.finalTemp, t);
    const waterTemp = interpolateTemperature(shParams.waterTemp, calorimetryState.finalTemp, t);
    
    data.push({ hot: hotTemp, water: waterTemp });
  }
  
  temperatureTimeChart.data.labels = labels;
  temperatureTimeChart.data.datasets[0].data = data.map(d => d.hot);
  temperatureTimeChart.data.datasets[1].data = data.map(d => d.water);
  
  temperatureTimeChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleSpecificHeat();
  applySpecificHeatI18n();
  bindSpecificHeatControls();
  initP5SpecificHeat();
  initTemperatureTimeChart();
  updateMaterialProperties();
  calculateHeatExchange();
});
