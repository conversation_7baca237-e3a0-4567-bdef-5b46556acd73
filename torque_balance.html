<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | محاكي العزم والتوازن</title>
  <link rel="stylesheet" href="styles/torque_balance.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="collision_lab.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">محاكي العزم والتوازن</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">محاكي العزم والتوازن: الاتزان الدوراني</h1>
      <p class="lead" data-i18n="lesson_lead">
        اكتشف شروط الاتزان الدوراني من خلال وضع أثقال مختلفة على مسافات متنوعة من نقطة الارتكاز. تعلم كيف يعتمد العزم على القوة والمسافة وشاهد متى تتوازن العارضة.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">العزم: τ = r × F (المسافة × القوة)</li>
            <li data-i18n="t2">اتجاه العزم: مع عقارب الساعة (+) أو عكسها (-)</li>
            <li data-i18n="t3">شرط التوازن الدوراني: Στ = 0</li>
            <li data-i18n="t4">العزم مع عقارب الساعة = العزم عكس عقارب الساعة</li>
            <li data-i18n="t5">وحدة العزم: نيوتن⋅متر (N⋅m)</li>
          </ul>
          <div class="formula">Στ_clockwise = Στ_counterclockwise</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot beam"></i> Beam</span>
              <span class="legend-item"><i class="dot fulcrum"></i> Fulcrum</span>
              <span class="legend-item"><i class="dot weight"></i> Weight</span>
              <span class="legend-item"><i class="dot torque"></i> Torque</span>
            </div>
            <div id="p5-container" class="p5-container" aria-label="Torque and balance simulation"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="weights-section">
            <h3 data-i18n="weights_title">الأثقال</h3>
            
            <div class="weight-control" data-weight="1">
              <h4 data-i18n="weight1_title">الثقل الأول</h4>
              <div class="field">
                <label for="mass1" data-i18n="mass_label">الكتلة (kg)</label>
                <input type="range" id="mass1" min="0" max="10" step="0.5" value="2"/>
                <output id="mass1Out">2.0</output>
              </div>
              <div class="field">
                <label for="position1" data-i18n="position_label">الموضع (cm)</label>
                <input type="range" id="position1" min="-50" max="50" step="5" value="-20"/>
                <output id="position1Out">-20</output>
              </div>
            </div>
            
            <div class="weight-control" data-weight="2">
              <h4 data-i18n="weight2_title">الثقل الثاني</h4>
              <div class="field">
                <label for="mass2" data-i18n="mass_label">الكتلة (kg)</label>
                <input type="range" id="mass2" min="0" max="10" step="0.5" value="4"/>
                <output id="mass2Out">4.0</output>
              </div>
              <div class="field">
                <label for="position2" data-i18n="position_label">الموضع (cm)</label>
                <input type="range" id="position2" min="-50" max="50" step="5" value="10"/>
                <output id="position2Out">10</output>
              </div>
            </div>
            
            <div class="weight-control" data-weight="3">
              <h4 data-i18n="weight3_title">الثقل الثالث</h4>
              <div class="field">
                <label for="mass3" data-i18n="mass_label">الكتلة (kg)</label>
                <input type="range" id="mass3" min="0" max="10" step="0.5" value="0"/>
                <output id="mass3Out">0.0</output>
              </div>
              <div class="field">
                <label for="position3" data-i18n="position_label">الموضع (cm)</label>
                <input type="range" id="position3" min="-50" max="50" step="5" value="0"/>
                <output id="position3Out">0</output>
              </div>
            </div>
          </div>
          
          <div class="field">
            <label for="gravity" data-i18n="gravity_label">الجاذبية g (m/s²)</label>
            <input type="range" id="gravity" min="5" max="15" step="0.1" value="9.8"/>
            <output id="gravityOut">9.8</output>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="balance" class="btn" data-i18n="auto_balance">توازن تلقائي</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="torques_title">العزوم</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_torque1">τ₁</span> = <span id="dashTorque1">0.0</span> N⋅m
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_torque2">τ₂</span> = <span id="dashTorque2">0.0</span> N⋅m
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_torque3">τ₃</span> = <span id="dashTorque3">0.0</span> N⋅m
                </div>
              </div>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_net_torque">Στ</span> = <span id="dashNetTorque">0.0</span> N⋅m
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_balance">التوازن</span>: <span id="dashBalance">غير متوازن</span>
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="forces_title">القوى</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_weight1">W₁</span> = <span id="dashWeight1">19.6</span> N
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_weight2">W₂</span> = <span id="dashWeight2">39.2</span> N
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_weight3">W₃</span> = <span id="dashWeight3">0.0</span> N
                </div>
              </div>
            </div>
            
            <canvas id="torqueChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        اكتشاف شرط التوازن الدوراني: أن يكون مجموع العزوم مع عقارب الساعة مساوياً لمجموع العزوم عكس عقارب الساعة. فهم أن العزم يعتمد على كل من القوة والمسافة من نقطة الارتكاز.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/torque_balance.js"></script>
</body>
</html>
