<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>قوانين نيوتن للحركة | فيزيكس</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles/workspace-config.css">
  <link rel="stylesheet" href="styles/newton_laws.css">
  <script src="scripts/workspace-config.js"></script>
</head>
<body class="bg-gray-50">
  <header class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-4 shadow-md">
    <div class="container mx-auto px-4 flex justify-between items-center">
      <div class="flex items-center">
        <i class="fas fa-balance-scale-right text-2xl mr-3"></i>
        <h1 class="text-2xl font-bold">قوانين نيوتن للحركة</h1>
      </div>
      <nav class="flex items-center gap-4">
        <a href="index.html" class="hover:text-emerald-100">العودة للرئيسية</a>
        <button id="darkModeToggle" class="bg-black/20 px-3 py-1.5 rounded">الوضع الليلي</button>
      </nav>
    </div>
  </header>

  <main class="container mx-auto px-4 py-6">
    <section class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2 simulation-window bg-white rounded-xl shadow p-4">
        <div class="flex justify-between items-center mb-3">
          <h2 class="text-xl font-semibold">كتلة على سطح مائل مع احتكاك وقوة خارجية</h2>
          <button id="resetBtn" class="text-emerald-600 text-sm flex items-center gap-1">
            <i class="fas fa-redo"></i><span>إعادة تعيين</span>
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="mass">الكتلة m (كغ)</label>
            <input id="mass" type="range" min="0.5" max="10" value="2" step="0.5" class="w-full" title="الكتلة">
            <div class="text-xs text-gray-500 mt-1"><span id="massVal">2.0</span> kg</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="theta">زاوية الميل θ (°)</label>
            <input id="theta" type="range" min="0" max="60" value="20" step="1" class="w-full" title="زاوية الميل">
            <div class="text-xs text-gray-500 mt-1"><span id="thetaVal">20</span> deg</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="mu">معامل الاحتكاك μ</label>
            <input id="mu" type="range" min="0" max="0.8" value="0.2" step="0.05" class="w-full" title="معامل الاحتكاك">
            <div class="text-xs text-gray-500 mt-1">μ = <span id="muVal">0.20</span></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="Fext">قوة خارجية F (نيوتن)</label>
            <input id="Fext" type="range" min="-50" max="50" value="0" step="1" class="w-full" title="قوة خارجية">
            <div class="text-xs text-gray-500 mt-1"><span id="FextVal">0</span> N</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="dt">Δt (ثانية)</label>
            <input id="dt" type="range" min="0.02" max="0.2" value="0.05" step="0.01" class="w-full" title="خطوة الزمن">
            <div class="text-xs text-gray-500 mt-1"><span id="dtVal">0.05</span> s</div>
          </div>
          <div class="flex items-end">
            <button id="playPause" class="bg-emerald-600 text-white px-3 py-2 rounded text-sm mr-2">تشغيل</button>
            <button id="stepBtn" class="bg-white border px-3 py-2 rounded text-sm">خطوة +0.1 ث</button>
          </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">مشهد السطح المائل والقوى</div>
            <canvas id="canvasIncline" class="w-full h-64"></canvas>
          </div>
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">السرعة والإزاحة على المنحدر</div>
            <canvas id="canvasPlots" class="w-full h-64"></canvas>
          </div>
        </div>
      </div>

      <aside class="bg-white rounded-xl shadow p-4">
        <h3 class="text-lg font-semibold mb-3">النتائج</h3>
        <ul class="space-y-2 text-sm text-gray-700">
          <li>حالة الحركة: <span id="stateText">ساكن</span></li>
          <li>العجلة على المنحدر a∥: <span id="accVal">0.00</span> m/s²</li>
          <li>السرعة v: <span id="velVal">0.00</span> m/s</li>
          <li>الإزاحة s: <span id="dispVal">0.00</span> m</li>
          <li>قوة الاحتكاك f: <span id="fricVal">0.00</span> N</li>
        </ul>
        <div class="mt-4 text-xs text-gray-600 leading-6">
          على سطح مائل بزاوية θ: المركّبة الموازية لوزن الجسم mg sinθ، والقوة العمودية N = mg cosθ.
          الاحتكاك الأقصى الساكن f_s,max = μ_s N. إذا ∣mg sinθ - F∥∣ ≤ f_s,max لا ينزلق الجسم، وإلا يتحرك بتسارع
          a = (mg sinθ - f_k - F∥)/m مع f_k = μ_k N (نبسّط μ_s = μ_k = μ هنا).
        </div>
        <div class="mt-4">
          <a href="index.html" class="text-emerald-600 text-sm">عودة إلى المنصة الرئيسية</a>
        </div>
      </aside>
    </section>
  </main>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" defer></script>
  <script src="scripts/newton_laws.js"></script>
</body>
</html>
