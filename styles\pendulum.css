:root{
  --bg:#0f1220;
  --panel:#151a2e;
  --muted:#8aa1b4;
  --text:#e6eef6;
  --primary:#3da9fc;
  --primary-700:#2486cc;
  --danger:#ef476f;
  --success:#2ec4b6;
  --accent:#ffd166;
  --card:#1b2240;
  --grid:#223055;
  --shadow:0 10px 24px rgba(0,0,0,.35);
  --radius:14px;
  --radius-sm:10px;
  --ring:0 0 0 3px rgba(61,169,252,.25);
  --font:'Cairo', system-ui, -apple-system, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Noto <PERSON>", sans-serif;
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family:var(--font);
  background:radial-gradient(1200px 600px at 80% -10%, #1d2447 0%, rgba(29,36,71,0) 70%), var(--bg);
  color:var(--text);
}

.app-header{
  position:sticky; top:0; z-index:10;
  background:rgba(15,18,32,.75);
  -webkit-backdrop-filter:saturate(140%) blur(10px);
  backdrop-filter:saturate(140%) blur(10px);
  border-bottom:1px solid rgba(255,255,255,.06);
}
.app-header .brand{
  display:flex; align-items:center; gap:.75rem;
  padding:12px 20px 6px;
}
.logo{
  text-decoration:none; color:var(--text);
  font-weight:700; letter-spacing:.3px; font-size:1.15rem;
  padding:.2rem .55rem; border-radius:10px;
  background:linear-gradient(135deg, #223055 0%, #18203f 100%);
  border:1px solid rgba(255,255,255,.06);
  box-shadow:inset 0 1px 0 rgba(255,255,255,.06), var(--shadow);
}
.divider{width:1px; height:18px; background:rgba(255,255,255,.12)}
.page-title{color:var(--muted); font-weight:600}

.nav{
  display:flex; gap:.4rem; padding:0 20px 12px; flex-wrap:wrap;
}
.nav a{
  color:var(--muted); text-decoration:none; font-weight:600;
  padding:.45rem .8rem; border-radius:999px;
  border:1px solid rgba(255,255,255,.08);
  background:linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
}
.nav a:hover{color:var(--text); border-color:rgba(255,255,255,.14)}
.nav a.active{color:#081019; background:linear-gradient(180deg, #49b5ff, #2b8fe0); border-color:transparent}

.container{
  max-width:1200px; margin:22px auto; padding:0 16px;
  display:grid; gap:16px;
  grid-template-columns: 1fr;
}
@media (min-width: 1000px){
  .container{
    grid-template-columns: 380px 1fr;
    grid-template-areas:
      "controls scene"
      "controls charts"
      "theory theory";
  }
  .controls{grid-area:controls}
  .scene{grid-area:scene}
  .charts{grid-area:charts}
  .theory{grid-area:theory}
}

.panel{
  background:linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
  border:1px solid rgba(255,255,255,.08);
  border-radius:var(--radius);
  box-shadow:var(--shadow);
  padding:16px;
}
.panel h2{
  margin:0 0 12px; font-size:1.05rem;
  color:#cfe6ff; letter-spacing:.2px;
}

.controls .grid{
  display:grid; gap:10px;
}
.field{
  display:grid; gap:6px;
  background:var(--card);
  border:1px solid rgba(255,255,255,.06);
  padding:10px; border-radius:var(--radius-sm);
}
.field span{font-size:.95rem; color:#c8d6e5}
.field input[type="range"]{width:100%}
.field input[type="number"]{
  width:100%; background:#0e142a; color:var(--text);
  border:1px solid rgba(255,255,255,.08);
  padding:.5rem .6rem; border-radius:10px;
}
.field input[type="number"]:focus{outline:none; box-shadow:var(--ring); border-color:#4aa9ff}

.actions{display:flex; gap:8px; flex-wrap:wrap; margin-top:8px}
.btn{
  appearance:none; border:none; cursor:pointer;
  padding:.55rem .9rem; border-radius:12px; font-weight:700;
  color:var(--text);
  background:linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
  border:1px solid rgba(255,255,255,.08);
}
.btn:hover{border-color:rgba(255,255,255,.16)}
.btn.primary{color:#081019; background:linear-gradient(180deg, #49b5ff, #2b8fe0); border-color:#277ac6}
.btn.primary:hover{filter:brightness(1.04)}
.btn.danger{background:linear-gradient(180deg, #ff6b8a, #ef476f); border-color:#d53a5c}
.btn.danger:hover{filter:brightness(1.05)}

.info{
  margin-top:10px; display:grid; gap:6px;
  color:#c9d7e7; font-size:.95rem;
}
.info strong{color:#fff}

.scene{display:flex; flex-direction:column; gap:8px}
#pendulumCanvas,#angleCanvas,#phaseCanvas{
  width:100%; height:auto; display:block;
  background:
    linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.015)),
    repeating-linear-gradient(0deg, transparent, transparent 22px, rgba(255,255,255,.05) 22px, rgba(255,255,255,.05) 23px),
    repeating-linear-gradient(90deg, transparent, transparent 22px, rgba(255,255,255,.05) 22px, rgba(255,255,255,.05) 23px),
    #0d1326;
  border:1px solid rgba(255,255,255,.08);
  border-radius:12px;
}

.charts .charts-grid{
  display:grid; gap:10px;
}
@media (min-width: 700px){
  .charts .charts-grid{grid-template-columns: 1fr 1fr}
}

.theory p{
  margin:0; line-height:1.9; color:#d7e4f3;
}

.app-footer{
  max-width:1200px; margin:10px auto 24px; padding:0 16px;
  display:flex; justify-content:center; color:var(--muted); font-size:.95rem;
}
