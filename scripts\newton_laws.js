// Newton's Laws - Inclined Plane with Friction and External Force
(function () {
  const $ = (id) => document.getElementById(id);

  // Inputs
  const massEl = $('mass');
  const thetaEl = $('theta');
  const muEl = $('mu');
  const FextEl = $('Fext');
  const dtEl = $('dt');
  const playPauseBtn = $('playPause');
  const stepBtn = $('stepBtn');
  const resetBtn = $('resetBtn');

  // Output fields
  const massVal = $('massVal');
  const thetaVal = $('thetaVal');
  const muVal = $('muVal');
  const FextVal = $('FextVal');
  const dtVal = $('dtVal');

  const stateText = $('stateText');
  const accVal = $('accVal');
  const velVal = $('velVal');
  const dispVal = $('dispVal');
  const fricVal = $('fricVal');

  // Canvases
  const canvasIncline = $('canvasIncline');
  const ctxIncline = canvasIncline.getContext('2d');
  const canvasPlots = $('canvasPlots');
  const ctxPlots = canvasPlots.getContext('2d');

  // Utilities: HiDPI scaling
  function setupHiDPICanvas(canvas) {
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    const w = canvas.clientWidth || canvas.offsetWidth;
    const h = canvas.clientHeight || canvas.offsetHeight;
    if (!w || !h) return ratio;
    canvas.width = Math.floor(w * ratio);
    canvas.height = Math.floor(h * ratio);
    const ctx = canvas.getContext('2d');
    ctx.setTransform(ratio, 0, 0, ratio, 0, 0);
    return ratio;
  }

  function resizeAll() {
    setupHiDPICanvas(canvasIncline);
    setupHiDPICanvas(canvasPlots);
    redrawAll();
  }

  let resizeTid;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTid);
    resizeTid = setTimeout(resizeAll, 150);
  });

  // Physics state
  let running = false;
  const g = 9.81; // m/s^2
  let m = parseFloat(massEl.value);
  let thetaDeg = parseFloat(thetaEl.value);
  let mu = parseFloat(muEl.value);
  let Fext = parseFloat(FextEl.value);
  let dt = parseFloat(dtEl.value);

  // On-ramp kinematics (1D along the incline)
  let s = 0; // displacement along incline (m)
  let v = 0; // velocity along incline (m/s)
  let a = 0; // acceleration along incline (m/s^2)
  let friction = 0;
  let isStatic = true; // whether it's stuck (static friction holds)

  // Plot samples
  const maxTime = 20;
  let t = 0;
  let samples = []; // {t, v, s}

  // Helpers
  function toRad(deg) { return (deg * Math.PI) / 180; }

  function computeDynamics() {
    const theta = toRad(thetaDeg);
    const mg = m * g;
    const mgParallel = mg * Math.sin(theta); // down the slope
    const N = mg * Math.cos(theta);
    const F_parallel = -Fext; // assume +Fext acts uphill; positive uphill
    // Using sign convention: uphill positive.
    // Weight parallel component acts downhill => negative in our sign
    const W_parallel = -mgParallel;

    // Try static friction first
    const netWithoutFric = W_parallel + F_parallel; // what remains without friction
    const f_s_max = mu * N;

    if (Math.abs(netWithoutFric) <= f_s_max && Math.abs(v) < 1e-6) {
      // static equilibrium
      isStatic = true;
      friction = -netWithoutFric; // balances it
      a = 0;
    } else {
      // kinetic motion
      isStatic = false;
      const dir = netWithoutFric >= 0 ? 1 : -1; // direction of impending/actual motion
      const f_k = mu * N * dir; // opposes motion: sign opposite velocity/netWithoutFric
      friction = -f_k;
      const net = W_parallel + F_parallel + friction;
      a = net / m;
    }
  }

  function step(simDt) {
    computeDynamics();
    if (!isStatic) {
      v += a * simDt;
      s += v * simDt;
    } else {
      // stuck, v = 0
      v = 0;
    }
    t += simDt;
    if (t > maxTime) {
      t = 0;
      samples = [];
    }
    samples.push({ t, v, s });
    updateOutputs();
    redrawAll();
  }

  function updateOutputs() {
    stateText.textContent = isStatic ? 'ساكن' : 'متحرك';
    accVal.textContent = a.toFixed(2);
    velVal.textContent = v.toFixed(2);
    dispVal.textContent = s.toFixed(2);
    fricVal.textContent = friction.toFixed(2);

    massVal.textContent = m.toFixed(1);
    thetaVal.textContent = thetaDeg.toFixed(0);
    muVal.textContent = mu.toFixed(2);
    FextVal.textContent = Fext.toFixed(0);
    dtVal.textContent = dt.toFixed(2);
  }

  function reset() {
    running = false;
    t = 0;
    s = 0;
    v = 0;
    a = 0;
    friction = 0;
    isStatic = true;
    samples = [];
    updateOutputs();
    redrawAll();
  }

  let rafId = null;
  function loop() {
    if (!running) return;
    step(dt);
    rafId = requestAnimationFrame(loop);
  }

  // Drawing
  function clear(ctx, canvas) {
    ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight);
  }

  function drawIncline() {
    clear(ctxIncline, canvasIncline);

    const w = canvasIncline.clientWidth;
    const h = canvasIncline.clientHeight;
    const cx = w * 0.2;
    const cy = h * 0.8;
    const L = w * 0.6; // ramp length
    const theta = toRad(thetaDeg);

    // Ramp line
    const x2 = cx + L * Math.cos(theta);
    const y2 = cy - L * Math.sin(theta);
    ctxIncline.strokeStyle = '#9CA3AF';
    ctxIncline.lineWidth = 3;
    ctxIncline.beginPath();
    ctxIncline.moveTo(cx, cy);
    ctxIncline.lineTo(x2, y2);
    ctxIncline.stroke();

    // Block position along ramp
    // Map s meters to pixels (simple scale)
    const pxPerMeter = 30;
    let sPx = s * pxPerMeter;
    // Clamp block within ramp visually
    sPx = Math.max(0, Math.min(L - 40, sPx));
    const bx = cx + sPx * Math.cos(theta);
    const by = cy - sPx * Math.sin(theta);

    // Draw block
    const bw = 40, bh = 24;
    ctxIncline.save();
    ctxIncline.translate(bx, by);
    ctxIncline.rotate(-theta);
    ctxIncline.fillStyle = '#059669';
    ctxIncline.fillRect(-bw/2, -bh/2, bw, bh);
    ctxIncline.restore();

    // Draw forces arrows at block
    function drawArrow(fromX, fromY, toX, toY, color='#EF4444') {
      ctxIncline.strokeStyle = color;
      ctxIncline.fillStyle = color;
      ctxIncline.lineWidth = 2;
      ctxIncline.beginPath();
      ctxIncline.moveTo(fromX, fromY);
      ctxIncline.lineTo(toX, toY);
      ctxIncline.stroke();
      // head
      const angle = Math.atan2(toY - fromY, toX - fromX);
      const size = 6;
      ctxIncline.beginPath();
      ctxIncline.moveTo(toX, toY);
      ctxIncline.lineTo(toX - size * Math.cos(angle - Math.PI/6), toY - size * Math.sin(angle - Math.PI/6));
      ctxIncline.lineTo(toX - size * Math.cos(angle + Math.PI/6), toY - size * Math.sin(angle + Math.PI/6));
      ctxIncline.closePath();
      ctxIncline.fill();
    }

    // Origin of arrows slightly above block
    const ax = bx;
    const ay = by - 10;

    // Weight vector (downward)
    drawArrow(ax, ay, ax, ay + 40, '#DC2626');

    // Normal (perpendicular to ramp)
    const nx = ax - 0 * Math.sin(theta);
    const ny = ay - 0 * Math.cos(theta);
    const Nlen = 30;
    drawArrow(ax, ay, ax - Nlen * Math.sin(theta), ay - Nlen * Math.cos(theta), '#2563EB');

    // Friction along ramp (opposes motion or potential motion)
    const fricSign = Math.sign(friction) || 0;
    if (fricSign !== 0) {
      const Flen = 30 * Math.min(1, Math.abs(friction) / (m * g)); // scaled
      drawArrow(ax, ay, ax + Flen * Math.cos(theta) * Math.sign(friction), ay - Flen * Math.sin(theta) * Math.sign(friction), '#10B981');
    }

    // External force along ramp (positive uphill)
    if (Math.abs(Fext) > 1e-6) {
      const Elen = 30 * Math.min(1, Math.abs(Fext) / (m * g));
      const sign = Fext >= 0 ? 1 : -1;
      drawArrow(ax, ay, ax + Elen * Math.cos(theta) * sign, ay - Elen * Math.sin(theta) * sign, '#F59E0B');
    }

    // Label angle
    ctxIncline.fillStyle = '#111827';
    ctxIncline.font = '12px Tajawal, Arial';
    ctxIncline.fillText(`θ = ${thetaDeg.toFixed(0)}°`, cx + 10, cy - 8);
  }

  function drawPlots() {
    clear(ctxPlots, canvasPlots);
    const w = canvasPlots.clientWidth;
    const h = canvasPlots.clientHeight;

    // Grid
    ctxPlots.strokeStyle = '#E5E7EB';
    ctxPlots.lineWidth = 1;
    for (let x = 0; x < w; x += 40) {
      ctxPlots.beginPath(); ctxPlots.moveTo(x, 0); ctxPlots.lineTo(x, h); ctxPlots.stroke();
    }
    for (let y = 0; y < h; y += 40) {
      ctxPlots.beginPath(); ctxPlots.moveTo(0, y); ctxPlots.lineTo(w, y); ctxPlots.stroke();
    }

    // Axes
    ctxPlots.strokeStyle = '#9CA3AF';
    ctxPlots.lineWidth = 1.5;
    ctxPlots.beginPath(); ctxPlots.moveTo(40, 0); ctxPlots.lineTo(40, h - 20); ctxPlots.stroke();
    ctxPlots.beginPath(); ctxPlots.moveTo(40, h - 20); ctxPlots.lineTo(w - 10, h - 20); ctxPlots.stroke();

    // v(t)
    const left = 40, bottom = h - 20, right = w - 10, top = 10;
    const plotW = right - left, plotH = bottom - top;
    const tMax = maxTime;
    const vScale = 15; // px per (m/s)
    const sScale = 15; // px per (m)

    // velocity line (blue)
    ctxPlots.strokeStyle = '#2563EB';
    ctxPlots.lineWidth = 2;
    ctxPlots.beginPath();
    samples.forEach((pt, i) => {
      const x = left + (pt.t / tMax) * plotW;
      const y = bottom - pt.v * vScale;
      if (i === 0) ctxPlots.moveTo(x, y); else ctxPlots.lineTo(x, y);
    });
    ctxPlots.stroke();

    // displacement line (purple)
    ctxPlots.strokeStyle = '#7C3AED';
    ctxPlots.lineWidth = 2;
    ctxPlots.beginPath();
    samples.forEach((pt, i) => {
      const x = left + (pt.t / tMax) * plotW;
      const y = bottom - pt.s * sScale;
      if (i === 0) ctxPlots.moveTo(x, y); else ctxPlots.lineTo(x, y);
    });
    ctxPlots.stroke();

    // Legends
    ctxPlots.fillStyle = '#111827';
    ctxPlots.font = '12px Tajawal, Arial';
    ctxPlots.fillText('v(t)', left + 6, top + 14);
    ctxPlots.fillStyle = '#7C3AED';
    ctxPlots.fillText('s(t)', left + 36, top + 14);
  }

  function redrawAll() {
    drawIncline();
    drawPlots();
  }

  // Initialize
  setupHiDPICanvas(canvasIncline);
  setupHiDPICanvas(canvasPlots);
  computeDynamics();
  updateOutputs();
  redrawAll();

  // Events
  massEl.addEventListener('input', () => {
    m = parseFloat(massEl.value);
    massVal.textContent = m.toFixed(1);
    if (!running) { computeDynamics(); updateOutputs(); redrawAll(); }
  });
  thetaEl.addEventListener('input', () => {
    thetaDeg = parseFloat(thetaEl.value);
    thetaVal.textContent = thetaDeg.toFixed(0);
    if (!running) { computeDynamics(); updateOutputs(); redrawAll(); }
  });
  muEl.addEventListener('input', () => {
    mu = parseFloat(muEl.value);
    muVal.textContent = mu.toFixed(2);
    if (!running) { computeDynamics(); updateOutputs(); redrawAll(); }
  });
  FextEl.addEventListener('input', () => {
    Fext = parseFloat(FextEl.value);
    FextVal.textContent = Fext.toFixed(0);
    if (!running) { computeDynamics(); updateOutputs(); redrawAll(); }
  });
  dtEl.addEventListener('input', () => {
    dt = parseFloat(dtEl.value);
    dtVal.textContent = dt.toFixed(2);
  });

  playPauseBtn.addEventListener('click', () => {
    running = !running;
    playPauseBtn.textContent = running ? 'إيقاف' : 'تشغيل';
    if (running) {
      loop();
    } else if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  });

  stepBtn.addEventListener('click', () => {
    step(0.1);
  });

  resetBtn.addEventListener('click', () => {
    reset();
  });

  // Accessibility titles to satisfy linters
  massEl.title = 'الكتلة m';
  thetaEl.title = 'زاوية الميل θ';
  muEl.title = 'معامل الاحتكاك μ';
  FextEl.title = 'قوة خارجية F';
  dtEl.title = 'خطوة الزمن Δt';
})();
