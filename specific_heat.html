<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | مختبر السعة الحرارية</title>
  <link rel="stylesheet" href="styles/specific_heat.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="states_of_matter.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">مختبر السعة الحرارية</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">مختبر السعة الحرارية: التوازن الحراري والمسعر</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف مفهوم السعة الحرارية النوعية من خلال تجارب المسعر. تعلم كيف تختلف المواد في قدرتها على امتصاص وتخزين الحرارة، وكيف يحدث التوازن الحراري بين الأجسام.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">السعة الحرارية النوعية: كمية الحرارة لرفع درجة حرارة 1 kg بمقدار 1°C</li>
            <li data-i18n="t2">معادلة الحرارة: Q = mcΔT</li>
            <li data-i18n="t3">التوازن الحراري: Q_مفقودة = Q_مكتسبة</li>
            <li data-i18n="t4">المسعر: جهاز لقياس كمية الحرارة المتبادلة</li>
            <li data-i18n="t5">كل مادة لها سعة حرارية نوعية مميزة</li>
          </ul>
          <div class="formula">Q = mcΔT</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot hot-object"></i> Hot Object</span>
              <span class="legend-item"><i class="dot cold-object"></i> Cold Object</span>
              <span class="legend-item"><i class="dot calorimeter"></i> Calorimeter</span>
              <span class="legend-item"><i class="dot heat-flow"></i> Heat Flow</span>
            </div>
            <div id="p5-container" class="p5-container" aria-label="Specific heat simulation"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="material-selector">
            <h3 data-i18n="material_title">نوع المادة</h3>
            <div class="material-buttons">
              <button class="btn material active" data-material="water" data-i18n="material_water">ماء</button>
              <button class="btn material" data-material="aluminum" data-i18n="material_aluminum">ألومنيوم</button>
              <button class="btn material" data-material="copper" data-i18n="material_copper">نحاس</button>
              <button class="btn material" data-material="iron" data-i18n="material_iron">حديد</button>
            </div>
          </div>
          
          <div class="field">
            <label for="mass" data-i18n="mass_label">الكتلة m (kg)</label>
            <input type="range" id="mass" min="0.1" max="2" step="0.1" value="0.5"/>
            <output id="massOut">0.5</output>
          </div>
          
          <div class="field">
            <label for="initialTemp" data-i18n="initial_temp_label">درجة الحرارة الابتدائية T₁ (°C)</label>
            <input type="range" id="initialTemp" min="20" max="100" step="5" value="80"/>
            <output id="initialTempOut">80</output>
          </div>
          
          <div class="field">
            <label for="waterTemp" data-i18n="water_temp_label">درجة حرارة الماء T₂ (°C)</label>
            <input type="range" id="waterTemp" min="10" max="50" step="2" value="20"/>
            <output id="waterTempOut">20</output>
          </div>
          
          <div class="field">
            <label for="waterMass" data-i18n="water_mass_label">كتلة الماء (kg)</label>
            <input type="range" id="waterMass" min="0.2" max="1" step="0.1" value="0.3"/>
            <output id="waterMassOut">0.3</output>
          </div>
          
          <div class="material-properties">
            <h3 data-i18n="properties_title">خصائص المادة</h3>
            <div class="property-info">
              <div class="property-item">
                <span data-i18n="specific_heat">السعة الحرارية النوعية</span>: <span id="specificHeatValue">4186</span> J/kg·°C
              </div>
              <div class="property-item">
                <span data-i18n="thermal_capacity">السعة الحرارية</span>: <span id="thermalCapacity">2093</span> J/°C
              </div>
            </div>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="start" class="btn" data-i18n="start">بدء التجربة</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="heat_exchange_title">تبادل الحرارة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_heat_lost">Q_مفقودة</span> = <span id="dashHeatLost">0</span> J
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_heat_gained">Q_مكتسبة</span> = <span id="dashHeatGained">0</span> J
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="equilibrium_title">التوازن الحراري</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_final_temp">T_نهائية</span> = <span id="dashFinalTemp">--</span> °C
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_time_to_equilibrium">زمن التوازن</span> = <span id="dashEquilibriumTime">--</span> s
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="energy_conservation_title">حفظ الطاقة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_energy_balance">توازن الطاقة</span>: <span id="dashEnergyBalance">متوازن</span>
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_efficiency">الكفاءة</span>: <span id="dashEfficiency">100</span>%
                </div>
              </div>
            </div>
            
            <canvas id="temperatureTimeChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        فهم مفهوم السعة الحرارية النوعية وكيف تختلف بين المواد. تطبيق مبدأ حفظ الطاقة في التوازن الحراري واستخدام معادلة Q = mcΔT لحساب كمية الحرارة المتبادلة بين الأجسام.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/specific_heat.js"></script>
</body>
</html>
