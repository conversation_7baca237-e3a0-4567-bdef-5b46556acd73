/**
 * PhysicsHub - Uniform Circular Motion
 * Author & Copyright:
 *   Dr. <PERSON>, SUST - BME, @ 2025.
 * Contact:
 *   <EMAIL>, Phone: +249912867327, +966538076790
 *
 * Features:
 * - i18n AR/EN with direction switching and dynamic text replacement
 * - Centripetal force calculator with sliders, instant result, steps, presets
 * - p5.js orbit simulator with live dashboard and optional vector/trace
 * - Chart.js live plot of Fc over time
 */

// -------------------- i18n (AR/EN) --------------------
const dict = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    nav_mechanics: "الميكانيكا الكلاسيكية",
    nav_thermo: "الديناميكا الحرارية",
    nav_em: "الكهرومغناطيسية",
    nav_modern: "الفيزياء الحديثة",
    nav_labs: "مركز المختبرات",
    home: "الصفحة الرئيسية",
    lesson_breadcrumb: "القوى والحركة الدائرية المنتظمة",
    lesson_title: "القوى والحركة الدائرية: فهم الجاذبية المدارية",
    lesson_lead:
      "في الحركة الدائرية المنتظمة، يتحرك الجسم بسرعة ثابتة المقدار على مسار دائري. على الرغم من ثبات السرعة، فإن الاتجاه يتغير باستمرار، مما يعني وجود تسارع مركزي نحو مركز الدائرة. القوة المسؤولة عن هذا التسارع تسمى القوة المركزية.",
    theory_title: "الشرح النظري",
    theory_text_1: "العلاقة الأساسية للحركة الدائرية المنتظمة هي:",
    theory_item_m: "m: الكتلة (kg)",
    theory_item_v: "v: السرعة (m/s)",
    theory_item_r: "r: نصف القطر (m)",
    theory_item_fc: "Fc: القوة المركزية (N)",
    theory_text_2:
      "تظهر القوة المركزية في حالات عديدة مثل دوران الأقمار الصناعية، دوران السيارات في المنحنيات، وحركة الإلكترونات حول النواة في نماذج مبسطة.",
    gif_caption: "اتجاه القوة المركزية نحو المركز",
    video_note: "ملاحظة: أضف فيديو قصير (≤ 3 دقائق) يشرح أمثلة عملية.",
    calc_title: "حاسبة القوة المركزية",
    input_m: "الكتلة m (kg)",
    input_v: "السرعة v (m/s)",
    input_r: "نصف القطر r (m)",
    presets_label: "أمثلة سريعة:",
    preset_satellite: "قمر صناعي",
    preset_car: "سيارة",
    preset_ball: "كرة مربوطة",
    show_steps: "إظهار خطوات الحل",
    result_fc: "القوة المركزية Fc",
    steps_title: "الخطوات",
    lab_title: "محاكي المدارات الدائرية",
    sim_m: "الكتلة m (kg)",
    sim_v: "السرعة v (m/s)",
    sim_r: "نصف القطر r (m)",
    sim_speed: "سرعة المحاكاة",
    toggle_trace: "عرض المسار",
    toggle_fc: "عرض متجه Fc",
    dash_m: "m",
    dash_v: "v",
    dash_r: "r",
    dash_fc: "Fc",
    dash_f: "f",
    dash_T: "T",
    cta_title: "جرّب بنفسك!",
    cta_text:
      "استخدم الضوابط لتغيير المعلمات ولاحظ كيف تتغير القوة المركزية والزمن الدوري. ما الذي يحدث إذا ضاعفت السرعة؟",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف",
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    nav_mechanics: "Classical Mechanics",
    nav_thermo: "Thermodynamics",
    nav_em: "Electromagnetism",
    nav_modern: "Modern Physics",
    nav_labs: "Labs Hub",
    home: "Home",
    lesson_breadcrumb: "Uniform Circular Motion",
    lesson_title: "Forces & Uniform Circular Motion: Understanding Orbital Gravity",
    lesson_lead:
      "In uniform circular motion, an object moves with constant speed along a circular path. Although speed is constant, the direction changes continuously, implying a centripetal acceleration towards the center. The force causing this is the centripetal force.",
    theory_title: "Theory",
    theory_text_1: "The fundamental relation for uniform circular motion is:",
    theory_item_m: "m: mass (kg)",
    theory_item_v: "v: speed (m/s)",
    theory_item_r: "r: radius (m)",
    theory_item_fc: "Fc: centripetal force (N)",
    theory_text_2:
      "Centripetal force appears in many situations like satellite orbits, cars on curves, and simplified models of electrons around nuclei.",
    gif_caption: "Centripetal force direction towards the center",
    video_note: "Note: Add a short (≤ 3 minutes) explanatory video.",
    calc_title: "Centripetal Force Calculator",
    input_m: "Mass m (kg)",
    input_v: "Speed v (m/s)",
    input_r: "Radius r (m)",
    presets_label: "Quick presets:",
    preset_satellite: "Satellite",
    preset_car: "Car",
    preset_ball: "Tethered ball",
    show_steps: "Show steps",
    result_fc: "Centripetal Force Fc",
    steps_title: "Steps",
    lab_title: "Circular Orbit Simulator",
    sim_m: "Mass m (kg)",
    sim_v: "Speed v (m/s)",
    sim_r: "Radius r (m)",
    sim_speed: "Simulation speed",
    toggle_trace: "Show path",
    toggle_fc: "Show Fc vector",
    dash_m: "m",
    dash_v: "v",
    dash_r: "r",
    dash_fc: "Fc",
    dash_f: "f",
    dash_T: "T",
    cta_title: "Try it yourself!",
    cta_text:
      "Use the controls to change parameters and see how Fc and the period evolve. What happens if you double the speed?",
    footer_author_label: "Author & Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone",
  },
};

let currentLang = "ar";

function applyI18n() {
  const t = dict[currentLang];
  document.documentElement.lang = currentLang === "ar" ? "ar" : "en";
  document.documentElement.dir = currentLang === "ar" ? "rtl" : "ltr";

  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });

  // Update aria-labels/titles/placeholders for accessibility
  setAccessibilityText();
}

function setAccessibilityText() {
  const t = dict[currentLang];
  // Calculator inputs placeholders/titles
  const mass = document.getElementById("mass");
  const velocity = document.getElementById("velocity");
  const radius = document.getElementById("radius");
  if (mass) { mass.setAttribute("placeholder", currentLang === "ar" ? "أدخل الكتلة" : "Enter mass"); mass.setAttribute("title", t.input_m); }
  if (velocity) { velocity.setAttribute("placeholder", currentLang === "ar" ? "أدخل السرعة" : "Enter speed"); velocity.setAttribute("title", t.input_v); }
  if (radius) { radius.setAttribute("placeholder", currentLang === "ar" ? "أدخل نصف القطر" : "Enter radius"); radius.setAttribute("title", t.input_r); }

  // Simulation controls titles
  const simMass = document.getElementById("simMass");
  const simVel = document.getElementById("simVel");
  const simRadius = document.getElementById("simRadius");
  const simSpeed = document.getElementById("simSpeed");
  if (simMass) simMass.setAttribute("title", t.sim_m);
  if (simVel) simVel.setAttribute("title", t.sim_v);
  if (simRadius) simRadius.setAttribute("title", t.sim_r);
  if (simSpeed) simSpeed.setAttribute("title", t.sim_speed);
}

// -------------------- Calculator --------------------
function computeFc(m, v, r) {
  if (r <= 0) return 0;
  return (m * v * v) / r;
}

function updateCalculator() {
  const m = parseFloat(document.getElementById("mass").value) || 0;
  const v = parseFloat(document.getElementById("velocity").value) || 0;
  const r = parseFloat(document.getElementById("radius").value) || 0.0000001;

  const Fc = computeFc(m, v, r);
  document.getElementById("fcValue").textContent = Fc.toFixed(3);

  if (!document.getElementById("stepsBox").hidden) {
    renderSteps(m, v, r, Fc);
  }
}

function syncSlider(inputId, rangeId) {
  const input = document.getElementById(inputId);
  const range = document.getElementById(rangeId);
  input.addEventListener("input", () => {
    range.value = input.value;
    updateCalculator();
  });
  range.addEventListener("input", () => {
    input.value = range.value;
    updateCalculator();
  });
}

function renderSteps(m, v, r, Fc) {
  const stepsList = document.getElementById("stepsList");
  const isAr = currentLang === "ar";
  stepsList.innerHTML = "";
  const items = [
    isAr ? `المعطيات: m = ${m} kg, v = ${v} m/s, r = ${r} m` : `Given: m = ${m} kg, v = ${v} m/s, r = ${r} m`,
    isAr ? "العلاقة: Fc = (m * v^2) / r" : "Formula: Fc = (m * v^2) / r",
    isAr ? `v^2 = ${v}^2 = ${(v * v).toFixed(3)}` : `v^2 = ${v}^2 = ${(v * v).toFixed(3)}`,
    isAr ? `m * v^2 = ${m} * ${(v * v).toFixed(3)} = ${(m * v * v).toFixed(3)}`
         : `m * v^2 = ${m} * ${(v * v).toFixed(3)} = ${(m * v * v).toFixed(3)}`,
    isAr ? `Fc = ${(m * v * v).toFixed(3)} / ${r} = ${Fc.toFixed(3)} N`
         : `Fc = ${(m * v * v).toFixed(3)} / ${r} = ${Fc.toFixed(3)} N`,
  ];
  items.forEach((text) => {
    const li = document.createElement("li");
    li.textContent = text;
    stepsList.appendChild(li);
  });
}

function setupCalculator() {
  syncSlider("mass", "massRange");
  syncSlider("velocity", "velocityRange");
  syncSlider("radius", "radiusRange");

  document.getElementById("showSteps").addEventListener("click", () => {
    const box = document.getElementById("stepsBox");
    box.hidden = !box.hidden;
    updateCalculator();
  });

  // Presets
  document.querySelectorAll(".preset").forEach((btn) => {
    btn.addEventListener("click", () => {
      const p = btn.getAttribute("data-preset");
      if (p === "satellite") {
        // Simplified values
        setCalcValues(500, 7600, 6800000); // kg, m/s, m (LEO-like scale)
      } else if (p === "car") {
        setCalcValues(1200, 20, 50); // car turning
      } else if (p === "ball") {
        setCalcValues(0.2, 8, 1.5); // tethered ball
      }
    });
  });

  function setCalcValues(m, v, r) {
    const ids = [
      ["mass", "massRange", m],
      ["velocity", "velocityRange", v],
      ["radius", "radiusRange", r],
    ];
    ids.forEach(([n, rId, val]) => {
      document.getElementById(n).value = val;
      document.getElementById(rId).value = val;
    });
    updateCalculator();
  }

  updateCalculator();
}

// -------------------- p5.js Orbit Simulation --------------------
let sketchInstance;
let state = {
  m: 10,
  v: 10,
  r: 120,
  simSpeed: 1,
  angle: 0,
  trace: [],
  showTrace: true,
  showFcVector: true,
};

function periodFromVR(v, r) {
  if (v <= 0) return Infinity;
  const circumference = 2 * Math.PI * r;
  return circumference / v;
}

function frequencyFromVR(v, r) {
  const T = periodFromVR(v, r);
  return T === Infinity ? 0 : 1 / T;
}

function initP5() {
  const container = document.getElementById("p5-container");

  const s = (p) => {
    let w = container.clientWidth;
    let h = container.clientHeight;

    p.setup = function () {
      const cnv = p.createCanvas(w, h);
      cnv.parent(container);
      p.frameRate(60);
      state.angle = 0;
      state.trace = [];
    };

    p.windowResized = function () {
      w = container.clientWidth;
      h = container.clientHeight;
      p.resizeCanvas(w, h);
    };

    p.draw = function () {
      p.background(8, 12, 30);

      // Center
      const cx = p.width / 2;
      const cy = p.height / 2;

      // Draw orbit
      p.noFill();
      p.stroke(120, 160, 255, 160);
      p.strokeWeight(2);
      p.circle(cx, cy, 2 * state.r);

      // Update kinematics
      const dt = (p.deltaTime / 1000) * state.simSpeed;
      const omega = state.v / state.r; // rad/s
      state.angle += omega * dt;

      // Position on circle
      const x = cx + state.r * Math.cos(state.angle);
      const y = cy + state.r * Math.sin(state.angle);

      // Trace
      if (state.showTrace) {
        state.trace.push({ x, y });
        if (state.trace.length > 600) state.trace.shift();
        p.stroke(80, 220, 180, 120);
        p.noFill();
        p.beginShape();
        state.trace.forEach((pt) => p.vertex(pt.x, pt.y));
        p.endShape();
      } else {
        state.trace = [];
      }

      // Draw object
      p.noStroke();
      p.fill(58, 160, 255);
      p.circle(x, y, 14);

      // Fc vector (towards center)
      if (state.showFcVector) {
        p.stroke(255, 92, 92);
        p.strokeWeight(3);
        p.line(x, y, cx, cy);
        // Arrow head
        const ang = Math.atan2(cy - y, cx - x);
        const ah = 10;
        p.line(cx, cy, cx + ah * Math.cos(ang + 0.3), cy + ah * Math.sin(ang + 0.3));
        p.line(cx, cy, cx + ah * Math.cos(ang - 0.3), cy + ah * Math.sin(ang - 0.3));
      }

      // Dashboard values update
      updateDashboard();
      pushChartPoint();
    };
  };

  if (sketchInstance) sketchInstance.remove();
  sketchInstance = new p5(s);
}

function updateDashboard() {
  document.getElementById("dashM").textContent = state.m.toFixed(0);
  document.getElementById("dashV").textContent = state.v.toFixed(1);
  document.getElementById("dashR").textContent = state.r.toFixed(0);

  const Fc = computeFc(state.m, state.v, state.r);
  document.getElementById("dashFc").textContent = Fc.toFixed(2);

  const f = frequencyFromVR(state.v, state.r);
  const T = periodFromVR(state.v, state.r);
  document.getElementById("dashF").textContent = f.toFixed(3);
  document.getElementById("dashT").textContent = isFinite(T) ? T.toFixed(2) : "∞";
}

// -------------------- Chart.js --------------------
let fcChart;
let chartTime = 0;

function initChart() {
  const ctx = document.getElementById("fcChart");
  fcChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: [],
      datasets: [
        {
          label: "Fc (N)",
          data: [],
          borderColor: "rgba(58,160,255,1)",
          backgroundColor: "rgba(58,160,255,0.2)",
          tension: 0.25,
          pointRadius: 0,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          title: { display: true, text: "t (s)" },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          title: { display: true, text: "Fc (N)" },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
      plugins: {
        legend: { labels: { color: "#e9eefb" } },
      },
    },
  });
}

function pushChartPoint() {
  if (!fcChart) return;
  chartTime += 1 / 30; // ~30 fps logical time
  const Fc = computeFc(state.m, state.v, state.r);

  fcChart.data.labels.push(chartTime.toFixed(2));
  fcChart.data.datasets[0].data.push(Fc);
  if (fcChart.data.labels.length > 300) {
    fcChart.data.labels.shift();
    fcChart.data.datasets[0].data.shift();
  }
  fcChart.update("none");
}

// -------------------- Language toggle --------------------
function setupLangToggle() {
  const btn = document.getElementById("langToggle");
  btn.addEventListener("click", () => {
    currentLang = currentLang === "ar" ? "en" : "ar";
    applyI18n();
    // Sync Chart.js labels with current language
    if (fcChart) {
      if (currentLang === "ar") {
        fcChart.options.scales.x.title.text = "الزمن (ث)";
        fcChart.options.scales.y.title.text = "Fc (نيوتن)";
        fcChart.data.datasets[0].label = "Fc (نيوتن)";
      } else {
        fcChart.options.scales.x.title.text = "t (s)";
        fcChart.options.scales.y.title.text = "Fc (N)";
        fcChart.data.datasets[0].label = "Fc (N)";
      }
      fcChart.update();
    }
  });
}

// -------------------- Bindings for simulation controls --------------------
function setupSimControls() {
  const simMass = document.getElementById("simMass");
  const simVel = document.getElementById("simVel");
  const simRadius = document.getElementById("simRadius");
  const simSpeed = document.getElementById("simSpeed");

  const simMassOut = document.getElementById("simMassOut");
  const simVelOut = document.getElementById("simVelOut");
  const simRadiusOut = document.getElementById("simRadiusOut");
  const simSpeedOut = document.getElementById("simSpeedOut");

  const showTrace = document.getElementById("showTrace");
  const showFcVector = document.getElementById("showFcVector");

  simMass.addEventListener("input", () => {
    state.m = parseFloat(simMass.value);
    simMassOut.textContent = state.m.toFixed(0);
  });
  simVel.addEventListener("input", () => {
    state.v = parseFloat(simVel.value);
    simVelOut.textContent = state.v.toFixed(1);
  });
  simRadius.addEventListener("input", () => {
    state.r = parseFloat(simRadius.value);
    simRadiusOut.textContent = state.r.toFixed(0);
  });
  simSpeed.addEventListener("input", () => {
    state.simSpeed = parseFloat(simSpeed.value);
    simSpeedOut.textContent = `${state.simSpeed.toFixed(2)}x`;
  });

  showTrace.addEventListener("change", () => {
    state.showTrace = showTrace.checked;
  });
  showFcVector.addEventListener("change", () => {
    state.showFcVector = showFcVector.checked;
  });

  // Initialize outputs
  simMass.dispatchEvent(new Event("input"));
  simVel.dispatchEvent(new Event("input"));
  simRadius.dispatchEvent(new Event("input"));
  simSpeed.dispatchEvent(new Event("input"));
}

// -------------------- Accessibility: fix unlabeled inputs --------------------
function ensureFormAccessibility() {
  // Add placeholders/titles for number inputs to satisfy linters
  setAccessibilityText();
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  // Setup i18n and language toggle
  setupLangToggle();
  applyI18n();

  // Accessibility
  ensureFormAccessibility();

  // Calculator
  setupCalculator();

  // Simulation and chart
  initP5();
  initChart();
});
