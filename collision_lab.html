<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | مختبر التصادم وحفظ الزخم</title>
  <link rel="stylesheet" href="styles/collision_lab.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="energy_conservation.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">مختبر التصادم وحفظ الزخم</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">مختبر التصادم وحفظ الزخم: التصادمات المرنة وغير المرنة</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف مبدأ حفظ الزخم في التصادمات المختلفة. قارن بين التصادمات المرنة وغير المرنة وشاهد كيف يُحفظ الزخم دائماً بينما الطاقة الحركية قد تُحفظ أو تضيع حسب نوع التصادم.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">الزخم: p = mv (كتلة × سرعة)</li>
            <li data-i18n="t2">حفظ الزخم: p_قبل = p_بعد (في نظام معزول)</li>
            <li data-i18n="t3">التصادم المرن: تُحفظ الطاقة الحركية والزخم</li>
            <li data-i18n="t4">التصادم غير المرن: يُحفظ الزخم فقط</li>
            <li data-i18n="t5">التصادم غير المرن تماماً: الأجسام تلتصق معاً</li>
          </ul>
          <div class="formula">m₁v₁ᵢ + m₂v₂ᵢ = m₁v₁f + m₂v₂f</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot obj1"></i> Object 1</span>
              <span class="legend-item"><i class="dot obj2"></i> Object 2</span>
              <span class="legend-item"><i class="dot momentum"></i> Momentum</span>
              <span class="legend-item"><i class="dot collision"></i> Collision</span>
            </div>
            <div id="p5-container" class="p5-container" aria-label="Collision simulation canvas"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="object-controls">
            <h3 data-i18n="object1_title">الجسم الأول</h3>
            <div class="field">
              <label for="mass1" data-i18n="mass1_label">الكتلة m₁ (kg)</label>
              <input type="range" id="mass1" min="0.5" max="10" step="0.1" value="2"/>
              <output id="mass1Out">2.0</output>
            </div>
            <div class="field">
              <label for="velocity1" data-i18n="velocity1_label">السرعة الابتدائية v₁ (m/s)</label>
              <input type="range" id="velocity1" min="-10" max="10" step="0.1" value="5"/>
              <output id="velocity1Out">5.0</output>
            </div>
          </div>
          
          <div class="object-controls">
            <h3 data-i18n="object2_title">الجسم الثاني</h3>
            <div class="field">
              <label for="mass2" data-i18n="mass2_label">الكتلة m₂ (kg)</label>
              <input type="range" id="mass2" min="0.5" max="10" step="0.1" value="1"/>
              <output id="mass2Out">1.0</output>
            </div>
            <div class="field">
              <label for="velocity2" data-i18n="velocity2_label">السرعة الابتدائية v₂ (m/s)</label>
              <input type="range" id="velocity2" min="-10" max="10" step="0.1" value="0"/>
              <output id="velocity2Out">0.0</output>
            </div>
          </div>
          
          <div class="collision-controls">
            <div class="field">
              <label data-i18n="collision_type_label">نوع التصادم</label>
              <div class="collision-types">
                <button class="btn preset active" data-type="elastic" data-i18n="elastic">مرن</button>
                <button class="btn preset" data-type="inelastic" data-i18n="inelastic">غير مرن</button>
                <button class="btn preset" data-type="perfectly_inelastic" data-i18n="perfectly_inelastic">غير مرن تماماً</button>
              </div>
            </div>
            <div class="field">
              <label for="restitution" data-i18n="restitution_label">معامل الاسترداد e</label>
              <input type="range" id="restitution" min="0" max="1" step="0.05" value="1"/>
              <output id="restitutionOut">1.00</output>
            </div>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="start" class="btn" data-i18n="start">بدء التصادم</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="before_collision">قبل التصادم</h4>
              <div class="dash-row">
                <div class="dash-item"><span data-i18n="dash_p1">p₁</span> = <span id="dashP1Before">10.0</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_p2">p₂</span> = <span id="dashP2Before">0.0</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_ptotal">p_total</span> = <span id="dashPTotalBefore">10.0</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_ke">KE</span> = <span id="dashKEBefore">25.0</span> J</div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="after_collision">بعد التصادم</h4>
              <div class="dash-row">
                <div class="dash-item"><span data-i18n="dash_p1">p₁</span> = <span id="dashP1After">--</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_p2">p₂</span> = <span id="dashP2After">--</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_ptotal">p_total</span> = <span id="dashPTotalAfter">--</span> kg⋅m/s</div>
                <div class="dash-item"><span data-i18n="dash_ke">KE</span> = <span id="dashKEAfter">--</span> J</div>
              </div>
            </div>
            
            <canvas id="momentumChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        التحقق من أن الزخم محفوظ دائماً في نظام معزول، بينما الطاقة الحركية تُحفظ فقط في التصادمات المرنة. في التصادمات غير المرنة، جزء من الطاقة الحركية يتحول إلى أشكال أخرى من الطاقة.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/collision_lab.js"></script>
</body>
</html>
