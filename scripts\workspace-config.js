/**
 * PhysicsHub - Workspace Configuration
 * Standardized workspace setup and scaling for all labs
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 */

// Workspace configuration constants
const WORKSPACE_CONFIG = {
  // Standard dimensions
  DEFAULT_WIDTH: 600,
  DEFAULT_HEIGHT: 400,
  MIN_WIDTH: 400,
  MIN_HEIGHT: 300,
  MAX_WIDTH: 800,
  MAX_HEIGHT: 500,
  
  // Physics simulation bounds
  BOUNDS_PADDING: 20,
  
  // Grid and scaling
  GRID_SIZE: 20,
  PIXELS_PER_CM: 10, // 10 pixels = 1 cm in simulation
  PIXELS_PER_METER: 100, // 100 pixels = 1 meter in simulation
  
  // Lab-specific configurations
  LAB_CONFIGS: {
    projectiles: { width: 600, height: 450, scale: 1.0 },
    newton_laws: { width: 600, height: 400, scale: 1.0 },
    energy_conservation: { width: 650, height: 450, scale: 1.0 },
    collision_lab: { width: 700, height: 350, scale: 1.0 },
    torque_balance: { width: 550, height: 350, scale: 1.0 },
    pendulum: { width: 500, height: 500, scale: 1.0 },
    ideal_gas: { width: 500, height: 500, scale: 1.0 },
    carnot_engine: { width: 650, height: 400, scale: 1.0 },
    heat_transfer: { width: 600, height: 400, scale: 1.0 },
    states_of_matter: { width: 550, height: 450, scale: 1.0 },
    specific_heat: { width: 600, height: 400, scale: 1.0 }
  }
};

// Workspace manager class
class WorkspaceManager {
  constructor(labName = 'default') {
    this.labName = labName;
    this.config = WORKSPACE_CONFIG.LAB_CONFIGS[labName] || {
      width: WORKSPACE_CONFIG.DEFAULT_WIDTH,
      height: WORKSPACE_CONFIG.DEFAULT_HEIGHT,
      scale: 1.0
    };
    
    this.actualWidth = this.config.width;
    this.actualHeight = this.config.height;
    this.scale = this.config.scale;
    
    this.setupResponsiveScaling();
    this.addWorkspaceInfo();
  }
  
  setupResponsiveScaling() {
    const updateDimensions = () => {
      const container = document.getElementById('p5-container');
      if (!container) return;
      
      const containerRect = container.getBoundingClientRect();
      const availableWidth = containerRect.width || window.innerWidth - 80;
      const availableHeight = window.innerHeight * 0.6;
      
      // Calculate responsive scale
      const widthScale = Math.min(1, availableWidth / this.config.width);
      const heightScale = Math.min(1, availableHeight / this.config.height);
      this.scale = Math.min(widthScale, heightScale, 1.0);
      
      // Apply minimum constraints
      this.actualWidth = Math.max(
        WORKSPACE_CONFIG.MIN_WIDTH,
        Math.min(this.config.width * this.scale, WORKSPACE_CONFIG.MAX_WIDTH)
      );
      this.actualHeight = Math.max(
        WORKSPACE_CONFIG.MIN_HEIGHT,
        Math.min(this.config.height * this.scale, WORKSPACE_CONFIG.MAX_HEIGHT)
      );
      
      // Update CSS custom properties
      document.documentElement.style.setProperty('--workspace-width', `${this.actualWidth}px`);
      document.documentElement.style.setProperty('--workspace-height', `${this.actualHeight}px`);
      document.documentElement.style.setProperty('--scale-factor', this.scale.toString());
      
      // Update physics world dimensions
      const physicsWidth = this.actualWidth - (2 * WORKSPACE_CONFIG.BOUNDS_PADDING);
      const physicsHeight = this.actualHeight - (2 * WORKSPACE_CONFIG.BOUNDS_PADDING);
      document.documentElement.style.setProperty('--physics-world-width', `${physicsWidth}px`);
      document.documentElement.style.setProperty('--physics-world-height', `${physicsHeight}px`);
    };
    
    // Initial setup
    updateDimensions();
    
    // Update on resize
    window.addEventListener('resize', updateDimensions);
    
    // Update when container is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', updateDimensions);
    }
  }
  
  addWorkspaceInfo() {
    const container = document.getElementById('p5-container');
    if (!container) return;
    
    // Add workspace info overlay
    const infoDiv = document.createElement('div');
    infoDiv.className = 'workspace-info';
    infoDiv.innerHTML = `${this.actualWidth}×${this.actualHeight}px | Scale: ${(this.scale * 100).toFixed(0)}%`;
    container.appendChild(infoDiv);
    
    // Add scale indicator
    const scaleDiv = document.createElement('div');
    scaleDiv.className = 'scale-indicator';
    scaleDiv.innerHTML = `5 cm`;
    container.appendChild(scaleDiv);
    
    // Update info when dimensions change
    const updateInfo = () => {
      infoDiv.innerHTML = `${this.actualWidth}×${this.actualHeight}px | Scale: ${(this.scale * 100).toFixed(0)}%`;
    };
    
    window.addEventListener('resize', updateInfo);
  }
  
  // Convert between coordinate systems
  pixelsToMeters(pixels) {
    return (pixels * this.scale) / WORKSPACE_CONFIG.PIXELS_PER_METER;
  }
  
  metersToPixels(meters) {
    return (meters * WORKSPACE_CONFIG.PIXELS_PER_METER) / this.scale;
  }
  
  pixelsToCentimeters(pixels) {
    return (pixels * this.scale) / WORKSPACE_CONFIG.PIXELS_PER_CM;
  }
  
  centimetersToPixels(centimeters) {
    return (centimeters * WORKSPACE_CONFIG.PIXELS_PER_CM) / this.scale;
  }
  
  // Get physics world bounds
  getPhysicsBounds() {
    return {
      left: WORKSPACE_CONFIG.BOUNDS_PADDING,
      right: this.actualWidth - WORKSPACE_CONFIG.BOUNDS_PADDING,
      top: WORKSPACE_CONFIG.BOUNDS_PADDING,
      bottom: this.actualHeight - WORKSPACE_CONFIG.BOUNDS_PADDING,
      width: this.actualWidth - (2 * WORKSPACE_CONFIG.BOUNDS_PADDING),
      height: this.actualHeight - (2 * WORKSPACE_CONFIG.BOUNDS_PADDING)
    };
  }
  
  // Check if point is within physics bounds
  isInBounds(x, y) {
    const bounds = this.getPhysicsBounds();
    return x >= bounds.left && x <= bounds.right && y >= bounds.top && y <= bounds.bottom;
  }
  
  // Constrain point to physics bounds
  constrainToBounds(x, y, margin = 0) {
    const bounds = this.getPhysicsBounds();
    return {
      x: Math.max(bounds.left + margin, Math.min(bounds.right - margin, x)),
      y: Math.max(bounds.top + margin, Math.min(bounds.bottom - margin, y))
    };
  }
  
  // Draw grid overlay
  drawGrid(p5, alpha = 0.1) {
    p5.stroke(255, 255, 255, alpha * 255);
    p5.strokeWeight(1);
    
    // Vertical lines
    for (let x = WORKSPACE_CONFIG.GRID_SIZE; x < this.actualWidth; x += WORKSPACE_CONFIG.GRID_SIZE) {
      p5.line(x, 0, x, this.actualHeight);
    }
    
    // Horizontal lines
    for (let y = WORKSPACE_CONFIG.GRID_SIZE; y < this.actualHeight; y += WORKSPACE_CONFIG.GRID_SIZE) {
      p5.line(0, y, this.actualWidth, y);
    }
  }
  
  // Draw coordinate axes
  drawAxes(p5, originX, originY, alpha = 0.3) {
    p5.stroke(255, 255, 255, alpha * 255);
    p5.strokeWeight(2);
    
    // X-axis
    p5.line(0, originY, this.actualWidth, originY);
    
    // Y-axis
    p5.line(originX, 0, originX, this.actualHeight);
    
    // Origin point
    p5.fill(255, 255, 255, alpha * 255);
    p5.noStroke();
    p5.circle(originX, originY, 6);
  }
  
  // Draw measurement rulers
  drawRulers(p5, alpha = 0.2) {
    const bounds = this.getPhysicsBounds();
    
    p5.stroke(255, 255, 255, alpha * 255);
    p5.strokeWeight(1);
    p5.fill(255, 255, 255, alpha * 255);
    p5.textAlign(p5.CENTER, p5.CENTER);
    p5.textSize(10);
    
    // Bottom ruler (X-axis)
    for (let x = bounds.left; x <= bounds.right; x += WORKSPACE_CONFIG.GRID_SIZE) {
      const cm = this.pixelsToCentimeters(x - bounds.left);
      if (cm % 5 === 0) { // Every 5 cm
        p5.line(x, bounds.bottom, x, bounds.bottom + 8);
        if (cm > 0) {
          p5.text(cm.toFixed(0), x, bounds.bottom + 15);
        }
      } else {
        p5.line(x, bounds.bottom, x, bounds.bottom + 4);
      }
    }
    
    // Left ruler (Y-axis)
    for (let y = bounds.bottom; y >= bounds.top; y -= WORKSPACE_CONFIG.GRID_SIZE) {
      const cm = this.pixelsToCentimeters(bounds.bottom - y);
      if (cm % 5 === 0) { // Every 5 cm
        p5.line(bounds.left - 8, y, bounds.left, y);
        if (cm > 0) {
          p5.push();
          p5.translate(bounds.left - 15, y);
          p5.rotate(-p5.PI/2);
          p5.text(cm.toFixed(0), 0, 0);
          p5.pop();
        }
      } else {
        p5.line(bounds.left - 4, y, bounds.left, y);
      }
    }
  }
  
  // Get canvas dimensions for p5.js
  getCanvasDimensions() {
    return {
      width: this.actualWidth,
      height: this.actualHeight
    };
  }
}

// Global workspace manager instance
let workspaceManager = null;

// Initialize workspace for a specific lab
function initializeWorkspace(labName) {
  workspaceManager = new WorkspaceManager(labName);
  return workspaceManager;
}

// Helper function to get current workspace manager
function getWorkspace() {
  return workspaceManager;
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { WorkspaceManager, WORKSPACE_CONFIG, initializeWorkspace, getWorkspace };
}
