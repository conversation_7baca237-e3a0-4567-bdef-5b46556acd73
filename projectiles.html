<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | محاكي المقذوفات</title>
  <link rel="stylesheet" href="styles/projectiles.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="circular_motion.html" data-i18n="nav_mechanics">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="#" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">محاكي المقذوفات</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">محاكي المقذوفات: علم الحركة في بعدين</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف حركة المقذوف بزاوية ابتدائية وسرعة ابتدائية، وشاهد استقلال المركبتين الأفقية والرأسية. جرّب تغيير الجاذبية لمحاكاة كواكب مختلفة، وفعّل مقاومة الهواء لملاحظة تأثيرها على المدى والارتفاع.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">تفكيك السرعة الابتدائية: vx = v0 cos(θ), vy = v0 sin(θ)</li>
            <li data-i18n="t2">بدون مقاومة هواء: x(t) = v0 cos(θ) t, y(t) = v0 sin(θ) t − 0.5 g t²</li>
            <li data-i18n="t3">المدى الأقصى عند θ = 45° (بدون مقاومة هواء وعلى نفس الارتفاع)</li>
          </ul>
          <div class="formula">T = 2 v0 sin(θ) / g, Hmax = v0² sin²(θ) / (2g), R = v0² sin(2θ)/g</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot v"></i> v</span>
              <span class="legend-item"><i class="dot vx"></i> vx</span>
              <span class="legend-item"><i class="dot vy"></i> vy</span>
              <span class="legend-item"><i class="dot drag"></i> Drag</span>
            </div>
            <div id="p5-container" class="p5-container" aria-label="Projectile simulation canvas"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="field">
            <label for="v0" data-i18n="v0_label">السرعة الابتدائية v0 (m/s)</label>
            <input type="range" id="v0" min="0" max="100" step="1" value="30"/>
            <output id="v0Out">30</output>
          </div>
          <div class="field">
            <label for="angle" data-i18n="angle_label">زاوية القذف θ (°)</label>
            <input type="range" id="angle" min="0" max="90" step="0.5" value="45"/>
            <output id="angleOut">45.0°</output>
          </div>
          <div class="field">
            <label for="mass" data-i18n="mass_label">الكتلة m (kg)</label>
            <input type="range" id="mass" min="0.1" max="10" step="0.1" value="1"/>
            <output id="massOut">1.0</output>
          </div>
          <div class="field">
            <label for="gravity" data-i18n="g_label">الجاذبية g (m/s²)</label>
            <input type="range" id="gravity" min="0.5" max="30" step="0.1" value="9.8"/>
            <output id="gravityOut">9.8</output>
            <div class="presets">
              <button class="btn preset" data-g="1.62">القمر 1.62</button>
              <button class="btn preset" data-g="3.71">المريخ 3.71</button>
              <button class="btn preset" data-g="9.81">الأرض 9.81</button>
              <button class="btn preset" data-g="24.79">المشتري 24.79</button>
            </div>
          </div>
          <div class="field inline">
            <label class="toggle">
              <input type="checkbox" id="air" />
              <span data-i18n="air_label">مقاومة الهواء</span>
            </label>
            <label>
              <span data-i18n="cd_label">معامل السحب Cd</span>
              <input type="range" id="cd" min="0.05" max="1.2" step="0.05" value="0.47"/>
              <output id="cdOut">0.47</output>
            </label>
            <label>
              <span data-i18n="A_label">المساحة A (m²)</span>
              <input type="range" id="area" min="0.001" max="0.1" step="0.001" value="0.01"/>
              <output id="areaOut">0.010</output>
            </label>
            <label>
              <span data-i18n="rho_label">كثافة الهواء ρ (kg/m³)</span>
              <input type="range" id="rho" min="0.1" max="2.0" step="0.1" value="1.225"/>
              <output id="rhoOut">1.225</output>
            </label>
          </div>
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="restart" class="btn" data-i18n="restart">بدء/إعادة تشغيل</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-row">
              <div class="dash-item"><span>vx</span> = <span id="dashVx">21.21</span> m/s</div>
              <div class="dash-item"><span>vy</span> = <span id="dashVy">21.21</span> m/s</div>
              <div class="dash-item"><span data-i18n="dash_H">Hmax</span> = <span id="dashH">22.94</span> m</div>
              <div class="dash-item"><span data-i18n="dash_R">R</span> = <span id="dashR">91.74</span> m</div>
              <div class="dash-item"><span data-i18n="dash_T">T</span> = <span id="dashT">4.31</span> s</div>
            </div>
            <canvas id="chartRange" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        فهم استقلالية الحركة الأفقية والرأسية، تحديد الزاوية المثلى لتحقيق أقصى مدى، وملاحظة تأثير مقاومة الهواء والجاذبية على حركة المقذوف.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/projectiles.js"></script>
</body>
</html>
