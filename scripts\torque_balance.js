/**
 * PhysicsHub - Torque and Balance Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: masses, positions, gravity
 * - p5.js visualization: balance beam with weights and fulcrum
 * - Live dashboard: individual torques, net torque, balance status
 * - Chart.js: torque comparison visualization
 * - Auto-balance feature
 */

// -------------------- i18n (AR/EN) --------------------
const TDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "محاكي العزم والتوازن",
    lesson_title: "محاكي العزم والتوازن: الاتزان الدوراني",
    lesson_lead: "اكتشف شروط الاتزان الدوراني من خلال وضع أثقال مختلفة على مسافات متنوعة من نقطة الارتكاز. تعلم كيف يعتمد العزم على القوة والمسافة وشاهد متى تتوازن العارضة.",
    theory_title: "المفهوم الأساسي",
    t1: "العزم: τ = r × F (المسافة × القوة)",
    t2: "اتجاه العزم: مع عقارب الساعة (+) أو عكسها (-)",
    t3: "شرط التوازن الدوراني: Στ = 0",
    t4: "العزم مع عقارب الساعة = العزم عكس عقارب الساعة",
    t5: "وحدة العزم: نيوتن⋅متر (N⋅m)",
    lab_title: "المعلمات التفاعلية",
    weights_title: "الأثقال",
    weight1_title: "الثقل الأول",
    weight2_title: "الثقل الثاني",
    weight3_title: "الثقل الثالث",
    mass_label: "الكتلة (kg)",
    position_label: "الموضع (cm)",
    gravity_label: "الجاذبية g (m/s²)",
    reset: "إعادة ضبط",
    auto_balance: "توازن تلقائي",
    torques_title: "العزوم",
    forces_title: "القوى",
    dash_torque1: "τ₁",
    dash_torque2: "τ₂",
    dash_torque3: "τ₃",
    dash_net_torque: "Στ",
    dash_balance: "التوازن",
    dash_weight1: "W₁",
    dash_weight2: "W₂",
    dash_weight3: "W₃",
    balanced: "متوازن",
    unbalanced: "غير متوازن",
    cta_title: "الهدف التعليمي",
    cta_text: "اكتشاف شرط التوازن الدوراني: أن يكون مجموع العزوم مع عقارب الساعة مساوياً لمجموع العزوم عكس عقارب الساعة. فهم أن العزم يعتمد على كل من القوة والمسافة من نقطة الارتكاز.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Torque and Balance Simulator",
    lesson_title: "Torque and Balance Simulator: Rotational Equilibrium",
    lesson_lead: "Discover the conditions for rotational equilibrium by placing different weights at various distances from the fulcrum. Learn how torque depends on both force and distance and see when the beam balances.",
    theory_title: "Core Concept",
    t1: "Torque: τ = r × F (distance × force)",
    t2: "Torque direction: clockwise (+) or counterclockwise (-)",
    t3: "Rotational equilibrium condition: Στ = 0",
    t4: "Clockwise torque = Counterclockwise torque",
    t5: "Torque unit: Newton⋅meter (N⋅m)",
    lab_title: "Interactive Parameters",
    weights_title: "Weights",
    weight1_title: "Weight 1",
    weight2_title: "Weight 2",
    weight3_title: "Weight 3",
    mass_label: "Mass (kg)",
    position_label: "Position (cm)",
    gravity_label: "Gravity g (m/s²)",
    reset: "Reset",
    auto_balance: "Auto Balance",
    torques_title: "Torques",
    forces_title: "Forces",
    dash_torque1: "τ₁",
    dash_torque2: "τ₂",
    dash_torque3: "τ₃",
    dash_net_torque: "Στ",
    dash_balance: "Balance",
    dash_weight1: "W₁",
    dash_weight2: "W₂",
    dash_weight3: "W₃",
    balanced: "Balanced",
    unbalanced: "Unbalanced",
    cta_title: "Learning Objective",
    cta_text: "Discover the condition for rotational equilibrium: the sum of clockwise torques must equal the sum of counterclockwise torques. Understand that torque depends on both force and distance from the fulcrum.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let tLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyTorqueI18n() {
  const t = TDICT[tLang];
  document.documentElement.lang = tLang === "ar" ? "ar" : "en";
  document.documentElement.dir = tLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  
  // Update balance status
  updateBalanceStatus();
}

function setupLangToggleTorque() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      tLang = tLang === "ar" ? "en" : "ar";
      applyTorqueI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let tParams = {
  masses: [2.0, 4.0, 0.0],
  positions: [-20, 10, 0], // in cm
  gravity: 9.8
};

// -------------------- Simulation State --------------------
let beamState = {
  angle: 0, // beam rotation angle
  torques: [0, 0, 0],
  netTorque: 0,
  isBalanced: false,
  weights: [0, 0, 0]
};

// -------------------- p5.js Sketch --------------------
let p5Instance;

function initP5Torque() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  p5Instance = new p5((p) => {
    p.setup = () => {
      const canvas = p.createCanvas(container.offsetWidth, container.offsetHeight);
      canvas.parent(container);
      calculateTorques();
    };

    p.windowResized = () => {
      p.resizeCanvas(container.offsetWidth, container.offsetHeight);
    };

    p.draw = () => {
      p.background(15, 20, 35);
      
      // Draw fulcrum
      drawFulcrum(p);
      
      // Draw beam
      drawBeam(p);
      
      // Draw weights
      drawWeights(p);
      
      // Draw torque vectors
      drawTorqueVectors(p);
      
      // Update beam angle based on net torque
      updateBeamAngle();
    };
  });
}

function drawFulcrum(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2 + 50;
  
  // Draw triangular fulcrum
  p.fill(107, 114, 128);
  p.noStroke();
  p.triangle(centerX - 20, centerY + 20, centerX + 20, centerY + 20, centerX, centerY - 20);
  
  // Draw base
  p.rect(centerX - 30, centerY + 20, 60, 10);
}

function drawBeam(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2 + 30;
  const beamLength = 300;
  
  p.push();
  p.translate(centerX, centerY);
  p.rotate(beamState.angle);
  
  // Draw beam
  p.fill(139, 92, 246);
  p.noStroke();
  p.rect(-beamLength/2, -5, beamLength, 10);
  
  // Draw scale marks
  p.fill(255, 255, 255, 150);
  for (let i = -50; i <= 50; i += 10) {
    const x = (i / 50) * (beamLength / 2);
    p.rect(x - 1, -8, 2, 6);
    if (i % 20 === 0 && i !== 0) {
      p.textAlign(p.CENTER);
      p.textSize(10);
      p.text(i, x, -12);
    }
  }
  
  p.pop();
}

function drawWeights(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2 + 30;
  const beamLength = 300;
  
  for (let i = 0; i < 3; i++) {
    if (tParams.masses[i] > 0) {
      const x = centerX + (tParams.positions[i] / 50) * (beamLength / 2);
      const y = centerY + 20 + beamState.angle * (tParams.positions[i] / 50) * (beamLength / 2);
      const size = Math.max(15, Math.min(40, tParams.masses[i] * 8));
      
      // Weight color based on index
      const colors = [[59, 130, 246], [239, 68, 68], [16, 185, 129]];
      p.fill(colors[i][0], colors[i][1], colors[i][2]);
      p.noStroke();
      
      // Draw weight as rectangle
      p.rect(x - size/2, y, size, size);
      
      // Draw string
      p.stroke(255, 255, 255, 150);
      p.strokeWeight(2);
      p.line(x, centerY + beamState.angle * (tParams.positions[i] / 50) * (beamLength / 2), x, y);
      
      // Label
      p.fill(255);
      p.noStroke();
      p.textAlign(p.CENTER);
      p.textSize(12);
      p.text((i + 1).toString(), x, y + size/2 + 4);
    }
  }
}

function drawTorqueVectors(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2 + 30;
  const beamLength = 300;
  
  for (let i = 0; i < 3; i++) {
    if (tParams.masses[i] > 0 && Math.abs(beamState.torques[i]) > 0.1) {
      const x = centerX + (tParams.positions[i] / 50) * (beamLength / 2);
      const y = centerY - 40;
      
      // Torque vector (curved arrow)
      p.stroke(16, 185, 129);
      p.strokeWeight(3);
      p.noFill();
      
      const radius = 20;
      const startAngle = beamState.torques[i] > 0 ? 0 : Math.PI;
      const endAngle = beamState.torques[i] > 0 ? Math.PI : 2 * Math.PI;
      
      p.arc(x, y, radius * 2, radius * 2, startAngle, endAngle);
      
      // Arrow head
      const arrowX = x + radius * Math.cos(endAngle);
      const arrowY = y + radius * Math.sin(endAngle);
      const arrowAngle = endAngle + (beamState.torques[i] > 0 ? Math.PI/2 : -Math.PI/2);
      
      p.line(arrowX, arrowY, arrowX + 8 * Math.cos(arrowAngle + 0.3), arrowY + 8 * Math.sin(arrowAngle + 0.3));
      p.line(arrowX, arrowY, arrowX + 8 * Math.cos(arrowAngle - 0.3), arrowY + 8 * Math.sin(arrowAngle - 0.3));
    }
  }
}

function updateBeamAngle() {
  // Simple physics: beam rotates based on net torque
  const maxAngle = 0.3; // Maximum rotation angle (radians)
  const sensitivity = 0.01;
  
  if (Math.abs(beamState.netTorque) > 0.1) {
    beamState.angle += beamState.netTorque * sensitivity;
    beamState.angle = Math.max(-maxAngle, Math.min(maxAngle, beamState.angle));
  } else {
    // Gradually return to horizontal if nearly balanced
    beamState.angle *= 0.95;
  }
}

function calculateTorques() {
  for (let i = 0; i < 3; i++) {
    const weight = tParams.masses[i] * tParams.gravity;
    const distance = tParams.positions[i] / 100; // Convert cm to m
    beamState.torques[i] = weight * distance;
    beamState.weights[i] = weight;
  }
  
  beamState.netTorque = beamState.torques.reduce((sum, torque) => sum + torque, 0);
  beamState.isBalanced = Math.abs(beamState.netTorque) < 0.5;
  
  updateTorqueDashboard();
  updateTorqueChart();
}

// -------------------- Controls --------------------
function bindTorqueControls() {
  // Mass and position sliders
  for (let i = 0; i < 3; i++) {
    const massSlider = document.getElementById(`mass${i + 1}`);
    const positionSlider = document.getElementById(`position${i + 1}`);
    const massOut = document.getElementById(`mass${i + 1}Out`);
    const positionOut = document.getElementById(`position${i + 1}Out`);
    
    if (massSlider && massOut) {
      massSlider.addEventListener("input", () => {
        tParams.masses[i] = parseFloat(massSlider.value);
        massOut.textContent = tParams.masses[i].toFixed(1);
        calculateTorques();
      });
    }
    
    if (positionSlider && positionOut) {
      positionSlider.addEventListener("input", () => {
        tParams.positions[i] = parseFloat(positionSlider.value);
        positionOut.textContent = tParams.positions[i].toString();
        calculateTorques();
      });
    }
  }
  
  // Gravity slider
  const gravitySlider = document.getElementById("gravity");
  const gravityOut = document.getElementById("gravityOut");
  
  if (gravitySlider && gravityOut) {
    gravitySlider.addEventListener("input", () => {
      tParams.gravity = parseFloat(gravitySlider.value);
      gravityOut.textContent = tParams.gravity.toFixed(1);
      calculateTorques();
    });
  }
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const balanceBtn = document.getElementById("balance");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", () => {
      // Reset to default values
      tParams.masses = [2.0, 4.0, 0.0];
      tParams.positions = [-20, 10, 0];
      tParams.gravity = 9.8;
      
      // Update sliders
      document.getElementById("mass1").value = 2.0;
      document.getElementById("mass2").value = 4.0;
      document.getElementById("mass3").value = 0.0;
      document.getElementById("position1").value = -20;
      document.getElementById("position2").value = 10;
      document.getElementById("position3").value = 0;
      document.getElementById("gravity").value = 9.8;
      
      // Update outputs
      document.getElementById("mass1Out").textContent = "2.0";
      document.getElementById("mass2Out").textContent = "4.0";
      document.getElementById("mass3Out").textContent = "0.0";
      document.getElementById("position1Out").textContent = "-20";
      document.getElementById("position2Out").textContent = "10";
      document.getElementById("position3Out").textContent = "0";
      document.getElementById("gravityOut").textContent = "9.8";
      
      beamState.angle = 0;
      calculateTorques();
    });
  }
  
  if (balanceBtn) {
    balanceBtn.addEventListener("click", () => {
      autoBalance();
    });
  }
}

function autoBalance() {
  // Simple auto-balance: adjust position of weight 2 to balance weight 1
  if (tParams.masses[0] > 0 && tParams.masses[1] > 0) {
    const requiredPosition = -(tParams.masses[0] * tParams.positions[0]) / tParams.masses[1];
    
    // Clamp to valid range
    const clampedPosition = Math.max(-50, Math.min(50, Math.round(requiredPosition / 5) * 5));
    
    tParams.positions[1] = clampedPosition;
    document.getElementById("position2").value = clampedPosition;
    document.getElementById("position2Out").textContent = clampedPosition.toString();
    
    calculateTorques();
  }
}

function updateTorqueDashboard() {
  // Update torque values
  for (let i = 0; i < 3; i++) {
    const torqueEl = document.getElementById(`dashTorque${i + 1}`);
    const weightEl = document.getElementById(`dashWeight${i + 1}`);
    
    if (torqueEl) torqueEl.textContent = beamState.torques[i].toFixed(1);
    if (weightEl) weightEl.textContent = beamState.weights[i].toFixed(1);
  }
  
  // Update net torque
  const netTorqueEl = document.getElementById("dashNetTorque");
  if (netTorqueEl) netTorqueEl.textContent = beamState.netTorque.toFixed(1);
  
  // Update balance status
  updateBalanceStatus();
}

function updateBalanceStatus() {
  const balanceEl = document.getElementById("dashBalance");
  if (balanceEl) {
    const t = TDICT[tLang];
    balanceEl.textContent = beamState.isBalanced ? t.balanced : t.unbalanced;
    balanceEl.className = beamState.isBalanced ? "balanced" : "unbalanced";
  }
}

// -------------------- Chart --------------------
let torqueChart;

function initTorqueChart() {
  const ctx = document.getElementById("torqueChart");
  if (!ctx) return;
  
  torqueChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: ['τ₁', 'τ₂', 'τ₃', 'Net'],
      datasets: [{
        label: 'Torque (N⋅m)',
        data: [0, 0, 0, 0],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ],
        borderColor: [
          '#3b82f6',
          '#ef4444',
          '#10b981',
          '#f59e0b'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Torque (N⋅m)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          display: false
        }
      }
    }
  });
}

function updateTorqueChart() {
  if (!torqueChart) return;
  
  torqueChart.data.datasets[0].data = [
    beamState.torques[0],
    beamState.torques[1],
    beamState.torques[2],
    beamState.netTorque
  ];
  
  torqueChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleTorque();
  applyTorqueI18n();
  bindTorqueControls();
  initP5Torque();
  initTorqueChart();
  calculateTorques();
});
