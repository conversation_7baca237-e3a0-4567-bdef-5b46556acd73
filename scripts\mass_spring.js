// Mass-Spring-Damper (SHM) Simulation
(function () {
  const $ = (id) => document.getElementById(id);

  // Inputs
  const mEl = $('m');
  const kEl = $('k');
  const bEl = $('b');
  const AEl = $('A');
  const phiEl = $('phi');
  const dtEl = $('dt');
  const playPauseBtn = $('playPause');
  const stepBtn = $('stepBtn');
  const resetBtn = $('resetBtn');

  // Outputs
  const mVal = $('mVal');
  const kVal = $('kVal');
  const bVal = $('bVal');
  const AVal = $('AVal');
  const phiVal = $('phiVal');
  const dtVal = $('dtVal');

  const omegaVal = $('omegaVal');
  const zetaVal = $('zetaVal');
  const xOut = $('xVal');
  const vOut = $('vVal');
  const eOut = $('eVal');

  // Canvases
  const canvasSystem = $('canvasSystem');
  const ctxSystem = canvasSystem.getContext('2d');
  const canvasPlots = $('canvasPlots');
  const ctxPlots = canvasPlots.getContext('2d');

  // Utilities: HiDPI scaling
  function setupHiDPICanvas(canvas) {
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    const w = canvas.clientWidth || canvas.offsetWidth;
    const h = canvas.clientHeight || canvas.offsetHeight;
    if (!w || !h) return ratio;
    canvas.width = Math.floor(w * ratio);
    canvas.height = Math.floor(h * ratio);
    const ctx = canvas.getContext('2d');
    ctx.setTransform(ratio, 0, 0, ratio, 0, 0);
    return ratio;
  }

  function resizeAll() {
    setupHiDPICanvas(canvasSystem);
    setupHiDPICanvas(canvasPlots);
    redrawAll();
  }

  let resizeTid;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTid);
    resizeTid = setTimeout(resizeAll, 150);
  });

  // State
  let running = false;
  let t = 0;

  // model parameters
  let m = parseFloat(mEl.value);
  let k = parseFloat(kEl.value);
  let b = parseFloat(bEl.value);
  let A = parseFloat(AEl.value);
  let phiDeg = parseFloat(phiEl.value);
  let dt = parseFloat(dtEl.value);

  // system state (x, v)
  let x = A; // start at amplitude
  let v = 0;

  // samples for plots
  const maxTime = 20;
  let samples = []; // {t, x, v, e}

  // Helpers
  function toRad(deg) { return (deg * Math.PI) / 180; }

  function naturalOmega(m, k) {
    return Math.sqrt(k / m);
  }

  function dampingRatio(m, k, b) {
    const ccrit = 2 * Math.sqrt(k * m);
    return b / ccrit;
  }

  function computeEnergy(m, k, x, v) {
    return 0.5 * k * x * x + 0.5 * m * v * v;
  }

  // Integration (semi-implicit Euler for stability with damping)
  function step(simDt) {
    // Continuous-time: m x'' + b x' + k x = 0
    const a = -(b / m) * v - (k / m) * x;
    v += a * simDt;
    x += v * simDt;

    t += simDt;
    if (t > maxTime) {
      t = 0;
      samples = [];
    }
    const e = computeEnergy(m, k, x, v);
    samples.push({ t, x, v, e });
    updateOutputs();
    redrawAll();
  }

  function updateOutputs() {
    const omega = naturalOmega(m, k);
    const zeta = dampingRatio(m, k, b);
    omegaVal.textContent = omega.toFixed(2);
    zetaVal.textContent = zeta.toFixed(2);
    xOut.textContent = x.toFixed(2);
    vOut.textContent = v.toFixed(2);
    eOut.textContent = computeEnergy(m, k, x, v).toFixed(2);

    mVal.textContent = m.toFixed(1);
    kVal.textContent = k.toFixed(0);
    bVal.textContent = b.toFixed(1);
    AVal.textContent = A.toFixed(2);
    phiVal.textContent = phiDeg.toFixed(0);
    dtVal.textContent = dt.toFixed(2);
  }

  function reset() {
    running = false;
    t = 0;
    m = parseFloat(mEl.value);
    k = parseFloat(kEl.value);
    b = parseFloat(bEl.value);
    A = parseFloat(AEl.value);
    phiDeg = parseFloat(phiEl.value);
    dt = parseFloat(dtEl.value);
    x = A * Math.cos(toRad(phiDeg));
    v = 0;
    samples = [];
    updateOutputs();
    redrawAll();
  }

  let rafId = null;
  function loop() {
    if (!running) return;
    step(dt);
    rafId = requestAnimationFrame(loop);
  }

  function clear(ctx, canvas) {
    ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight);
  }

  function drawSystem() {
    clear(ctxSystem, canvasSystem);
    const w = canvasSystem.clientWidth;
    const h = canvasSystem.clientHeight;

    // Base line
    ctxSystem.strokeStyle = '#9CA3AF';
    ctxSystem.lineWidth = 3;
    ctxSystem.beginPath();
    ctxSystem.moveTo(40, h * 0.7);
    ctxSystem.lineTo(w - 40, h * 0.7);
    ctxSystem.stroke();

    // Fixed wall at left
    ctxSystem.fillStyle = '#6B7280';
    ctxSystem.fillRect(30, h * 0.5, 10, h * 0.4);

    // Spring
    const baseX = 40;
    const originY = h * 0.7;
    const pxPerMeter = 120; // scale x to pixels
    const blockX = baseX + x * pxPerMeter + 150; // offset to keep visible for negative x too
    const blockY = originY - 20;

    // Draw spring as zigzag
    function drawSpring(x1, x2, y) {
      const coils = 8;
      const amp = 10;
      const dx = (x2 - x1) / (coils * 2);
      ctxSystem.strokeStyle = '#7C3AED';
      ctxSystem.lineWidth = 2;
      ctxSystem.beginPath();
      ctxSystem.moveTo(x1, y);
      for (let i = 1; i <= coils * 2; i++) {
        const nx = x1 + i * dx;
        const ny = y + (i % 2 === 0 ? -amp : amp);
        ctxSystem.lineTo(nx, ny);
      }
      ctxSystem.lineTo(x2, y);
      ctxSystem.stroke();
    }

    drawSpring(40, blockX - 25, originY);

    // Block
    ctxSystem.fillStyle = '#7C3AED';
    ctxSystem.fillRect(blockX - 25, blockY - 15, 50, 30);
  }

  function drawPlots() {
    clear(ctxPlots, canvasPlots);
    const w = canvasPlots.clientWidth;
    const h = canvasPlots.clientHeight;

    // Grid
    ctxPlots.strokeStyle = '#E5E7EB';
    ctxPlots.lineWidth = 1;
    for (let xg = 0; xg < w; xg += 40) {
      ctxPlots.beginPath(); ctxPlots.moveTo(xg, 0); ctxPlots.lineTo(xg, h); ctxPlots.stroke();
    }
    for (let yg = 0; yg < h; yg += 40) {
      ctxPlots.beginPath(); ctxPlots.moveTo(0, yg); ctxPlots.lineTo(w, yg); ctxPlots.stroke();
    }

    // Axes
    ctxPlots.strokeStyle = '#9CA3AF';
    ctxPlots.lineWidth = 1.5;
    ctxPlots.beginPath(); ctxPlots.moveTo(40, 0); ctxPlots.lineTo(40, h - 20); ctxPlots.stroke();
    ctxPlots.beginPath(); ctxPlots.moveTo(40, h - 20); ctxPlots.lineTo(w - 10, h - 20); ctxPlots.stroke();

    const left = 40, bottom = h - 20, right = w - 10, top = 10;
    const plotW = right - left;
    const tMax = maxTime;

    // Scales
    const xScale = 120;  // px per meter for plotting
    const vScale = 60;   // px per (m/s)
    const eScale = 4;    // px per Joule (arbitrary)

    // x(t) - purple
    ctxPlots.strokeStyle = '#7C3AED';
    ctxPlots.lineWidth = 2;
    ctxPlots.beginPath();
    samples.forEach((pt, i) => {
      const x = left + (pt.t / tMax) * plotW;
      const y = bottom - pt.x * xScale * 0.25; // reduce amplitude for visibility
      if (i === 0) ctxPlots.moveTo(x, y); else ctxPlots.lineTo(x, y);
    });
    ctxPlots.stroke();

    // v(t) - blue
    ctxPlots.strokeStyle = '#2563EB';
    ctxPlots.lineWidth = 2;
    ctxPlots.beginPath();
    samples.forEach((pt, i) => {
      const x = left + (pt.t / tMax) * plotW;
      const y = bottom - pt.v * vScale * 0.25;
      if (i === 0) ctxPlots.moveTo(x, y); else ctxPlots.lineTo(x, y);
    });
    ctxPlots.stroke();

    // E(t) - green
    ctxPlots.strokeStyle = '#10B981';
    ctxPlots.lineWidth = 2;
    ctxPlots.beginPath();
    samples.forEach((pt, i) => {
      const x = left + (pt.t / tMax) * plotW;
      const y = bottom - pt.e * eScale * 0.05;
      if (i === 0) ctxPlots.moveTo(x, y); else ctxPlots.lineTo(x, y);
    });
    ctxPlots.stroke();

    // Legends
    ctxPlots.fillStyle = '#111827';
    ctxPlots.font = '12px Tajawal, Arial';
    ctxPlots.fillText('x(t)', left + 6, top + 14);
    ctxPlots.fillStyle = '#2563EB';
    ctxPlots.fillText('v(t)', left + 36, top + 14);
    ctxPlots.fillStyle = '#10B981';
    ctxPlots.fillText('E(t)', left + 66, top + 14);
  }

  function redrawAll() {
    drawSystem();
    drawPlots();
  }

  // Initialize
  setupHiDPICanvas(canvasSystem);
  setupHiDPICanvas(canvasPlots);
  reset();

  // Events
  mEl.addEventListener('input', () => {
    m = parseFloat(mEl.value); mVal.textContent = m.toFixed(1); if (!running) { updateOutputs(); redrawAll(); }
  });
  kEl.addEventListener('input', () => {
    k = parseFloat(kEl.value); kVal.textContent = k.toFixed(0); if (!running) { updateOutputs(); redrawAll(); }
  });
  bEl.addEventListener('input', () => {
    b = parseFloat(bEl.value); bVal.textContent = b.toFixed(1); if (!running) { updateOutputs(); redrawAll(); }
  });
  AEl.addEventListener('input', () => {
    A = parseFloat(AEl.value); AVal.textContent = A.toFixed(2); if (!running) { reset(); }
  });
  phiEl.addEventListener('input', () => {
    phiDeg = parseFloat(phiEl.value); phiVal.textContent = phiDeg.toFixed(0); if (!running) { reset(); }
  });
  dtEl.addEventListener('input', () => {
    dt = parseFloat(dtEl.value); dtVal.textContent = dt.toFixed(2);
  });

  playPauseBtn.addEventListener('click', () => {
    running = !running;
    playPauseBtn.textContent = running ? 'إيقاف' : 'تشغيل';
    if (running) {
      loop();
    } else if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  });

  stepBtn.addEventListener('click', () => {
    step(0.05);
  });

  resetBtn.addEventListener('click', () => {
    reset();
  });

  // Accessibility titles
  mEl.title = 'الكتلة m';
  kEl.title = 'ثابت النابض k';
  bEl.title = 'تخميد b';
  AEl.title = 'السعة A';
  phiEl.title = 'الطور φ';
  dtEl.title = 'خطوة الزمن Δt';
})();
