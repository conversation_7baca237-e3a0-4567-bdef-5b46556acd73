<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | مختبر انتقال الحرارة</title>
  <link rel="stylesheet" href="styles/heat_transfer.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="carnot_engine.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">مختبر انتقال الحرارة</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">مختبر انتقال الحرارة: التوصيل والحمل والإشعاع</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف الطرق الثلاث لانتقال الحرارة من خلال محاكاة تفاعلية. شاهد كيف تنتقل الحرارة عبر المواد الصلبة والسوائل والغازات، وتعلم العوامل المؤثرة على معدل انتقال الحرارة.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">التوصيل: انتقال الحرارة عبر المواد الصلبة</li>
            <li data-i18n="t2">الحمل: انتقال الحرارة عبر السوائل والغازات</li>
            <li data-i18n="t3">الإشعاع: انتقال الحرارة عبر الموجات الكهرومغناطيسية</li>
            <li data-i18n="t4">قانون فورييه: q = -kA(dT/dx)</li>
            <li data-i18n="t5">معدل انتقال الحرارة يعتمد على المادة والمساحة وفرق درجة الحرارة</li>
          </ul>
          <div class="formula">q = kA(T₁ - T₂)/L</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot conduction"></i> Conduction</span>
              <span class="legend-item"><i class="dot convection"></i> Convection</span>
              <span class="legend-item"><i class="dot radiation"></i> Radiation</span>
              <span class="legend-item"><i class="dot temperature"></i> Temperature</span>
            </div>
            <div id="p5-container" class="p5-container" aria-label="Heat transfer simulation"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="transfer-mode">
            <h3 data-i18n="mode_title">طريقة انتقال الحرارة</h3>
            <div class="mode-buttons">
              <button class="btn mode active" data-mode="conduction" data-i18n="mode_conduction">التوصيل</button>
              <button class="btn mode" data-mode="convection" data-i18n="mode_convection">الحمل</button>
              <button class="btn mode" data-mode="radiation" data-i18n="mode_radiation">الإشعاع</button>
            </div>
          </div>
          
          <div class="field">
            <label for="tempHot" data-i18n="temp_hot_label">درجة الحرارة العالية T₁ (°C)</label>
            <input type="range" id="tempHot" min="20" max="200" step="5" value="100"/>
            <output id="tempHotOut">100</output>
          </div>
          
          <div class="field">
            <label for="tempCold" data-i18n="temp_cold_label">درجة الحرارة المنخفضة T₂ (°C)</label>
            <input type="range" id="tempCold" min="0" max="100" step="5" value="20"/>
            <output id="tempColdOut">20</output>
          </div>
          
          <div class="field">
            <label for="material" data-i18n="material_label">نوع المادة</label>
            <select id="material">
              <option value="copper" data-i18n="material_copper">نحاس (k=400)</option>
              <option value="aluminum" data-i18n="material_aluminum">ألومنيوم (k=200)</option>
              <option value="steel" data-i18n="material_steel">فولاذ (k=50)</option>
              <option value="glass" data-i18n="material_glass">زجاج (k=1)</option>
              <option value="wood" data-i18n="material_wood">خشب (k=0.1)</option>
            </select>
          </div>
          
          <div class="field">
            <label for="thickness" data-i18n="thickness_label">سمك المادة L (cm)</label>
            <input type="range" id="thickness" min="1" max="20" step="1" value="5"/>
            <output id="thicknessOut">5</output>
          </div>
          
          <div class="field">
            <label for="area" data-i18n="area_label">المساحة A (cm²)</label>
            <input type="range" id="area" min="10" max="100" step="5" value="50"/>
            <output id="areaOut">50</output>
          </div>
          
          <div class="field convection-only" style="display: none;">
            <label for="fluidVelocity" data-i18n="velocity_label">سرعة السائل (m/s)</label>
            <input type="range" id="fluidVelocity" min="0" max="5" step="0.1" value="1"/>
            <output id="fluidVelocityOut">1.0</output>
          </div>
          
          <div class="field radiation-only" style="display: none;">
            <label for="emissivity" data-i18n="emissivity_label">معامل الانبعاث ε</label>
            <input type="range" id="emissivity" min="0.1" max="1" step="0.05" value="0.8"/>
            <output id="emissivityOut">0.80</output>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="start" class="btn" data-i18n="start">بدء المحاكاة</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="heat_flow_title">تدفق الحرارة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_heat_rate">q</span> = <span id="dashHeatRate">0.0</span> W
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_temp_diff">ΔT</span> = <span id="dashTempDiff">80</span> °C
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="material_props_title">خصائص المادة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_conductivity">k</span> = <span id="dashConductivity">400</span> W/m·K
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_resistance">R</span> = <span id="dashResistance">0.001</span> K/W
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="efficiency_title">الكفاءة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_time_constant">τ</span> = <span id="dashTimeConstant">10</span> s
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_steady_state">حالة مستقرة</span>: <span id="dashSteadyState">لا</span>
                </div>
              </div>
            </div>
            
            <canvas id="temperatureChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        فهم الطرق الثلاث لانتقال الحرارة وكيفية تأثير خصائص المادة والهندسة على معدل انتقال الحرارة. اكتشاف أن التوصيل يحدث في المواد الصلبة، والحمل في السوائل والغازات، والإشعاع لا يحتاج لوسط مادي.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/heat_transfer.js"></script>
</body>
</html>
