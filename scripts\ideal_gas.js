/**
 * PhysicsHub - Ideal Gas Law Lab (PV = nRT)
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: n (mol), T (K), V (m^3)
 * - p5.js visualization: container with moving piston and gas particles
 * - Live dashboard: P, V, T, n
 * - Chart.js: PV, VT, or PT curve according to selected mode
 * - Reset/Restart controls; continuous update loop
 */

// -------------------- i18n (AR/EN) --------------------
const IG_DICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    lesson_breadcrumb: "قانون الغاز المثالي",
    lesson_title: "قانون الغاز المثالي: PV = nRT",
    lesson_lead:
      "استكشف العلاقات بين الضغط والحجم ودرجة الحرارة وعدد الجسيمات. يمكنك تثبيت أحد المتغيرات لتوليد مخططات P-V أو V-T أو P-T، ومراقبة سلوك الغاز المثالي في وعاء بمكبس متحرك.",
    theory_title: "المفهوم الأساسي",
    n_label: "عدد المولات n (mol)",
    T_label: "درجة الحرارة T (K)",
    V_label: "الحجم V (m³)",
    mode_label: "وضع الرسم",
    mode_pv: "P مقابل V (ثبوت T,n)",
    mode_vt: "V مقابل T (ثبوت P,n)",
    mode_pt: "P مقابل T (ثبوت V,n)",
    reset: "إعادة ضبط",
    restart: "بدء/إعادة تشغيل",
    lab_title: "المعلمات التفاعلية",
    cta_title: "الهدف التعليمي",
    cta_text:
      "استكشاف العلاقات بين المتغيرات: بويل (P-V)، شارل (V-T)، وجاي-لوساك (P-T) عمليًا عبر ضبط ثبات متغيرين ودراسة تغير الثالث.",
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    lesson_breadcrumb: "Ideal Gas Law",
    lesson_title: "Ideal Gas Law: PV = nRT",
    lesson_lead:
      "Explore relations among pressure, volume, temperature, and amount of gas. Fix one variable to produce P–V, V–T, or P–T plots, and visualize an ideal gas in a piston container.",
    theory_title: "Core Concept",
    n_label: "Moles n (mol)",
    T_label: "Temperature T (K)",
    V_label: "Volume V (m³)",
    mode_label: "Plot mode",
    mode_pv: "P vs V (T,n fixed)",
    mode_vt: "V vs T (P,n fixed)",
    mode_pt: "P vs T (V,n fixed)",
    reset: "Reset",
    restart: "Start/Restart",
    lab_title: "Interactive parameters",
    cta_title: "Learning objective",
    cta_text:
      "Explore Boyle (P–V), Charles (V–T), and Gay-Lussac (P–T) laws by holding two variables constant and varying the third.",
  },
};

let igLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyIGI18n() {
  const t = IG_DICT[igLang];
  document.documentElement.lang = igLang === "ar" ? "ar" : "en";
  document.documentElement.dir = igLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  // Titles for controls
  const ids = [
    ["n", "n_label"],
    ["T", "T_label"],
    ["V", "V_label"],
    ["mode", "mode_label"],
  ];
  ids.forEach(([id, key]) => {
    const el = document.getElementById(id);
    if (el && t[key]) el.setAttribute("title", t[key]);
  });

  // chart labels
  if (igChart) {
    if (igLang === "ar") {
      igChart.options.scales.x.title.text =
        mode.value === "pv" ? "الحجم V (م³)" : mode.value === "vt" ? "درجة الحرارة T (كلفن)" : "درجة الحرارة T (كلفن)";
      igChart.options.scales.y.title.text =
        mode.value === "pv" ? "الضغط P (باسكال)" : mode.value === "vt" ? "الحجم V (م³)" : "الضغط P (باسكال)";
      igChart.data.datasets[0].label =
        mode.value === "pv" ? "P(V)" : mode.value === "vt" ? "V(T)" : "P(T)";
    } else {
      igChart.options.scales.x.title.text =
        mode.value === "pv" ? "Volume V (m³)" : mode.value === "vt" ? "Temperature T (K)" : "Temperature T (K)";
      igChart.options.scales.y.title.text =
        mode.value === "pv" ? "Pressure P (Pa)" : mode.value === "vt" ? "Volume V (m³)" : "Pressure P (Pa)";
      igChart.data.datasets[0].label =
        mode.value === "pv" ? "P(V)" : mode.value === "vt" ? "V(T)" : "P(T)";
    }
    igChart.update();
  }
}

function setupLangToggleIG() {
  const btn = document.getElementById("langToggle");
  if (!btn) return;
  btn.addEventListener("click", () => {
    igLang = igLang === "ar" ? "en" : "ar";
    applyIGI18n();
  });
}

// -------------------- State & DOM --------------------
const ig$ = (id) => document.getElementById(id);
const R = 8.314462618; // J/(mol·K)

const igParams = {
  n: 1.0,
  T: 300,
  V: 0.1,
};

function pressureFromState(n, T, V) {
  return (n * R * T) / V; // Pa
}

// Dashboard update
function updateIGDashboard() {
  const P = pressureFromState(igParams.n, igParams.T, igParams.V);
  ig$("dashP").textContent = Math.round(P).toString();
  ig$("dashV").textContent = igParams.V.toFixed(2);
  ig$("dashT").textContent = Math.round(igParams.T).toString();
  ig$("dashN").textContent = igParams.n.toFixed(2);
}

// -------------------- p5.js: piston container --------------------
let igP5;

function initP5IG() {
  const container = document.getElementById("p5-container");

  const s = (p) => {
    let w = container.clientWidth;
    let h = container.clientHeight;

    // simple gas particles
    const maxParticles = 60;
    let particles = [];

    function resetParticles() {
      particles = [];
      for (let i = 0; i < maxParticles; i++) {
        particles.push({
          x: p.random(60, w - 20),
          y: p.random(20, h - 60),
          vx: p.random(-1, 1) * (igParams.T / 300) * 60,
          vy: p.random(-1, 1) * (igParams.T / 300) * 60,
        });
      }
    }

    p.setup = function () {
      const cnv = p.createCanvas(w, h);
      cnv.parent(container);
      p.frameRate(60);
      resetParticles();
    };

    p.windowResized = function () {
      w = container.clientWidth;
      h = container.clientHeight;
      p.resizeCanvas(w, h);
      resetParticles();
    };

    p.draw = function () {
      p.background(8, 12, 30);

      // Map V to piston height (bigger volume => higher chamber)
      const minH = 100;
      const maxH = h - 40;
      const vNorm = Math.min(1, Math.max(0, (igParams.V - 0.01) / (1.0 - 0.01)));
      const chamberH = minH + vNorm * (maxH - minH);

      // container walls
      const left = 40, right = w - 40, top = 20, bot = top + chamberH;
      p.noFill();
      p.stroke(120, 160, 255, 160);
      p.strokeWeight(2);
      // left, right walls
      p.line(left, top, left, bot);
      p.line(right, top, right, bot);
      // top (fixed)
      p.line(left, top, right, top);
      // piston (bottom)
      p.stroke(255, 92, 92);
      p.line(left, bot, right, bot);

      // particles
      p.noStroke();
      p.fill(123, 211, 137);
      const tempScale = Math.sqrt(igParams.T / 300);
      for (const pt of particles) {
        // move
        pt.x += pt.vx * tempScale * 0.016;
        pt.y += pt.vy * tempScale * 0.016;
        // collisions with walls
        if (pt.x < left + 6) { pt.x = left + 6; pt.vx *= -1; }
        if (pt.x > right - 6) { pt.x = right - 6; pt.vx *= -1; }
        if (pt.y < top + 6) { pt.y = top + 6; pt.vy *= -1; }
        if (pt.y > bot - 6) { pt.y = bot - 6; pt.vy *= -1; }
        // draw
        p.circle(pt.x, pt.y, 6);
      }

      // render P as a bar on left
      const P = pressureFromState(igParams.n, igParams.T, igParams.V);
      const Pnorm = Math.min(1, P / 200000); // normalize to 200 kPa scale
      p.fill(58, 160, 255, 160);
      p.noStroke();
      p.rect(10, bot - Pnorm * (bot - top), 12, Pnorm * (bot - top));
    };
  };

  if (igP5) igP5.remove();
  igP5 = new p5(s);
}

// -------------------- Chart.js --------------------
let igChart;
function initIGChart() {
  const ctx = document.getElementById("chart");
  const m = ig$("mode").value;
  const xTitleAR = m === "pv" ? "الحجم V (م³)" : "درجة الحرارة T (كلفن)";
  const yTitleAR = m === "pv" ? "الضغط P (باسكال)" : m === "vt" ? "الحجم V (م³)" : "الضغط P (باسكال)";
  const xTitleEN = m === "pv" ? "Volume V (m³)" : "Temperature T (K)";
  const yTitleEN = m === "pv" ? "Pressure P (Pa)" : m === "vt" ? "Volume V (m³)" : "Pressure P (Pa)";

  igChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: [],
      datasets: [
        {
          label: m === "pv" ? "P(V)" : m === "vt" ? "V(T)" : "P(T)",
          data: [],
          borderColor: "rgba(58,160,255,1)",
          backgroundColor: "rgba(58,160,255,0.2)",
          tension: 0.25,
          pointRadius: 0,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          title: { display: true, text: igLang === "ar" ? xTitleAR : xTitleEN },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          title: { display: true, text: igLang === "ar" ? yTitleAR : yTitleEN },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
      plugins: {
        legend: { labels: { color: "#e9eefb" } },
      },
    },
  });
}

function igChartReset() {
  if (!igChart) return;
  igChart.data.labels = [];
  igChart.data.datasets[0].data = [];
  igChart.update("none");
}

function pushIGChartPoint() {
  if (!igChart) return;
  const m = ig$("mode").value;
  // push current state pair to chart
  if (m === "pv") {
    igChart.data.labels.push(igParams.V.toFixed(3));
    igChart.data.datasets[0].data.push(pressureFromState(igParams.n, igParams.T, igParams.V));
  } else if (m === "vt") {
    igChart.data.labels.push(Math.round(igParams.T).toString());
    igChart.data.datasets[0].data.push(igParams.V);
  } else if (m === "pt") {
    igChart.data.labels.push(Math.round(igParams.T).toString());
    igChart.data.datasets[0].data.push(pressureFromState(igParams.n, igParams.T, igParams.V));
  }
  if (igChart.data.labels.length > 300) {
    igChart.data.labels.shift();
    igChart.data.datasets[0].data.shift();
  }
  igChart.update("none");
}

// -------------------- Controls --------------------
function bindIGControls() {
  const n = ig$("n");
  const T = ig$("T");
  const V = ig$("V");
  const modeSel = ig$("mode");

  const nOut = ig$("nOut");
  const TOut = ig$("TOut");
  const VOut = ig$("VOut");

  n.addEventListener("input", () => {
    igParams.n = parseFloat(n.value);
    nOut.textContent = igParams.n.toFixed(1);
    updateIGDashboard();
  });
  T.addEventListener("input", () => {
    igParams.T = parseFloat(T.value);
    TOut.textContent = Math.round(igParams.T).toString();
    updateIGDashboard();
  });
  V.addEventListener("input", () => {
    igParams.V = parseFloat(V.value);
    VOut.textContent = igParams.V.toFixed(2);
    updateIGDashboard();
  });

  modeSel.addEventListener("change", () => {
    igChartReset();
    // update labels to new mode
    applyIGI18n();
  });

  ig$("reset").addEventListener("click", () => {
    igParams.n = 1.0; n.value = 1.0; n.dispatchEvent(new Event("input"));
    igParams.T = 300; T.value = 300; T.dispatchEvent(new Event("input"));
    igParams.V = 0.1; V.value = 0.1; V.dispatchEvent(new Event("input"));
    igChartReset();
  });

  ig$("restart").addEventListener("click", () => {
    igChartReset();
  });

  // initialize outputs
  n.dispatchEvent(new Event("input"));
  T.dispatchEvent(new Event("input"));
  V.dispatchEvent(new Event("input"));
}

// -------------------- Loop --------------------
function igTick() {
  // periodically push points to chart to show the relation as user drags sliders
  pushIGChartPoint();
  updateIGDashboard();
  requestAnimationFrame(igTick);
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleIG();
  applyIGI18n();
  bindIGControls();
  initP5IG();
  initIGChart();
  updateIGDashboard();
  igTick();
});
