:root {
  --bg: #0b1020;
  --card: #121933;
  --accent: #10b981;
  --accent-2: #34d399;
  --text: #e9eefb;
  --muted: #a9b4d0;
  --danger: #ff5c5c;
  --success: #4ade80;
  --hot-object-color: #dc2626;
  --cold-object-color: #3b82f6;
  --calorimeter-color: #6b7280;
  --heat-flow-color: #f59e0b;
  --shadow: 0 10px 30px rgba(0,0,0,0.35);
}

* { box-sizing: border-box; }
html, body {
  margin: 0;
  padding: 0;
  background: linear-gradient(180deg, #0b1020, #0b1329 40%, #0b1020);
  color: var(--text);
  font-family: "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, "Noto Ku<PERSON> Arabic", "Cairo", sans-serif;
}

a { color: var(--accent); text-decoration: none; }
a:hover { text-decoration: underline; }

.site-header {
  position: sticky; top: 0; z-index: 1000;
  background: rgba(11,16,32,0.7);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255,255,255,0.06);
}

.navbar {
  display: flex; justify-content: space-between; align-items: center;
  padding: 1rem 2rem; max-width: 1200px; margin: 0 auto;
}

.brand { display: flex; flex-direction: column; }
.brand-title { font-size: 1.5rem; font-weight: 700; color: var(--accent); }
.brand-sub { font-size: 0.75rem; color: var(--muted); margin-top: -2px; }

.nav-links {
  display: flex; list-style: none; gap: 1.5rem; margin: 0; padding: 0;
}
.nav-links a { color: var(--text); font-weight: 500; }
.nav-links a:hover { color: var(--accent); }

.lang-toggle {
  background: var(--accent); color: white; border: none;
  padding: 0.5rem 1rem; border-radius: 6px; font-weight: 500;
  cursor: pointer; transition: all 0.2s;
}
.lang-toggle:hover { background: var(--accent-2); }

.container { max-width: 1200px; margin: 0 auto; padding: 2rem; }

.breadcrumbs {
  display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;
  font-size: 0.875rem; color: var(--muted);
}
.breadcrumbs a { color: var(--accent); }
.sep { color: var(--muted); }

.lesson-hero {
  text-align: center; margin-bottom: 3rem;
}
.lesson-hero h1 {
  font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent), var(--accent-2));
  -webkit-background-clip: text; -webkit-text-fill-color: transparent;
  background-clip: text;
}
.lead {
  font-size: 1.125rem; line-height: 1.6; color: var(--muted);
  max-width: 800px; margin: 0 auto;
}

.card {
  background: var(--card); border-radius: 12px; padding: 2rem;
  margin-bottom: 2rem; box-shadow: var(--shadow);
  border: 1px solid rgba(255,255,255,0.05);
}

.card h2 {
  font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem;
  color: var(--accent);
}

.theory-grid {
  display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;
  align-items: start;
}

.bullets {
  list-style: none; padding: 0; margin: 0;
}
.bullets li {
  padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.05);
  font-size: 0.95rem; line-height: 1.4;
}
.bullets li:last-child { border-bottom: none; }

.formula {
  background: rgba(16,185,129,0.1); border: 1px solid var(--accent);
  padding: 1rem; border-radius: 8px; margin-top: 1rem;
  font-family: 'Courier New', monospace; font-size: 1.1rem;
  text-align: center; color: var(--accent);
}

.diagram-box {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.legend {
  display: flex; gap: 1rem; margin-bottom: 1rem; flex-wrap: wrap;
}
.legend-item {
  display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem;
}
.dot {
  width: 12px; height: 12px; border-radius: 50%; display: inline-block;
}
.dot.hot-object { background: var(--hot-object-color); }
.dot.cold-object { background: var(--cold-object-color); }
.dot.calorimeter { background: var(--calorimeter-color); }
.dot.heat-flow { background: var(--heat-flow-color); }

.p5-container {
  width: 100%; height: 300px; border-radius: 6px;
  background: rgba(0,0,0,0.2); position: relative;
}

.lab-grid {
  display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;
}

.lab-controls {
  display: flex; flex-direction: column; gap: 1.5rem;
}

.material-selector {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.material-selector h3 {
  margin: 0 0 1rem 0; font-size: 1rem; color: var(--accent);
}

.material-buttons {
  display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem;
}

.material-properties {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.material-properties h3 {
  margin: 0 0 1rem 0; font-size: 1rem; color: var(--accent);
}

.property-info {
  display: flex; flex-direction: column; gap: 0.5rem;
}

.property-item {
  display: flex; justify-content: space-between; align-items: center;
  padding: 0.5rem; background: rgba(255,255,255,0.05); border-radius: 4px;
  font-size: 0.875rem;
}

.field {
  display: flex; flex-direction: column; gap: 0.5rem;
}
.field label {
  font-weight: 500; color: var(--text); font-size: 0.875rem;
}

input[type="range"] {
  width: 100%; height: 6px; border-radius: 3px;
  background: rgba(255,255,255,0.1); outline: none;
  -webkit-appearance: none; appearance: none;
}
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none; appearance: none;
  width: 18px; height: 18px; border-radius: 50%;
  background: var(--accent); cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
input[type="range"]::-moz-range-thumb {
  width: 18px; height: 18px; border-radius: 50%;
  background: var(--accent); cursor: pointer; border: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

output {
  font-weight: 600; color: var(--accent); font-size: 0.875rem;
}

.btn {
  padding: 0.75rem 1.5rem; border-radius: 6px; border: none;
  font-weight: 500; cursor: pointer; transition: all 0.2s;
  background: var(--accent); color: white;
}
.btn:hover { background: var(--accent-2); transform: translateY(-1px); }
.btn.outline {
  background: transparent; border: 1px solid var(--accent); color: var(--accent);
}
.btn.outline:hover { background: var(--accent); color: white; }
.btn.material {
  font-size: 0.8rem; padding: 0.5rem 1rem;
  background: rgba(255,255,255,0.1); color: var(--text);
}
.btn.material.active {
  background: var(--accent); color: white;
}
.btn.material:hover {
  background: var(--accent-2); color: white;
}

.buttons {
  display: flex; gap: 1rem;
}

.lab-visuals {
  display: flex; flex-direction: column;
}

.dash {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.dash-section {
  margin-bottom: 1.5rem;
}
.dash-section:last-of-type {
  margin-bottom: 1rem;
}

.dash-section h4 {
  margin: 0 0 0.5rem 0; font-size: 1rem; color: var(--accent);
}

.dash-row {
  display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem; margin-bottom: 0.5rem;
}
.dash-row:last-child { margin-bottom: 0; }

.dash-item {
  text-align: center; font-size: 0.875rem;
}
.dash-item span:first-child {
  font-weight: 600; color: var(--muted);
}
.dash-item span:last-child {
  font-weight: 700; color: var(--accent); font-size: 1rem;
}

.site-footer {
  background: var(--card); border-top: 1px solid rgba(255,255,255,0.05);
  padding: 2rem; margin-top: 3rem;
}

.footer-content {
  max-width: 1200px; margin: 0 auto;
  display: flex; justify-content: space-between; align-items: center;
  flex-wrap: wrap; gap: 1rem; font-size: 0.875rem;
}

.footer-content strong { color: var(--accent); }
.footer-content a { color: var(--accent); }

/* Material-specific styles */
.water-material {
  border-left: 4px solid #3b82f6;
}

.aluminum-material {
  border-left: 4px solid #6b7280;
}

.copper-material {
  border-left: 4px solid #f59e0b;
}

.iron-material {
  border-left: 4px solid #dc2626;
}

/* Energy balance indicators */
.balanced {
  color: var(--success) !important;
}

.unbalanced {
  color: var(--danger) !important;
}

@media (max-width: 768px) {
  .theory-grid, .lab-grid {
    grid-template-columns: 1fr;
  }
  .navbar {
    flex-direction: column; gap: 1rem; padding: 1rem;
  }
  .nav-links {
    gap: 1rem;
  }
  .lesson-hero h1 {
    font-size: 2rem;
  }
  .container {
    padding: 1rem;
  }
  .footer-content {
    flex-direction: column; text-align: center;
  }
  .material-buttons {
    grid-template-columns: 1fr;
  }
}
