/**
 * PhysicsHub - Energy Conservation on Roller Coaster Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: mass, friction, gravity, track design
 * - p5.js visualization: roller coaster track with moving cart
 * - Live dashboard: PE, KE, Total Energy, speed, height
 * - Chart.js: energy vs time/position
 * - Track presets: simple, loop, hills, custom
 */

// -------------------- i18n (AR/EN) --------------------
const EDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "حفظ الطاقة على الأفعوانية",
    lesson_title: "حفظ الطاقة على الأفعوانية: تحولات الطاقة الحركية والكامنة",
    lesson_lead: "صمم مسار أفعوانية مخصص وشاهد كيف تتحول الطاقة الكامنة إلى حركية والعكس. اكتشف مبدأ حفظ الطاقة الميكانيكية وتأثير الاحتكاك على الطاقة الكلية للنظام.",
    theory_title: "المفهوم الأساسي",
    t1: "الطاقة الكامنة: PE = mgh (حيث h الارتفاع عن المرجع)",
    t2: "الطاقة الحركية: KE = ½mv² (حيث v السرعة)",
    t3: "الطاقة الميكانيكية الكلية: E = PE + KE",
    t4: "في غياب الاحتكاك: E = ثابت (مبدأ حفظ الطاقة)",
    t5: "مع الاحتكاك: E تتناقص تدريجياً",
    lab_title: "المعلمات التفاعلية",
    mass_label: "كتلة العربة m (kg)",
    friction_label: "معامل الاحتكاك μ",
    g_label: "الجاذبية g (m/s²)",
    track_design_label: "تصميم المسار",
    track_simple: "بسيط",
    track_loop: "حلقة",
    track_hills: "تلال",
    track_custom: "مخصص",
    reset: "إعادة ضبط",
    start: "بدء المحاكاة",
    dash_pe: "PE",
    dash_ke: "KE",
    dash_total: "Total",
    dash_speed: "v",
    dash_height: "h",
    cta_title: "الهدف التعليمي",
    cta_text: "إثبات مبدأ حفظ الطاقة الميكانيكية: عندما تقل الطاقة الكامنة تزداد الحركية والعكس، والطاقة الكلية تبقى ثابتة في غياب الاحتكاك. مع الاحتكاك، نلاحظ تناقص الطاقة الكلية تدريجياً.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Energy Conservation on Roller Coaster",
    lesson_title: "Energy Conservation on Roller Coaster: Kinetic and Potential Energy Transformations",
    lesson_lead: "Design a custom roller coaster track and watch how potential energy transforms to kinetic energy and vice versa. Discover the principle of mechanical energy conservation and the effect of friction on total system energy.",
    theory_title: "Core Concept",
    t1: "Potential Energy: PE = mgh (where h is height above reference)",
    t2: "Kinetic Energy: KE = ½mv² (where v is velocity)",
    t3: "Total Mechanical Energy: E = PE + KE",
    t4: "Without friction: E = constant (conservation of energy principle)",
    t5: "With friction: E decreases gradually",
    lab_title: "Interactive Parameters",
    mass_label: "Cart mass m (kg)",
    friction_label: "Friction coefficient μ",
    g_label: "Gravity g (m/s²)",
    track_design_label: "Track Design",
    track_simple: "Simple",
    track_loop: "Loop",
    track_hills: "Hills",
    track_custom: "Custom",
    reset: "Reset",
    start: "Start Simulation",
    dash_pe: "PE",
    dash_ke: "KE",
    dash_total: "Total",
    dash_speed: "v",
    dash_height: "h",
    cta_title: "Learning Objective",
    cta_text: "Demonstrate the principle of mechanical energy conservation: when potential energy decreases, kinetic energy increases and vice versa, with total energy remaining constant without friction. With friction, observe gradual decrease in total energy.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let eLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyEnergyI18n() {
  const t = EDICT[eLang];
  document.documentElement.lang = eLang === "ar" ? "ar" : "en";
  document.documentElement.dir = eLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
}

function setupLangToggleEnergy() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      eLang = eLang === "ar" ? "en" : "ar";
      applyEnergyI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let eParams = {
  mass: 1.0,
  friction: 0.0,
  gravity: 9.8,
  trackType: "simple"
};

// -------------------- Track Definitions --------------------
const trackPresets = {
  simple: [
    {x: 0, y: 100}, {x: 100, y: 100}, {x: 200, y: 200}, 
    {x: 300, y: 150}, {x: 400, y: 250}, {x: 500, y: 200}
  ],
  loop: [
    {x: 0, y: 100}, {x: 100, y: 100}, {x: 150, y: 80}, 
    {x: 200, y: 50}, {x: 250, y: 80}, {x: 300, y: 100},
    {x: 400, y: 200}, {x: 500, y: 180}
  ],
  hills: [
    {x: 0, y: 50}, {x: 80, y: 150}, {x: 160, y: 80}, 
    {x: 240, y: 180}, {x: 320, y: 100}, {x: 400, y: 200}, 
    {x: 500, y: 150}
  ],
  custom: [
    {x: 0, y: 80}, {x: 100, y: 120}, {x: 200, y: 60}, 
    {x: 300, y: 180}, {x: 400, y: 100}, {x: 500, y: 220}
  ]
};

// -------------------- Simulation State --------------------
let cart = {
  x: 0, y: 0, vx: 0, vy: 0, 
  trackIndex: 0, trackProgress: 0,
  pe: 0, ke: 0, totalE: 0
};

let isRunning = false;
let track = trackPresets.simple;
let energyData = [];

// -------------------- p5.js Sketch --------------------
let p5Instance;

function initP5Energy() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  p5Instance = new p5((p) => {
    p.setup = () => {
      const canvas = p.createCanvas(container.offsetWidth, container.offsetHeight);
      canvas.parent(container);
      resetCart();
    };

    p.windowResized = () => {
      p.resizeCanvas(container.offsetWidth, container.offsetHeight);
    };

    p.draw = () => {
      p.background(15, 20, 35);
      
      // Draw track
      drawTrack(p);
      
      // Draw cart
      drawCart(p);
      
      // Draw energy bars
      drawEnergyBars(p);
      
      // Update simulation
      if (isRunning) {
        updateCart();
      }
    };
  });
}

function drawTrack(p) {
  p.stroke(255, 107, 53);
  p.strokeWeight(4);
  p.noFill();
  
  p.beginShape();
  for (let point of track) {
    p.vertex(point.x, point.y);
  }
  p.endShape();
  
  // Draw track points
  p.fill(255, 107, 53, 100);
  p.noStroke();
  for (let point of track) {
    p.circle(point.x, point.y, 8);
  }
}

function drawCart(p) {
  p.fill(255, 107, 53);
  p.noStroke();
  p.circle(cart.x, cart.y, 16);
  
  // Draw velocity vector
  if (cart.vx !== 0 || cart.vy !== 0) {
    p.stroke(247, 147, 30);
    p.strokeWeight(2);
    const scale = 5;
    p.line(cart.x, cart.y, 
           cart.x + cart.vx * scale, 
           cart.y + cart.vy * scale);
  }
}

function drawEnergyBars(p) {
  const barWidth = 20;
  const maxHeight = 100;
  const x = p.width - 80;
  const y = 50;
  
  // Calculate energy values
  const maxE = cart.totalE || 1;
  const peHeight = (cart.pe / maxE) * maxHeight;
  const keHeight = (cart.ke / maxE) * maxHeight;
  
  // PE bar
  p.fill(59, 130, 246);
  p.noStroke();
  p.rect(x, y + maxHeight - peHeight, barWidth, peHeight);
  
  // KE bar
  p.fill(239, 68, 68);
  p.rect(x + 25, y + maxHeight - keHeight, barWidth, keHeight);
  
  // Labels
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text("PE", x + barWidth/2, y + maxHeight + 15);
  p.text("KE", x + 25 + barWidth/2, y + maxHeight + 15);
}

function resetCart() {
  cart.x = track[0].x;
  cart.y = track[0].y;
  cart.vx = 0;
  cart.vy = 0;
  cart.trackIndex = 0;
  cart.trackProgress = 0;
  
  // Calculate initial energy
  const height = (300 - cart.y) / 100; // Convert pixels to meters
  cart.pe = eParams.mass * eParams.gravity * height;
  cart.ke = 0;
  cart.totalE = cart.pe + cart.ke;
  
  energyData = [];
  updateEnergyDashboard();
}

function updateCart() {
  if (cart.trackIndex >= track.length - 1) {
    isRunning = false;
    return;
  }
  
  // Simple physics simulation
  const dt = 0.02;
  const currentPoint = track[cart.trackIndex];
  const nextPoint = track[cart.trackIndex + 1];
  
  // Calculate track direction
  const dx = nextPoint.x - currentPoint.x;
  const dy = nextPoint.y - currentPoint.y;
  const distance = Math.sqrt(dx*dx + dy*dy);
  
  if (distance > 0) {
    const dirX = dx / distance;
    const dirY = dy / distance;
    
    // Apply gravity component along track
    const gravityForce = eParams.gravity * dirY;
    const frictionForce = eParams.friction * eParams.gravity;
    
    const acceleration = gravityForce - frictionForce;
    
    // Update velocity
    const speed = Math.sqrt(cart.vx*cart.vx + cart.vy*cart.vy);
    const newSpeed = Math.max(0, speed + acceleration * dt);
    
    cart.vx = dirX * newSpeed;
    cart.vy = dirY * newSpeed;
    
    // Update position
    cart.x += cart.vx * dt * 10; // Scale for visualization
    cart.y += cart.vy * dt * 10;
    
    // Check if reached next point
    const distToNext = Math.sqrt((cart.x - nextPoint.x)**2 + (cart.y - nextPoint.y)**2);
    if (distToNext < 10) {
      cart.trackIndex++;
      if (cart.trackIndex < track.length) {
        cart.x = track[cart.trackIndex].x;
        cart.y = track[cart.trackIndex].y;
      }
    }
  }
  
  // Update energy
  const height = (300 - cart.y) / 100; // Convert pixels to meters
  cart.pe = eParams.mass * eParams.gravity * height;
  const speed = Math.sqrt(cart.vx*cart.vx + cart.vy*cart.vy) / 10; // Scale back
  cart.ke = 0.5 * eParams.mass * speed * speed;
  
  // Apply energy loss due to friction
  if (eParams.friction > 0) {
    cart.totalE *= (1 - eParams.friction * dt);
  }
  
  energyData.push({
    time: energyData.length * dt,
    pe: cart.pe,
    ke: cart.ke,
    total: cart.pe + cart.ke
  });
  
  updateEnergyDashboard();
  updateEnergyChart();
}

// -------------------- Controls --------------------
function bindEnergyControls() {
  const massSlider = document.getElementById("mass");
  const frictionSlider = document.getElementById("friction");
  const gravitySlider = document.getElementById("gravity");
  
  const massOut = document.getElementById("massOut");
  const frictionOut = document.getElementById("frictionOut");
  const gravityOut = document.getElementById("gravityOut");
  
  if (massSlider && massOut) {
    massSlider.addEventListener("input", () => {
      eParams.mass = parseFloat(massSlider.value);
      massOut.textContent = eParams.mass.toFixed(1);
      resetCart();
    });
  }
  
  if (frictionSlider && frictionOut) {
    frictionSlider.addEventListener("input", () => {
      eParams.friction = parseFloat(frictionSlider.value);
      frictionOut.textContent = eParams.friction.toFixed(3);
      resetCart();
    });
  }
  
  if (gravitySlider && gravityOut) {
    gravitySlider.addEventListener("input", () => {
      eParams.gravity = parseFloat(gravitySlider.value);
      gravityOut.textContent = eParams.gravity.toFixed(1);
      resetCart();
    });
  }
  
  // Track presets
  document.querySelectorAll("[data-track]").forEach(btn => {
    btn.addEventListener("click", () => {
      const trackType = btn.getAttribute("data-track");
      if (trackPresets[trackType]) {
        track = [...trackPresets[trackType]];
        eParams.trackType = trackType;
        resetCart();
      }
    });
  });
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", resetCart);
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      isRunning = !isRunning;
      startBtn.textContent = isRunning ? 
        (eLang === "ar" ? "إيقاف" : "Stop") : 
        (eLang === "ar" ? "بدء المحاكاة" : "Start Simulation");
    });
  }
}

function updateEnergyDashboard() {
  const dashPE = document.getElementById("dashPE");
  const dashKE = document.getElementById("dashKE");
  const dashTotal = document.getElementById("dashTotal");
  const dashSpeed = document.getElementById("dashSpeed");
  const dashHeight = document.getElementById("dashHeight");
  
  if (dashPE) dashPE.textContent = cart.pe.toFixed(2);
  if (dashKE) dashKE.textContent = cart.ke.toFixed(2);
  if (dashTotal) dashTotal.textContent = (cart.pe + cart.ke).toFixed(2);
  
  const speed = Math.sqrt(cart.vx*cart.vx + cart.vy*cart.vy) / 10;
  if (dashSpeed) dashSpeed.textContent = speed.toFixed(2);
  
  const height = (300 - cart.y) / 100;
  if (dashHeight) dashHeight.textContent = height.toFixed(2);
}

// -------------------- Chart --------------------
let energyChart;

function initEnergyChart() {
  const ctx = document.getElementById("energyChart");
  if (!ctx) return;
  
  energyChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'PE',
          data: [],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        },
        {
          label: 'KE',
          data: [],
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4
        },
        {
          label: 'Total',
          data: [],
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          title: { display: true, text: 'Time (s)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Energy (J)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          labels: { color: '#a9b4d0' }
        }
      }
    }
  });
}

function updateEnergyChart() {
  if (!energyChart || energyData.length === 0) return;
  
  const maxPoints = 100;
  const data = energyData.slice(-maxPoints);
  
  energyChart.data.labels = data.map(d => d.time.toFixed(1));
  energyChart.data.datasets[0].data = data.map(d => d.pe);
  energyChart.data.datasets[1].data = data.map(d => d.ke);
  energyChart.data.datasets[2].data = data.map(d => d.total);
  
  energyChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleEnergy();
  applyEnergyI18n();
  bindEnergyControls();
  initP5Energy();
  initEnergyChart();
  resetCart();
});
