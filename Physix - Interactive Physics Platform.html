<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيزيكس - منصة الفيزياء التفاعلية | Physix - Interactive Physics Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Poppins:wght@300;400;500;600&display=swap');
        
        :root {
            --primary: #4F46E5;
            --secondary: #10B981;
            --dark: #1F2937;
            --light: #F3F4F6;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
        }
        
        .en {
            font-family: 'Poppins', sans-serif;
            direction: ltr;
            text-align: left;
        }
        
        .ar {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        .simulation-window {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .lab-equipment {
            transition: all 0.3s ease;
        }
        
        .lab-equipment:hover {
            transform: translateY(-5px);
            filter: drop-shadow(0 5px 5px rgba(0,0,0,0.1));
        }
        
        .language-switch {
            transition: all 0.3s ease;
        }
        
        .language-switch:hover {
            transform: scale(1.05);
        }
        
        .concept-card {
            transition: all 0.3s ease;
            perspective: 1000px;
        }
        
        .concept-card:hover {
            transform: translateY(-5px) rotateY(5deg);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        /* Animation for interactive elements */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .interactive-element {
            animation: pulse 2s infinite;
        }
        
        /* Dark mode toggle styles */
        .dark-mode {
            background-color: #1a202c;
            color: #f7fafc;
        }
        
        .dark-mode .bg-white {
            background-color: #2d3748 !important;
            color: #f7fafc !important;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Language Switch Button -->
    <div class="fixed top-4 right-4 z-50">
        <button id="languageSwitch" class="language-switch bg-indigo-600 text-white px-4 py-2 rounded-full shadow-lg flex items-center">
            <i class="fas fa-language mr-2"></i>
            <span class="en">العربية</span>
            <span class="ar hidden">English</span>
        </button>
    </div>
    
    <!-- Dark Mode Toggle -->
    <div class="fixed top-4 left-4 z-50">
        <button id="darkModeToggle" class="bg-gray-800 text-white px-4 py-2 rounded-full shadow-lg">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- Header -->
    <header class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-6 shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <i class="fas fa-atom text-3xl mr-3"></i>
                    <h1 class="text-3xl font-bold ar">فيزيكس</h1>
                    <h1 class="text-3xl font-bold en hidden">Physix</h1>
                </div>
                <nav class="flex space-x-6 ar:space-x-reverse">
                    <a href="#home" class="hover:text-indigo-200 font-medium ar">الرئيسية</a>
                    <a href="#simulations" class="hover:text-indigo-200 font-medium ar">المحاكاة</a>
                    <a href="#labs" class="hover:text-indigo-200 font-medium ar">المختبرات</a>
                    <a href="#courses" class="hover:text-indigo-200 font-medium ar">الدروس</a>
                    <a href="#about" class="hover:text-indigo-200 font-medium ar">عن المنصة</a>
                </nav>
                <nav class="hidden en space-x-6">
                    <a href="#home" class="hover:text-indigo-200 font-medium">Home</a>
                    <a href="#simulations" class="hover:text-indigo-200 font-medium">Simulations</a>
                    <a href="#labs" class="hover:text-indigo-200 font-medium">Labs</a>
                    <a href="#courses" class="hover:text-indigo-200 font-medium">Courses</a>
                    <a href="#about" class="hover:text-indigo-200 font-medium">About</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-8 md:mb-0">
                    <h2 class="text-4xl font-bold mb-4 ar">استكشف عالم الفيزياء بطريقة تفاعلية</h2>
                    <h2 class="text-4xl font-bold mb-4 en hidden">Explore Physics in an Interactive Way</h2>
                    
                    <p class="text-gray-600 mb-6 ar">
                        منصة تعليمية متكاملة تحول المفاهيم الفيزيائية المعقدة إلى تجارب تفاعلية ومحاكاة حية. تعلم الميكانيكا، الديناميكا الحرارية والكهرومغناطيسية بطريقة ممتعة.
                    </p>
                    <p class="text-gray-600 mb-6 en hidden">
                        An integrated educational platform that transforms complex physics concepts into interactive experiments and live simulations. Learn mechanics, thermodynamics and electromagnetism in a fun way.
                    </p>
                    
                    <div class="flex space-x-4 ar:space-x-reverse">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">ابدأ التعلم</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow ar">جولة في المنصة</button>
                        
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">Start Learning</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow en hidden">Platform Tour</button>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="simulation-window p-4">
                        <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                            <div class="bg-gray-200 h-6 flex items-center px-2">
                                <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                            </div>
                            <div class="p-4 h-64 flex items-center justify-center">
                                <canvas id="heroSimulation" class="w-full h-full"></canvas>
                            </div>
                            <div class="bg-gray-100 px-4 py-2 flex justify-between items-center">
                                <span class="text-sm text-gray-600 ar">محاكاة حركة المقذوفات</span>
                                <span class="text-sm text-gray-600 en hidden">Projectile Motion Simulation</span>
                                <button class="text-indigo-600 text-sm flex items-center">
                                    <i class="fas fa-play mr-1 ar"></i>
                                    <span class="ar">تشغيل</span>
                                    <i class="fas fa-play ml-1 en hidden"></i>
                                    <span class="en hidden">Play</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Physics Branches Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 ar">استكشف فروع الفيزياء</h2>
            <h2 class="text-3xl font-bold text-center mb-12 en hidden">Explore Physics Branches</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Classical Mechanics Card -->
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-indigo-100 p-4 flex justify-center">
                        <i class="fas fa-weight-hanging text-5xl text-indigo-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الميكانيكا الكلاسيكية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Classical Mechanics</h3>
                        <p class="text-gray-600 mb-4 ar">
                            فهم حركة الأجسام تحت تأثير القوى. استكشف قوانين نيوتن، الطاقة، الزخم، الحركة التوافقية البسيطة والمزيد.
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Understand the motion of objects under the influence of forces. Explore Newton's laws, energy, momentum, simple harmonic motion and more.
                        </p>
                        <button class="text-indigo-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-indigo-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Thermodynamics Card -->
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-red-100 p-4 flex justify-center">
                        <i class="fas fa-temperature-high text-5xl text-red-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الديناميكا الحرارية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Thermodynamics</h3>
                        <p class="text-gray-600 mb-4 ar">
                            دراسة الحرارة والطاقة وتحولاتهما. استكشف القوانين الأساسية، المحركات الحرارية، الإنتروبيا والتطبيقات العملية.
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Study of heat, energy and their transformations. Explore fundamental laws, heat engines, entropy and practical applications.
                        </p>
                        <button class="text-red-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-red-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Electromagnetism Card -->
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-blue-100 p-4 flex justify-center">
                        <i class="fas fa-bolt text-5xl text-blue-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الكهرومغناطيسية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Electromagnetism</h3>
                        <p class="text-gray-600 mb-4 ar">
                            فهم التفاعل بين الكهرباء والمغناطيسية. استكشف المجالات الكهربائية والمغناطيسية، دوائر التيار المتردد، الموجات الكهرومغناطيسية والمزيد.
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Understand the interaction between electricity and magnetism. Explore electric and magnetic fields, AC circuits, electromagnetic waves and more.
                        </p>
                        <button class="text-blue-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-blue-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Simulations Section -->
    <section id="simulations" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">المحاكاة التفاعلية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Interactive Simulations</h2>
            
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">
                استكشف المفاهيم الفيزيائية من خلال محاكاة تفاعلية تسمح لك بتغيير المعايير ورؤية النتائج في الوقت الحقيقي
            </p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">
                Explore physics concepts through interactive simulations that allow you to change parameters and see results in real time
            </p>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Simulation 1 -->
                <div class="simulation-window p-4">
                    <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                        <div class="bg-gray-200 h-6 flex items-center px-2">
                            <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div class="p-4 h-64 flex items-center justify-center">
                            <canvas id="simulation1" class="w-full h-full"></canvas>
                        </div>
                        <div class="bg-gray-100 px-4 py-3">
                            <h3 class="font-semibold mb-2 ar">حركة المقذوفات</h3>
                            <h3 class="font-semibold mb-2 en hidden">Projectile Motion</h3>
                            
                            <div class="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">السرعة الابتدائية (م/ث)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Initial Velocity (m/s)</label>
                                    <input type="range" min="10" max="100" value="50" class="w-full">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">زاوية الإطلاق (درجة)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Launch Angle (degrees)</label>
                                    <input type="range" min="0" max="90" value="45" class="w-full">
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <button class="text-indigo-600 text-sm flex items-center ar">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span>إعادة تعيين</span>
                                </button>
                                <button class="text-indigo-600 text-sm flex items-center en hidden">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span>Reset</span>
                                </button>
                                
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center ar">
                                    <i class="fas fa-play mr-1"></i>
                                    <span>تشغيل المحاكاة</span>
                                </button>
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center en hidden">
                                    <i class="fas fa-play mr-1"></i>
                                    <span>Run Simulation</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Simulation 2 -->
                <div class="simulation-window p-4">
                    <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                        <div class="bg-gray-200 h-6 flex items-center px-2">
                            <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div class="p-4 h-64 flex items-center justify-center">
                            <canvas id="simulation2" class="w-full h-full"></canvas>
                        </div>
                        <div class="bg-gray-100 px-4 py-3">
                            <h3 class="font-semibold mb-2 ar">الدوائر الكهربائية</h3>
                            <h3 class="font-semibold mb-2 en hidden">Electric Circuits</h3>
                            
                            <div class="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">الجهد الكهربائي (فولت)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Voltage (V)</label>
                                    <input type="range" min="1" max="12" value="6" class="w-full">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">المقاومة (أوم)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Resistance (Ω)</label>
                                    <input type="range" min="1" max="20" value="10" class="w-full">
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <button class="text-indigo-600 text-sm flex items-center ar">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span>إعادة تعيين</span>
                                </button>
                                <button class="text-indigo-600 text-sm flex items-center en hidden">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span>Reset</span>
                                </button>
                                
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center ar">
                                    <i class="fas fa-play mr-1"></i>
                                    <span>تشغيل المحاكاة</span>
                                </button>
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center en hidden">
                                    <i class="fas fa-play mr-1"></i>
                                    <span>Run Simulation</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">
                    <i class="fas fa-list mr-2"></i>
                    عرض جميع المحاكاة
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">
                    <i class="fas fa-list mr-2"></i>
                    View All Simulations
                </button>
            </div>
        </div>
    </section>

    <!-- Virtual Labs Section -->
    <section id="labs" class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">المختبرات الافتراضية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Virtual Labs</h2>
            
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">
                قم بإجراء تجارب معملية افتراضية في بيئة آمنة وتفاعلية دون الحاجة إلى معدات فعلية
            </p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">
                Conduct virtual lab experiments in a safe, interactive environment without the need for physical equipment
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Lab 1 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="relative h-48 bg-indigo-100 flex items-center justify-center">
                        <img src="https://via.placeholder.com/300x200" alt="Physics Lab" class="absolute inset-0 w-full h-full object-cover opacity-30">
                        <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                            <i class="fas fa-weight-hanging text-4xl text-indigo-600"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">مختبر الميكانيكا</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Mechanics Lab</h3>
                        <p class="text-gray-600 mb-4 ar">
                            استكشف قوانين الحركة، الاحتكاك، الزخم والطاقة من خلال تجارب معملية تفاعلية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Explore laws of motion, friction, momentum and energy through interactive lab experiments
                        </p>
                        <button class="text-indigo-600 font-medium flex items-center ar">
                            <span>الدخول إلى المختبر</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-indigo-600 font-medium flex items-center en hidden">
                            <span>Enter Lab</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Lab 2 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="relative h-48 bg-red-100 flex items-center justify-center">
                        <img src="https://via.placeholder.com/300x200" alt="Thermodynamics Lab" class="absolute inset-0 w-full h-full object-cover opacity-30">
                        <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                            <i class="fas fa-temperature-high text-4xl text-red-600"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">مختبر الديناميكا الحرارية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Thermodynamics Lab</h3>
                        <p class="text-gray-600 mb-4 ar">
                            دراسة انتقال الحرارة، القوانين الأساسية للديناميكا الحرارية والمحركات الحرارية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Study heat transfer, fundamental laws of thermodynamics and heat engines
                        </p>
                        <button class="text-red-600 font-medium flex items-center ar">
                            <span>الدخول إلى المختبر</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-red-600 font-medium flex items-center en hidden">
                            <span>Enter Lab</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Lab 3 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="relative h-48 bg-blue-100 flex items-center justify-center">
                        <img src="https://via.placeholder.com/300x200" alt="Electromagnetism Lab" class="absolute inset-0 w-full h-full object-cover opacity-30">
                        <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                            <i class="fas fa-bolt text-4xl text-blue-600"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">مختبر الكهرومغناطيسية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Electromagnetism Lab</h3>
                        <p class="text-gray-600 mb-4 ar">
                            استكشف المجالات الكهربائية والمغناطيسية، الحث الكهرومغناطيسي والدوائر الكهربائية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Explore electric and magnetic fields, electromagnetic induction and electric circuits
                        </p>
                        <button class="text-blue-600 font-medium flex items-center ar">
                            <span>الدخول إلى المختبر</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-blue-600 font-medium flex items-center en hidden">
                            <span>Enter Lab</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">
                    <i class="fas fa-flask mr-2"></i>
                    استكشف جميع المختبرات
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">
                    <i class="fas fa-flask mr-2"></i>
                    Explore All Labs
                </button>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">الدروس التعليمية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Learning Courses</h2>
            
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">
                تعلم الفيزياء من خلال دروس تفاعلية تغطي المفاهيم الأساسية مع أمثلة تطبيقية وتمارين تفاعلية
            </p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">
                Learn physics through interactive lessons covering fundamental concepts with practical examples and interactive exercises
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Course 1 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-indigo-500 transition-all">
                    <div class="h-48 bg-indigo-100 flex items-center justify-center">
                        <i class="fas fa-weight-hanging text-6xl text-indigo-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold ar">الميكانيكا الكلاسيكية</h3>
                            <h3 class="text-xl font-semibold en hidden">Classical Mechanics</h3>
                            <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full ar">12 درس</span>
                            <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full en hidden">12 Lessons</span>
                        </div>
                        <p class="text-gray-600 mb-4 ar">
                            تعلم أساسيات الحركة، القوى، الطاقة والزخم من خلال أمثلة تفاعلية وتطبيقات عملية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Learn the basics of motion, forces, energy and momentum through interactive examples and practical applications
                        </p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600">4.8</span>
                            </div>
                            <button class="text-indigo-600 font-medium flex items-center ar">
                                <span>بدء الدورة</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </button>
                            <button class="text-indigo-600 font-medium flex items-center en hidden">
                                <span>Start Course</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Course 2 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-red-500 transition-all">
                    <div class="h-48 bg-red-100 flex items-center justify-center">
                        <i class="fas fa-temperature-high text-6xl text-red-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold ar">الديناميكا الحرارية</h3>
                            <h3 class="text-xl font-semibold en hidden">Thermodynamics</h3>
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full ar">8 درس</span>
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full en hidden">8 Lessons</span>
                        </div>
                        <p class="text-gray-600 mb-4 ar">
                            فهم القوانين الأساسية للديناميكا الحرارية وتطبيقاتها في المحركات الحرارية والأنظمة الحرارية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Understand the fundamental laws of thermodynamics and their applications in heat engines and thermal systems
                        </p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600">4.6</span>
                            </div>
                            <button class="text-red-600 font-medium flex items-center ar">
                                <span>بدء الدورة</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </button>
                            <button class="text-red-600 font-medium flex items-center en hidden">
                                <span>Start Course</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Course 3 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-blue-500 transition-all">
                    <div class="h-48 bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-bolt text-6xl text-blue-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold ar">الكهرومغناطيسية</h3>
                            <h3 class="text-xl font-semibold en hidden">Electromagnetism</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ar">10 درس</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full en hidden">10 Lessons</span>
                        </div>
                        <p class="text-gray-600 mb-4 ar">
                            استكشف العالم المذهل للكهرباء والمغناطيسية وتفاعلاتهما من خلال تجارب وتطبيقات عملية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Explore the fascinating world of electricity and magnetism and their interactions through experiments and practical applications
                        </p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600">4.9</span>
                            </div>
                            <button class="text-blue-600 font-medium flex items-center ar">
                                <span>بدء الدورة</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </button>
                            <button class="text-blue-600 font-medium flex items-center en hidden">
                                <span>Start Course</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">
                    <i class="fas fa-book-open mr-2"></i>
                    تصفح جميع الدورات
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">
                    <i class="fas fa-book-open mr-2"></i>
                    Browse All Courses
                </button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">لماذا تختار منصتنا؟</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Why Choose Our Platform?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-laptop-code text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">محاكاة تفاعلية</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Interactive Simulations</h3>
                    <p class="text-gray-600 ar">
                        تجارب فيزيائية تفاعلية تسمح لك بتغيير المعايير ورؤية النتائج في الوقت الحقيقي
                    </p>
                    <p class="text-gray-600 en hidden">
                        Interactive physics experiments that allow you to change parameters and see results in real time
                    </p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-flask text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">مختبرات افتراضية</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Virtual Labs</h3>
                    <p class="text-gray-600 ar">
                        بيئة معملية آمنة لتجارب الفيزياء دون الحاجة إلى معدات فعلية
                    </p>
                    <p class="text-gray-600 en hidden">
                        Safe lab environment for physics experiments without the need for physical equipment
                    </p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-language text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">دعم ثنائي اللغة</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Bilingual Support</h3>
                    <p class="text-gray-600 ar">
                        واجهة مستخدم متكاملة باللغتين العربية والإنجليزية لضمان تجربة تعلم شاملة
                    </p>
                    <p class="text-gray-600 en hidden">
                        Fully integrated Arabic/English interface to ensure a comprehensive learning experience
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-8 md:mb-0">
                    <h2 class="text-3xl font-bold mb-4 ar">عن منصة فيزيكس</h2>
                    <h2 class="text-3xl font-bold mb-4 en hidden">About Physix Platform</h2>
                    
                    <p class="text-gray-600 mb-4 ar">
                        فيزيكس هي منصة تعليمية مبتكرة تهدف إلى تحويل تعلم الفيزياء من عملية نظرية إلى تجربة تفاعلية ممتعة. نقدم محتوى تعليمي عالي الجودة مدعوم بمحاكاة حية ومختبرات افتراضية.
                    </p>
                    <p class="text-gray-600 mb-4 en hidden">
                        Physix is an innovative educational platform that aims to transform physics learning from a theoretical process into an enjoyable interactive experience. We provide high-quality educational content supported by live simulations and virtual labs.
                    </p>
                    
                    <p class="text-gray-600 mb-6 ar">
                        مهمتنا هي جعل الفيزياء في متناول الجميع بغض النظر عن خلفياتهم التعليمية أو مستوى مهاراتهم.
                    </p>
                    <p class="text-gray-600 mb-6 en hidden">
                        Our mission is to make physics accessible to everyone regardless of their educational background or skill level.
                    </p>
                    
                    <div class="flex space-x-4 ar:space-x-reverse">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">تواصل معنا</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow ar">تعرف على فريقنا</button>
                        
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">Contact Us</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow en hidden">Meet Our Team</button>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="bg-indigo-100 rounded-xl p-8">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">50+</h3>
                                <p class="text-gray-600 ar">محاكاة تفاعلية</p>
                                <p class="text-gray-600 en hidden">Interactive Simulations</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">15+</h3>
                                <p class="text-gray-600 ar">مختبر افتراضي</p>
                                <p class="text-gray-600 en hidden">Virtual Labs</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">30+</h3>
                                <p class="text-gray-600 ar">درس تعليمي</p>
                                <p class="text-gray-600 en hidden">Learning Lessons</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">10K+</h3>
                                <p class="text-gray-600 ar">طالب مستفيد</p>
                                <p class="text-gray-600 en hidden">Active Students</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">آراء طلابنا</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Student Testimonials</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <!-- Testimonial 1 -->
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">أحمد محمد</h4>
                            <p class="text-gray-500 text-sm ar">طالب فيزياء</p>
                            <p class="text-gray-500 text-sm en hidden">Physics Student</p>
                        </div>
                    </div>
                    <p class="text-gray-600 ar">
                        "المحاكاة التفاعلية ساعدتني كثيرًا في فهم مفاهيم الحركة التوافقية البسيطة التي كنت أجد صعوبة في استيعابها من الكتب فقط."
                    </p>
                    <p class="text-gray-600 en hidden">
                        "The interactive simulations helped me a lot in understanding simple harmonic motion concepts that I found difficult to grasp from books alone."
                    </p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">Sarah Johnson</h4>
                            <p class="text-gray-500 text-sm en">High School Teacher</p>
                            <p class="text-gray-500 text-sm ar hidden">معلمة فيزياء</p>
                        </div>
                    </div>
                    <p class="text-gray-600 en">
                        "The virtual labs are an excellent resource for my students who don't have access to a fully equipped physics lab at school."
                    </p>
                    <p class="text-gray-600 ar hidden">
                        "المختبرات الافتراضية مصدر ممتاز لطلابي الذين لا يحصلون على مختبر فيزياء مجهز بالكامل في المدرسة."
                    </p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
                
                <!-- Testimonial 3 -->
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">علي عبد الرحمن</h4>
                            <p class="text-gray-500 text-sm ar">مهندس</p>
                            <p class="text-gray-500 text-sm en hidden">Engineer</p>
                        </div>
                    </div>
                    <p class="text-gray-600 ar">
                        "الدروس ثنائية اللغة ساعدتني في تحسين فهمي للفيزياء بالإنجليزية مع الحفاظ على الأساسيات بالعربية."
                    </p>
                    <p class="text-gray-600 en hidden">
                        "The bilingual lessons helped me improve my understanding of physics in English while maintaining the basics in Arabic."
                    </p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-12 bg-indigo-600 text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-4 ar">اشترك في نشرتنا البريدية</h2>
                <h2 class="text-3xl font-bold mb-4 en hidden">Subscribe to Our Newsletter</h2>
                
                <p class="mb-6 opacity-90 ar">
                    احصل على آخر التحديثات حول المحاكاة الجديدة، الدروس والمختبرات الافتراضية مباشرة إلى بريدك الإلكتروني
                </p>
                <p class="mb-6 opacity-90 en hidden">
                    Get the latest updates on new simulations, lessons and virtual labs delivered straight to your inbox
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="بريدك الإلكتروني" class="flex-grow px-4 py-3 rounded-lg text-gray-800 ar" dir="rtl">
                    <input type="email" placeholder="Your Email" class="flex-grow px-4 py-3 rounded-lg text-gray-800 en hidden" dir="ltr">
                    <button class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium shadow ar">اشتراك</button>
                    <button class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium shadow en hidden">Subscribe</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <i class="fas fa-atom text-2xl mr-3"></i>
                        <h3 class="text-xl font-bold ar">فيزيكس</h3>
                        <h3 class="text-xl font-bold en hidden">Physix</h3>
                    </div>
                    <p class="text-gray-400 mb-4 ar">
                        منصة تعليمية تفاعلية لتعلم الفيزياء عبر الإنترنت من خلال محاكاة حية ومختبرات افتراضية.
                    </p>
                    <p class="text-gray-400 mb-4 en hidden">
                        Interactive online physics learning platform through live simulations and virtual labs.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">روابط سريعة</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الرئيسية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">المحاكاة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">المختبرات</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الدروس</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">عن المنصة</a></li>
                        
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Home</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Simulations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Labs</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Courses</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">About</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">فروع الفيزياء</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Physics Branches</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الميكانيكا الكلاسيكية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الديناميكا الحرارية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الكهرومغناطيسية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">البصريات</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الفيزياء الحديثة</a></li>
                        
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Classical Mechanics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Thermodynamics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Electromagnetism</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Optics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Modern Physics</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">تواصل معنا</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Contact Us</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-gray-400"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2 text-gray-400"></i>
                            <span class="text-gray-400">+966 12 345 6789</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                            <span class="text-gray-400 ar">الرياض، المملكة العربية السعودية</span>
                            <span class="text-gray-400 en hidden">Riyadh, Saudi Arabia</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p class="ar">&copy; 2023 فيزيكس. جميع الحقوق محفوظة.</p>
                <p class="en hidden">&copy; 2023 Physix. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Language Switch Functionality
        document.getElementById('languageSwitch').addEventListener('click', function() {
            document.querySelectorAll('.ar').forEach(el => el.classList.toggle('hidden'));
            document.querySelectorAll('.en').forEach(el => el.classList.toggle('hidden'));
            
            // Toggle HTML direction and lang attribute
            if (document.documentElement.lang === 'ar') {
                document.documentElement.lang = 'en';
                document.documentElement.dir = 'ltr';
            } else {
                document.documentElement.lang = 'ar';
                document.documentElement.dir = 'rtl';
            }
        });
        
        // Dark Mode Toggle
        document.getElementById('darkModeToggle').addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            // Change icon
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-moon')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        });
        
        // Simple Projectile Motion Simulation for Hero Section
        const heroCanvas = document.getElementById('heroSimulation');
        const heroCtx = heroCanvas.getContext('2d');
        
        // Set canvas dimensions
        heroCanvas.width = heroCanvas.offsetWidth;
        heroCanvas.height = heroCanvas.offsetHeight;
        
        // Simulation parameters
        let time = 0;
        const gravity = 0.2;
        let ball = {
            x: 50,
            y: heroCanvas.height - 50,
            radius: 10,
            vx: 3,
            vy: -8,
            color: '#4F46E5'
        };
        
        function drawBall() {
            heroCtx.beginPath();
            heroCtx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
            heroCtx.fillStyle = ball.color;
            heroCtx.fill();
            heroCtx.closePath();
        }
        
        function drawGround() {
            heroCtx.beginPath();
            heroCtx.moveTo(0, heroCanvas.height - 30);
            heroCtx.lineTo(heroCanvas.width, heroCanvas.height - 30);
            heroCtx.strokeStyle = '#6B7280';
            heroCtx.stroke();
            heroCtx.closePath();
        }
        
        function update() {
            heroCtx.clearRect(0, 0, heroCanvas.width, heroCanvas.height);
            
            // Update ball position
            ball.x += ball.vx;
            ball.y += ball.vy;
            ball.vy += gravity;
            
            // Bounce off ground
            if (ball.y + ball.radius > heroCanvas.height - 30) {
                ball.y = heroCanvas.height - 30 - ball.radius;
                ball.vy = -ball.vy * 0.6;
            }
            
            // Reset when ball goes off screen
            if (ball.x - ball.radius > heroCanvas.width) {
                ball.x = 50;
                ball.y = heroCanvas.height - 50;
                ball.vx = 3;
                ball.vy = -8;
            }
            
            drawGround();
            drawBall();
            
            requestAnimationFrame(update);
        }
        
        update();
        
        // Simulation 1 - Projectile Motion
        const sim1Canvas = document.getElementById('simulation1');
        const sim1Ctx = sim1Canvas.getContext('2d');
        
        sim1Canvas.width = sim1Canvas.offsetWidth;
        sim1Canvas.height = sim1Canvas.offsetHeight;
        
        let sim1Ball = {
            x: 50,
            y: sim1Canvas.height - 50,
            radius: 8,
            vx: 5,
            vy: -10,
            color: '#4F46E5'
        };
        
        let sim1Running = false;
        
        function drawSim1Ball() {
            sim1Ctx.beginPath();
            sim1Ctx.arc(sim1Ball.x, sim1Ball.y, sim1Ball.radius, 0, Math.PI * 2);
            sim1Ctx.fillStyle = sim1Ball.color;
            sim1Ctx.fill();
            sim1Ctx.closePath();
        }
        
        function drawSim1Ground() {
            sim1Ctx.beginPath();
            sim1Ctx.moveTo(0, sim1Canvas.height - 30);
            sim1Ctx.lineTo(sim1Canvas.width, sim1Canvas.height - 30);
            sim1Ctx.strokeStyle = '#6B7280';
            sim1Ctx.stroke();
            sim1Ctx.closePath();
        }
        
        function updateSim1() {
            if (!sim1Running) return;
            
            sim1Ctx.clearRect(0, 0, sim1Canvas.width, sim1Canvas.height);
            
            // Update ball position
            sim1Ball.x += sim1Ball.vx;
            sim1Ball.y += sim1Ball.vy;
            sim1Ball.vy += gravity;
            
            // Bounce off ground
            if (sim1Ball.y + sim1Ball.radius > sim1Canvas.height - 30) {
                sim1Ball.y = sim1Canvas.height - 30 - sim1Ball.radius;
                sim1Ball.vy = -sim1Ball.vy * 0.6;
            }
            
            // Reset when ball goes off screen
            if (sim1Ball.x - sim1Ball.radius > sim1Canvas.width) {
                resetSim1();
            }
            
            drawSim1Ground();
            drawSim1Ball();
            
            requestAnimationFrame(updateSim1);
        }
        
        function resetSim1() {
            sim1Running = false;
            sim1Ball.x = 50;
            sim1Ball.y = sim1Canvas.height - 50;
            sim1Ball.vx = 5;
            sim1Ball.vy = -10;
            
            sim1Ctx.clearRect(0, 0, sim1Canvas.width, sim1Canvas.height);
            drawSim1Ground();
            drawSim1Ball();
        }
        
        // Initialize sim1
        drawSim1Ground();
        drawSim1Ball();
        
        // Simulation 2 - Electric Circuit
        const sim2Canvas = document.getElementById('simulation2');
        const sim2Ctx = sim2Canvas.getContext('2d');
        
        sim2Canvas.width = sim2Canvas.offsetWidth;
        sim2Canvas.height = sim2Canvas.offsetHeight;
        
        let voltage = 6;
        let resistance = 10;
        let current = voltage / resistance;
        
        function drawCircuit() {
            sim2Ctx.clearRect(0, 0, sim2Canvas.width, sim2Canvas.height);
            
            // Draw battery
            sim2Ctx.fillStyle = '#EF4444';
            sim2Ctx.fillRect(50, sim2Canvas.height/2 - 30, 30, 60);
            
            // Draw resistor
            sim2Ctx.fillStyle = '#10B981';
            sim2Ctx.fillRect(sim2Canvas.width/2 - 15, sim2Canvas.height/2 - 20, 30, 40);
            
            // Draw wires
            sim2Ctx.beginPath();
            sim2Ctx.moveTo(80, sim2Canvas.height/2);
            sim2Ctx.lineTo(sim2Canvas.width/2 - 15, sim2Canvas.height/2);
            sim2Ctx.moveTo(sim2Canvas.width/2 + 15, sim2Canvas.height/2);
            sim2Ctx.lineTo(sim2Canvas.width - 50, sim2Canvas.height/2);
            sim2Ctx.lineTo(sim2Canvas.width - 50, sim2Canvas.height/2 + 60);
            sim2Ctx.lineTo(50, sim2Canvas.height/2 + 60);
            sim2Ctx.lineTo(50, sim2Canvas.height/2);
            sim2Ctx.strokeStyle = '#000';
            sim2Ctx.lineWidth = 2;
            sim2Ctx.stroke();
            
            // Draw current indicator
            sim2Ctx.fillStyle = '#3B82F6';
            sim2Ctx.font = '14px Arial';
            sim2Ctx.fillText(`I = ${current.toFixed(2)} A`, sim2Canvas.width/2 - 20, sim2Canvas.height/2 - 30);
            
            // Draw voltage and resistance values
            sim2Ctx.fillStyle = '#000';
            sim2Ctx.font = '12px Arial';
            sim2Ctx.fillText(`${voltage}V`, 40, sim2Canvas.height/2 - 40);
            sim2Ctx.fillText(`${resistance}Ω`, sim2Canvas.width/2 - 10, sim2Canvas.height/2 - 40);
        }
        
        // Initialize circuit
        drawCircuit();
        
        // Update circuit when sliders change
        document.querySelectorAll('#simulation2 + div input[type="range"]').forEach(slider => {
            slider.addEventListener('input', function() {
                if (this.parentElement.querySelector('label').textContent.includes('الجهد')) {
                    voltage = parseInt(this.value);
                } else {
                    resistance = parseInt(this.value);
                }
                current = voltage / resistance;
                drawCircuit();
            });
        });
        
        // Run simulation buttons
        document.querySelectorAll('.simulation-window button').forEach(button => {
            if (button.textContent.includes('تشغيل') || button.textContent.includes('Run')) {
                button.addEventListener('click', function() {
                    const simulationWindow = this.closest('.simulation-window');
                    const canvas = simulationWindow.querySelector('canvas');
                    
                    if (canvas.id === 'simulation1') {
                        sim1Running = true;
                        updateSim1();
                    }
                });
            }
            
            if (button.textContent.includes('إعادة') || button.textContent.includes('Reset')) {
                button.addEventListener('click', function() {
                    const simulationWindow = this.closest('.simulation-window');
                    const canvas = simulationWindow.querySelector('canvas');
                    
                    if (canvas.id === 'simulation1') {
                        resetSim1();
                    } else if (canvas.id === 'simulation2') {
                        voltage = 6;
                        resistance = 10;
                        current = voltage / resistance;
                        simulationWindow.querySelectorAll('input[type="range"]')[0].value = voltage;
                        simulationWindow.querySelectorAll('input[type="range"]')[1].value = resistance;
                        drawCircuit();
                    }
                });
            }
        });
        
        // Responsive adjustments
        window.addEventListener('resize', function() {
            // Hero simulation
            heroCanvas.width = heroCanvas.offsetWidth;
            heroCanvas.height = heroCanvas.offsetHeight;
            
            // Simulation 1
            sim1Canvas.width = sim1Canvas.offsetWidth;
            sim1Canvas.height = sim1Canvas.offsetHeight;
            resetSim1();
            
            // Simulation 2
            sim2Canvas.width = sim2Canvas.offsetWidth;
            sim2Canvas.height = sim2Canvas.offsetHeight;
            drawCircuit();
        });
    </script>
</body>
</html>