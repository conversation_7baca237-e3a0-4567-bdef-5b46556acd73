<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | محاكي الحالات الثلاث للمادة</title>
  <link rel="stylesheet" href="styles/workspace-config.css"/>
  <link rel="stylesheet" href="styles/states_of_matter.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
  <script src="scripts/workspace-config.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="heat_transfer.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">محاكي الحالات الثلاث للمادة</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">محاكي الحالات الثلاث للمادة: تحولات الطور</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف كيف تتحول المادة بين الحالات الثلاث (صلبة، سائلة، غازية) بتأثير درجة الحرارة والضغط. شاهد حركة الجزيئات وتفاعلها في كل حالة وتعلم نقاط التحول بين الأطوار.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">الحالة الصلبة: جزيئات مترابطة بقوة، حركة اهتزازية محدودة</li>
            <li data-i18n="t2">الحالة السائلة: جزيئات متحركة، روابط أضعف من الصلب</li>
            <li data-i18n="t3">الحالة الغازية: جزيئات حرة الحركة، روابط ضعيفة جداً</li>
            <li data-i18n="t4">نقاط التحول: انصهار، تبخر، تجمد، تكثف</li>
            <li data-i18n="t5">الطاقة الحركية للجزيئات تزداد مع درجة الحرارة</li>
          </ul>
          <div class="formula">KE_avg = (3/2)kT</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot solid"></i> Solid</span>
              <span class="legend-item"><i class="dot liquid"></i> Liquid</span>
              <span class="legend-item"><i class="dot gas"></i> Gas</span>
              <span class="legend-item"><i class="dot molecule"></i> Molecule</span>
            </div>
            <div id="p5-container" class="p5-container states-matter-workspace" aria-label="States of matter simulation"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="state-selector">
            <h3 data-i18n="state_title">حالة المادة</h3>
            <div class="state-buttons">
              <button class="btn state active" data-state="solid" data-i18n="state_solid">صلبة</button>
              <button class="btn state" data-state="liquid" data-i18n="state_liquid">سائلة</button>
              <button class="btn state" data-state="gas" data-i18n="state_gas">غازية</button>
            </div>
          </div>
          
          <div class="field">
            <label for="temperature" data-i18n="temperature_label">درجة الحرارة T (K)</label>
            <input type="range" id="temperature" min="100" max="500" step="5" value="273"/>
            <output id="temperatureOut">273</output>
          </div>
          
          <div class="field">
            <label for="pressure" data-i18n="pressure_label">الضغط P (atm)</label>
            <input type="range" id="pressure" min="0.1" max="5" step="0.1" value="1"/>
            <output id="pressureOut">1.0</output>
          </div>
          
          <div class="field">
            <label for="moleculeCount" data-i18n="molecules_label">عدد الجزيئات</label>
            <input type="range" id="moleculeCount" min="50" max="200" step="10" value="100"/>
            <output id="moleculeCountOut">100</output>
          </div>
          
          <div class="field">
            <label for="interactionStrength" data-i18n="interaction_label">قوة التفاعل بين الجزيئات</label>
            <input type="range" id="interactionStrength" min="0" max="10" step="0.5" value="5"/>
            <output id="interactionStrengthOut">5.0</output>
          </div>
          
          <div class="phase-transitions">
            <h3 data-i18n="transitions_title">نقاط التحول</h3>
            <div class="transition-info">
              <div class="transition-item">
                <span data-i18n="melting_point">نقطة الانصهار</span>: <span id="meltingPoint">273 K</span>
              </div>
              <div class="transition-item">
                <span data-i18n="boiling_point">نقطة الغليان</span>: <span id="boilingPoint">373 K</span>
              </div>
            </div>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="start" class="btn" data-i18n="start">بدء المحاكاة</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="kinetic_energy_title">الطاقة الحركية</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_avg_ke">KE_avg</span> = <span id="dashAvgKE">0.0</span> J
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_avg_speed">v_avg</span> = <span id="dashAvgSpeed">0.0</span> m/s
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="phase_info_title">معلومات الطور</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_current_state">الحالة</span>: <span id="dashCurrentState">صلبة</span>
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_density">الكثافة</span>: <span id="dashDensity">1.0</span> g/cm³
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="molecular_motion_title">الحركة الجزيئية</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_vibration">الاهتزاز</span>: <span id="dashVibration">عالي</span>
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_translation">الانتقال</span>: <span id="dashTranslation">منخفض</span>
                </div>
              </div>
            </div>
            
            <canvas id="energyDistributionChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        فهم العلاقة بين درجة الحرارة والضغط وحالة المادة. اكتشاف كيف تؤثر الطاقة الحركية للجزيئات على نوع الحركة (اهتزازية، انتقالية، دورانية) وكيف تحدث تحولات الطور عند نقاط حرجة معينة.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/states_of_matter.js"></script>
</body>
</html>
