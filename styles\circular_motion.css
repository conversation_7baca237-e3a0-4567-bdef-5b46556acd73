:root {
  --bg: #0b1020;
  --card: #121933;
  --accent: #3aa0ff;
  --accent-2: #7bd389;
  --text: #e9eefb;
  --muted: #a9b4d0;
  --danger: #ff5c5c;
  --shadow: 0 10px 30px rgba(0,0,0,0.35);
}

* { box-sizing: border-box; }
html, body {
  margin: 0;
  padding: 0;
  background: linear-gradient(180deg, #0b1020, #0b1329 40%, #0b1020);
  color: var(--text);
  font-family: "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, "Noto Kufi Arabic", "Cairo", sans-serif;
}

body[dir="rtl"] { direction: rtl; }
body[dir="ltr"] { direction: ltr; }

a { color: var(--accent); text-decoration: none; }
a:hover { text-decoration: underline; }

.site-header {
  position: sticky; top: 0; z-index: 1000;
  background: rgba(11,16,32,0.7);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255,255,255,0.06);
}
.navbar {
  max-width: 1200px; margin: 0 auto; padding: 12px 16px;
  display: flex; align-items: center; justify-content: space-between; gap: 16px;
}
.brand-title { font-size: 1.2rem; font-weight: 700; letter-spacing: 0.5px; }
.brand-sub { color: var(--muted); display: block; font-size: 0.78rem; }

.nav-links { list-style: none; display: flex; gap: 12px; padding: 0; margin: 0; flex-wrap: wrap; }
.nav-links a {
  display: inline-block; padding: 8px 12px; border-radius: 8px; color: var(--text);
}
.nav-links a:hover { background: rgba(255,255,255,0.06); }

.lang-toggle {
  border: 1px solid rgba(255,255,255,0.2);
  background: transparent; color: var(--text);
  padding: 8px 10px; border-radius: 8px; cursor: pointer;
}
.lang-toggle:hover { background: rgba(255,255,255,0.08); }

.container { max-width: 1200px; margin: 24px auto; padding: 0 16px; }

.breadcrumbs {
  color: var(--muted); font-size: 0.9rem; margin-bottom: 10px;
}
.breadcrumbs .sep { opacity: 0.6; margin: 0 6px; }

.lesson-hero {
  background: radial-gradient(1200px 400px at 50% -20%, rgba(58,160,255,0.15), transparent 60%);
  padding: 16px 0 8px 0;
}
.lesson-hero h1 { margin: 0 0 8px 0; font-size: 1.6rem; }
.lesson-hero .lead { margin: 0; color: var(--muted); }

.card {
  background: linear-gradient(180deg, rgba(18,25,51,0.9), rgba(18,25,51,0.8));
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 14px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: var(--shadow);
}

.theory-grid {
  display: grid; grid-template-columns: 1fr; gap: 16px;
}
@media (min-width: 900px) {
  .theory-grid { grid-template-columns: 1.1fr 0.9fr; }
}

.formula {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", monospace;
  background: rgba(58,160,255,0.12);
  border: 1px solid rgba(58,160,255,0.35);
  color: #d9eeff;
  display: inline-block;
  padding: 10px 12px;
  border-radius: 10px;
  margin: 8px 0 10px 0;
}
.sub { font-size: 0.9em; }

.bullets { margin-top: 6px; padding-inline-start: 1.2em; color: var(--muted); }

.media { display: grid; grid-template-columns: 1fr; gap: 12px; }
.gif-box, .video-box { background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.06); border-radius: 12px; padding: 12px; }
.circle-scene {
  position: relative; width: 100%; aspect-ratio: 1 / 1; min-height: 220px;
}
.circle-scene .orbit {
  position: absolute; inset: 10%; border-radius: 50%;
  border: 2px dashed rgba(122,167,255,0.45);
}
.circle-scene .object {
  --angle: 0deg;
  position: absolute; width: 16px; height: 16px; border-radius: 50%;
  background: var(--accent);
  top: 50%; left: 50%;
  transform-origin: -40% -40%;
  animation: orbit 4s linear infinite;
}
.circle-scene .arrow {
  position: absolute; width: 2px; height: 28%;
  background: var(--danger);
  top: 22%; left: 50%;
  transform-origin: bottom center;
  animation: arrow 4s linear infinite;
}
@keyframes orbit {
  from { transform: rotate(0deg) translate(40%) rotate(0deg); }
  to   { transform: rotate(360deg) translate(40%) rotate(-360deg); }
}
@keyframes arrow {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}

.calc-grid {
  display: grid; grid-template-columns: 1fr; gap: 16px;
}
@media (min-width: 960px) {
  .calc-grid { grid-template-columns: 1fr 1fr; }
}
.inputs .field { margin-bottom: 12px; }
.field label { display: block; margin-bottom: 6px; color: var(--text); font-weight: 600; }
.control {
  display: grid; grid-template-columns: 1fr; gap: 8px;
}
.control input[type="number"], .control input[type="range"] {
  width: 100%;
}
input[type="number"] {
  background: rgba(0,0,0,0.25);
  border: 1px solid rgba(255,255,255,0.12);
  color: var(--text);
  border-radius: 8px;
  padding: 10px 12px;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type="range"] {
  appearance: none; height: 6px; border-radius: 6px; background: rgba(255,255,255,0.15);
}
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none; appearance: none;
  width: 16px; height: 16px; border-radius: 50%; background: var(--accent); border: none;
}
.presets { display: flex; flex-wrap: wrap; align-items: center; gap: 8px; margin-top: 8px; }
.preset-label { color: var(--muted); margin-inline-end: 6px; }
.btn {
  background: var(--accent); color: #001428; border: none; border-radius: 10px;
  padding: 8px 12px; cursor: pointer; font-weight: 700;
}
.btn:hover { filter: brightness(1.05); }
.btn.outline {
  background: transparent; color: var(--accent);
  border: 1px solid var(--accent); padding: 8px 12px;
}

.outputs .result-box {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 12px; padding: 14px; margin-bottom: 12px;
}
.result-title { color: var(--muted); margin-bottom: 4px; }
.result-value { font-size: 1.6rem; font-weight: 800; }

.steps-box {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 12px; padding: 12px;
}
.steps-list { margin: 0; padding-inline-start: 1.2em; }

.lab-grid { display: grid; grid-template-columns: 1fr; gap: 16px; }
@media (min-width: 1100px) {
  .lab-grid { grid-template-columns: 0.9fr 1.1fr; }
}
.lab-controls .field { margin-bottom: 12px; }
.lab-controls label { display: inline-block; min-width: 140px; }
.lab-controls output { color: var(--accent-2); font-weight: 700; margin-inline-start: 6px; }
.toggles { display: flex; gap: 16px; margin-top: 6px; color: var(--muted); }
.toggle input { margin-inline-end: 6px; }

.canvas-wrap {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 12px; padding: 8px;
}
.p5-container { width: 100%; height: 360px; }

.dash { margin-top: 10px; }
.dash-row { display: grid; grid-template-columns: repeat(3,1fr); gap: 8px; }
@media (min-width: 800px) {
  .dash-row { grid-template-columns: repeat(6,1fr); }
}
.dash-item {
  background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.08);
  border-radius: 8px; padding: 8px; text-align: center; font-family: ui-monospace, monospace;
}

.cta { text-align: center; }
.cta h2 { margin-top: 0; }
.cta p { color: var(--muted); }

.site-footer {
  border-top: 1px solid rgba(255,255,255,0.06);
  background: rgba(11,16,32,0.7);
}
.footer-content {
  max-width: 1200px; margin: 0 auto; padding: 14px 16px;
  display: grid; gap: 10px;
}
@media (min-width: 800px) {
  .footer-content { grid-template-columns: 1fr 1fr; }
}
.footer-content .left, .footer-content .right { color: var(--muted); }
