<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>فيزيكس - منصة الفيزياء التفاعلية | Physix - Interactive Physics Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4F46E5;
            --secondary: #10B981;
            --dark: #1F2937;
            --light: #F3F4F6;
        }
        body {
            font-family: 'Tajawal', 'Poppins', sans-serif;
        }
        .en {
            font-family: 'Poppins', sans-serif;
            direction: ltr;
            text-align: left;
        }
        .ar {
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
        }
        .simulation-window {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .lab-equipment {
            transition: all 0.3s ease;
        }
        .lab-equipment:hover {
            transform: translateY(-5px);
            filter: drop-shadow(0 5px 5px rgba(0,0,0,0.1));
        }
        .language-switch {
            transition: all 0.3s ease;
        }
        .language-switch:hover {
            transform: scale(1.05);
        }
        .concept-card {
            transition: all 0.3s ease;
            perspective: 1000px;
        }
        .concept-card:hover {
            transform: translateY(-5px) rotateY(5deg);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .interactive-element {
            animation: pulse 2s infinite;
        }
        .dark-mode {
            background-color: #1a202c;
            color: #f7fafc;
        }
        .dark-mode .bg-white {
            background-color: #2d3748 !important;
            color: #f7fafc !important;
        }
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Language Switch Button -->
    <div class="fixed top-4 right-4 z-50">
        <button id="languageSwitch" class="language-switch bg-indigo-600 text-white px-4 py-2 rounded-full shadow-lg flex items-center" aria-label="تبديل اللغة" data-action="toggle-language">
            <i class="fas fa-language mr-2"></i>
            <span class="en">العربية</span>
            <span class="ar hidden">English</span>
        </button>
    </div>
    <!-- Dark Mode Toggle -->
    <div class="fixed top-4 left-4 z-50">
        <button id="darkModeToggle" class="bg-gray-800 text-white px-4 py-2 rounded-full shadow-lg" aria-label="تبديل الوضع الليلي" data-action="toggle-darkmode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- Header -->
    <header class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-6 shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <i class="fas fa-atom text-3xl mr-3"></i>
                    <h1 class="text-3xl font-bold ar">فيزيكس</h1>
                    <h1 class="text-3xl font-bold en hidden">Physix</h1>
                </div>
                <nav class="flex space-x-6 ar:space-x-reverse">
                    <a href="#home" class="hover:text-indigo-200 font-medium ar">الرئيسية</a>
                    <a href="#simulations" class="hover:text-indigo-200 font-medium ar">المحاكاة</a>
                    <a href="#labs" class="hover:text-indigo-200 font-medium ar">المختبرات</a>
                    <a href="#courses" class="hover:text-indigo-200 font-medium ar">الدروس</a>
                    <a href="#about" class="hover:text-indigo-200 font-medium ar">عن المنصة</a>
                </nav>
                <nav class="hidden en space-x-6">
                    <a href="#home" class="hover:text-indigo-200 font-medium">Home</a>
                    <a href="#simulations" class="hover:text-indigo-200 font-medium">Simulations</a>
                    <a href="#labs" class="hover:text-indigo-200 font-medium">Labs</a>
                    <a href="#courses" class="hover:text-indigo-200 font-medium">Courses</a>
                    <a href="#about" class="hover:text-indigo-200 font-medium">About</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-8 md:mb-0">
                    <h2 class="text-4xl font-bold mb-4 ar">استكشف عالم الفيزياء بطريقة تفاعلية</h2>
                    <h2 class="text-4xl font-bold mb-4 en hidden">Explore Physics in an Interactive Way</h2>
                    <p class="text-gray-600 mb-6 ar">
                        منصة تعليمية متكاملة تحول المفاهيم الفيزيائية المعقدة إلى تجارب تفاعلية ومحاكاة حية. تعلم الميكانيكا، الديناميكا الحرارية والكهرومغناطيسية بطريقة ممتعة.
                    </p>
                    <p class="text-gray-600 mb-6 en hidden">
                        An integrated educational platform that transforms complex physics concepts into interactive experiments and live simulations. Learn mechanics, thermodynamics and electromagnetism in a fun way.
                    </p>
                    <div class="flex space-x-4 ar:space-x-reverse">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">ابدأ التعلم</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow ar">جولة في المنصة</button>
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">Start Learning</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow en hidden">Platform Tour</button>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="simulation-window p-4">
                        <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                            <div class="bg-gray-200 h-6 flex items-center px-2">
                                <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                            </div>
                            <div class="p-4 h-64 flex items-center justify-center">
                                <canvas id="heroSimulation" class="w-full h-full"></canvas>
                            </div>
                            <div class="bg-gray-100 px-4 py-2 flex justify-between items-center">
                                <span class="text-sm text-gray-600 ar">محاكاة حركة المقذوفات</span>
                                <span class="text-sm text-gray-600 en hidden">Projectile Motion Simulation</span>
                                <button class="text-indigo-600 text-sm flex items-center" data-action="hero-play">
                                    <i class="fas fa-play mr-1 ar"></i>
                                    <span class="ar">تشغيل</span>
                                    <i class="fas fa-play ml-1 en hidden"></i>
                                    <span class="en hidden">Play</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Physics Branches Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 ar">استكشف فروع الفيزياء</h2>
            <h2 class="text-3xl font-bold text-center mb-12 en hidden">Explore Physics Branches</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-indigo-100 p-4 flex justify-center">
                        <i class="fas fa-weight-hanging text-5xl text-indigo-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الميكانيكا الكلاسيكية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Classical Mechanics</h3>
                        <p class="text-gray-600 mb-4 ar">فهم حركة الأجسام تحت تأثير القوى...</p>
                        <p class="text-gray-600 mb-4 en hidden">Understand the motion of objects under the influence of forces...</p>
                        <button class="text-indigo-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-indigo-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-red-100 p-4 flex justify-center">
                        <i class="fas fa-temperature-high text-5xl text-red-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الديناميكا الحرارية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Thermodynamics</h3>
                        <p class="text-gray-600 mb-4 ar">دراسة الحرارة والطاقة وتحولاتهما...</p>
                        <p class="text-gray-600 mb-4 en hidden">Study of heat, energy and their transformations...</p>
                        <button class="text-red-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-red-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                <div class="concept-card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-blue-100 p-4 flex justify-center">
                        <i class="fas fa-bolt text-5xl text-blue-600"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 ar">الكهرومغناطيسية</h3>
                        <h3 class="text-xl font-semibold mb-2 en hidden">Electromagnetism</h3>
                        <p class="text-gray-600 mb-4 ar">فهم التفاعل بين الكهرباء والمغناطيسية...</p>
                        <p class="text-gray-600 mb-4 en hidden">Understand the interaction between electricity and magnetism...</p>
                        <button class="text-blue-600 font-medium flex items-center ar">
                            <span>استكشف المحاكاة</span>
                            <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                        <button class="text-blue-600 font-medium flex items-center en hidden">
                            <span>Explore Simulations</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Simulations Section -->
    <section id="simulations" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">المحاكاة التفاعلية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Interactive Simulations</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">
                استكشف المفاهيم الفيزيائية من خلال محاكاة تفاعلية تسمح لك بتغيير المعايير ورؤية النتائج في الوقت الحقيقي
            </p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">
                Explore physics concepts through interactive simulations that allow you to change parameters and see results in real time
            </p>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Simulation 1 -->
                <div class="simulation-window p-4">
                    <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                        <div class="bg-gray-200 h-6 flex items-center px-2">
                            <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div class="p-4 h-64 flex items-center justify-center">
                            <canvas id="simulation1" class="w-full h-full"></canvas>
                        </div>
                        <div class="bg-gray-100 px-4 py-3">
                            <h3 class="font-semibold mb-2 ar">حركة المقذوفات</h3>
                            <h3 class="font-semibold mb-2 en hidden">Projectile Motion</h3>
                            <div class="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">السرعة الابتدائية (م/ث)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Initial Velocity (m/s)</label>
<input type="range" min="10" max="100" value="50" class="w-full" data-param="v0" aria-label="السرعة الابتدائية" title="السرعة الابتدائية" placeholder="السرعة الابتدائية">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">زاوية الإطلاق (درجة)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Launch Angle (degrees)</label>
<input type="range" min="0" max="90" value="45" class="w-full" data-param="theta" aria-label="زاوية الإطلاق" title="زاوية الإطلاق" placeholder="زاوية الإطلاق">
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <button class="text-indigo-600 text-sm flex items-center" data-action="reset-sim" data-target="simulation1">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span class="ar">إعادة تعيين</span>
                                    <span class="en hidden">Reset</span>
                                </button>
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center" data-action="run-sim" data-target="simulation1">
                                    <i class="fas fa-play mr-1"></i>
                                    <span class="ar">تشغيل المحاكاة</span>
                                    <span class="en hidden">Run Simulation</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Simulation 2 -->
                <div class="simulation-window p-4">
                    <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                        <div class="bg-gray-200 h-6 flex items-center px-2">
                            <divكات="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div class="p-4 h-64 flex items-center justify-center">
                            <canvas id="simulation2" class="w-full h-full"></canvas>
                        </div>
                        <div class="bg-gray-100 px-4 py-3">
                            <h3 class="font-semibold mb-2 ar">الدوائر الكهربائية</h3>
                            <h3 class="font-semibold mb-2 en hidden">Electric Circuits</h3>
                            <div class="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">الجهد الكهربائي (فولت)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Voltage (V)</label>
<input type="range" min="1" max="12" value="6" class="w-full" data-param="voltage" aria-label="الجهد الكهربائي" title="الجهد الكهربائي" placeholder="الجهد">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1 ar">المقاومة (أوم)</label>
                                    <label class="block text-sm text-gray-600 mb-1 en hidden">Resistance (Ω)</label>
<input type="range" min="1" max="20" value="10" class="w-full" data-param="resistance" aria-label="المقاومة" title="المقاومة" placeholder="المقاومة">
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <button class="text-indigo-600 text-sm flex items-center" data-action="reset-sim" data-target="simulation2">
                                    <i class="fas fa-redo mr-1"></i>
                                    <span class="ar">إعادة تعيين</span>
                                    <span class="en hidden">Reset</span>
                                </button>
                                <button class="bg-indigo-600 text-white px-4 py-1 rounded text-sm flex items-center" data-action="run-sim" data-target="simulation2" disabled>
                                    <i class="fas fa-play mr-1"></i>
                                    <span class="ar">تشغيل المحاكاة</span>
                                    <span class="en hidden">Run Simulation</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-8">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">
                    <i class="fas fa-list mr-2"></i>
                    عرض جميع المحاكاة
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">
                    <i class="fas fa-list mr-2"></i>
                    View All Simulations
                </button>
            </div>
        </div>
    </section>

    <!-- Virtual Labs Section -->
    <section id="labs" class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">المختبرات الافتراضية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Virtual Labs</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">مختبرات تفاعلية متطورة لاستكشاف المفاهيم الفيزيائية من خلال التجريب والمحاكاة</p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">Advanced interactive labs to explore physics concepts through experimentation and simulation</p>

            <!-- Mechanics Labs -->
            <div class="mb-8">
                <h3 class="text-2xl font-bold mb-6 ar">الميكانيكا الكلاسيكية</h3>
                <h3 class="text-2xl font-bold mb-6 en hidden">Classical Mechanics</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-orange-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-baseball-ball text-4xl text-orange-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">محاكي المقذوفات</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Projectile Simulator</h4>
                            <p class="text-gray-600 mb-4 ar">استكشف حركة المقذوفات في بعدين مع تأثير الجاذبية ومقاومة الهواء</p>
                            <p class="text-gray-600 mb-4 en hidden">Explore projectile motion in 2D with gravity and air resistance effects</p>
                            <a href="projectiles.html" class="text-orange-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="projectiles.html" class="text-orange-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-blue-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-weight-hanging text-4xl text-blue-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">قوانين نيوتن</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Newton's Laws</h4>
                            <p class="text-gray-600 mb-4 ar">دراسة قوانين نيوتن والاحتكاك على السطوح المائلة</p>
                            <p class="text-gray-600 mb-4 en hidden">Study Newton's laws and friction on inclined surfaces</p>
                            <a href="newton_laws.html" class="text-blue-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="newton_laws.html" class="text-blue-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-green-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-route text-4xl text-green-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">حفظ الطاقة</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Energy Conservation</h4>
                            <p class="text-gray-600 mb-4 ar">تحولات الطاقة الحركية والكامنة على الأفعوانية</p>
                            <p class="text-gray-600 mb-4 en hidden">Kinetic and potential energy transformations on roller coaster</p>
                            <a href="energy_conservation.html" class="text-green-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="energy_conservation.html" class="text-green-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-purple-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-circle text-4xl text-purple-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">مختبر التصادم</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Collision Lab</h4>
                            <p class="text-gray-600 mb-4 ar">حفظ الزخم في التصادمات المرنة وغير المرنة</p>
                            <p class="text-gray-600 mb-4 en hidden">Momentum conservation in elastic and inelastic collisions</p>
                            <a href="collision_lab.html" class="text-purple-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="collision_lab.html" class="text-purple-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-yellow-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-balance-scale text-4xl text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">العزم والتوازن</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Torque & Balance</h4>
                            <p class="text-gray-600 mb-4 ar">الاتزان الدوراني وشروط التوازن</p>
                            <p class="text-gray-600 mb-4 en hidden">Rotational equilibrium and balance conditions</p>
                            <a href="torque_balance.html" class="text-yellow-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="torque_balance.html" class="text-yellow-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-indigo-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-pendulum text-4xl text-indigo-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">البندول البسيط</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Simple Pendulum</h4>
                            <p class="text-gray-600 mb-4 ar">الحركة التوافقية البسيطة والزمن الدوري</p>
                            <p class="text-gray-600 mb-4 en hidden">Simple harmonic motion and periodic time</p>
                            <a href="pendulum.html" class="text-indigo-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="pendulum.html" class="text-indigo-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thermodynamics Labs -->
            <div class="mb-8">
                <h3 class="text-2xl font-bold mb-6 ar">الديناميكا الحرارية</h3>
                <h3 class="text-2xl font-bold mb-6 en hidden">Thermodynamics</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-blue-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-flask text-4xl text-blue-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">قانون الغاز المثالي</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Ideal Gas Law</h4>
                            <p class="text-gray-600 mb-4 ar">العلاقة بين الضغط والحجم ودرجة الحرارة</p>
                            <p class="text-gray-600 mb-4 en hidden">Relationship between pressure, volume and temperature</p>
                            <a href="ideal_gas.html" class="text-blue-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="ideal_gas.html" class="text-blue-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-red-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-cog text-4xl text-red-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">محرك كارنو</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Carnot Engine</h4>
                            <p class="text-gray-600 mb-4 ar">القانون الثاني للديناميكا الحرارية والكفاءة</p>
                            <p class="text-gray-600 mb-4 en hidden">Second law of thermodynamics and efficiency</p>
                            <a href="carnot_engine.html" class="text-red-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="carnot_engine.html" class="text-red-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-orange-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-fire text-4xl text-orange-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">انتقال الحرارة</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Heat Transfer</h4>
                            <p class="text-gray-600 mb-4 ar">التوصيل والحمل والإشعاع</p>
                            <p class="text-gray-600 mb-4 en hidden">Conduction, convection, and radiation</p>
                            <a href="heat_transfer.html" class="text-orange-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="heat_transfer.html" class="text-orange-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-cyan-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-cube text-4xl text-cyan-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">الحالات الثلاث للمادة</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">States of Matter</h4>
                            <p class="text-gray-600 mb-4 ar">تحولات الطور والحركة الجزيئية</p>
                            <p class="text-gray-600 mb-4 en hidden">Phase transitions and molecular motion</p>
                            <a href="states_of_matter.html" class="text-cyan-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="states_of_matter.html" class="text-cyan-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-green-100 flex items-center justify-center">
                            <div class="lab-equipment bg-white p-4 rounded-lg shadow-lg z-10">
                                <i class="fas fa-thermometer-half text-4xl text-green-600"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-xl font-semibold mb-2 ar">السعة الحرارية</h4>
                            <h4 class="text-xl font-semibold mb-2 en hidden">Specific Heat</h4>
                            <p class="text-gray-600 mb-4 ar">التوازن الحراري والمسعر</p>
                            <p class="text-gray-600 mb-4 en hidden">Thermal equilibrium and calorimetry</p>
                            <a href="specific_heat.html" class="text-green-600 font-medium flex items-center ar">
                                <span>الدخول إلى المختبر</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                            <a href="specific_heat.html" class="text-green-600 font-medium flex items-center en hidden">
                                <span>Enter Lab</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-8">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">
                    <i class="fas fa-flask mr-2"></i>
                    استكشف جميع المختبرات
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">
                    <i class="fas fa-flask mr-2"></i>
                    Explore All Labs
                </button>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">الدروس التعليمية</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Learning Courses</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 ar">
                تعلم الفيزياء من خلال دروس تفاعلية تغطي المفاهيم الأساسية مع أمثلة تطبيقية وتمارين تفاعلية
            </p>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12 en hidden">
                Learn physics through interactive lessons covering fundamental concepts with practical examples and interactive exercises
            </p>

            <!-- Courses Listing -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Course: Classical Mechanics -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-indigo-500 transition-all">
                    <div class="h-48 bg-indigo-100 flex items-center justify-center">
                        <i class="fas fa-weight-hanging text-6xl text-indigo-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h3 class="text-xl font-semibold ar">الميكانيكا الكلاسيكية</h3>
                                <h3 class="text-xl font-semibold en hidden">Classical Mechanics</h3>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full ar">12 درس</span>
                                <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full en hidden">12 Lessons</span>
                                <span class="text-yellow-500" aria-label="rating">★ 4.8</span>
                            </div>
                        </div>

                        <p class="text-gray-600 mb-4 ar">
                            تعلم أساسيات الحركة، القوى، الطاقة والزخم من خلال أمثلة تفاعلية وتطبيقات عملية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Learn the basics of motion, forces, energy and momentum through interactive examples and practical applications
                        </p>

                        <!-- Lessons inside Course -->
                        <ul class="space-y-2 mb-4">
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-indigo-500"></i>
                                    <a class="text-indigo-600 hover:underline ar" href="newton_laws.html" title="قوانين نيوتن">قوانين نيوتن للحركة</a>
                                    <a class="text-indigo-600 hover:underline en hidden" href="newton_laws.html" title="Newton's Laws">Newton's Laws of Motion</a>
                                </div>
                                <span class="text-xs text-gray-500">10:32</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-indigo-500"></i>
                                    <a class="text-indigo-600 hover:underline ar" href="kinematics_1d.html" title="الحركة الخطية">الحركة الخطية (Kinematics 1D)</a>
                                    <a class="text-indigo-600 hover:underline en hidden" href="kinematics_1d.html" title="1D Kinematics">1D Kinematics</a>
                                </div>
                                <span class="text-xs text-gray-500">08:47</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-indigo-500"></i>
                                    <a class="text-indigo-600 hover:underline ar" href="mass_spring.html" title="كتلة-نابض">الاهتزاز البسيط: كتلة-نابض</a>
                                    <a class="text-indigo-600 hover:underline en hidden" href="mass_spring.html" title="Mass-Spring">Simple Harmonic Motion</a>
                                </div>
                                <span class="text-xs text-gray-500">11:05</span>
                            </li>
                        </ul>

                        <div class="flex justify-between items-center">
                            <a href="#simulations" class="text-indigo-600 font-medium flex items-center gap-2 ar">
                                <span>جرّب المحاكاة المتعلقة</span>
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <a href="#simulations" class="text-indigo-600 font-medium flex items-center gap-2 en hidden">
                                <span>Try Related Simulations</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <button class="bg-indigo-600 text-white px-3 py-1.5 rounded text-sm" aria-label="بدء الدورة" data-action="start-course" data-course="mechanics">
                                <span class="ar">بدء الدورة</span>
                                <span class="en hidden">Start Course</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Course: Thermodynamics -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-red-500 transition-all">
                    <div class="h-48 bg-red-100 flex items-center justify-center">
                        <i class="fas fa-temperature-high text-6xl text-red-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h3 class="text-xl font-semibold ar">الديناميكا الحرارية</h3>
                                <h3 class="text-xl font-semibold en hidden">Thermodynamics</h3>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full ar">8 درس</span>
                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full en hidden">8 Lessons</span>
                                <span class="text-yellow-500" aria-label="rating">★ 4.6</span>
                            </div>
                        </div>

                        <p class="text-gray-600 mb-4 ar">
                            فهم القوانين الأساسية للديناميكا الحرارية وتطبيقاتها في المحركات الحرارية والأنظمة الحرارية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Understand the fundamental laws of thermodynamics and their applications in heat engines and thermal systems
                        </p>

                        <ul class="space-y-2 mb-4">
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-red-500"></i>
                                    <span class="ar">القانون الأول للديناميكا الحرارية</span>
                                    <span class="en hidden">First Law of Thermodynamics</span>
                                </div>
                                <span class="text-xs text-gray-500">09:10</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-red-500"></i>
                                    <span class="ar">القانون الثاني والإنتروبيا</span>
                                    <span class="en hidden">Second Law and Entropy</span>
                                </div>
                                <span class="text-xs text-gray-500">12:24</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-red-500"></i>
                                    <span class="ar">المحركات الحرارية والكفاءة</span>
                                    <span class="en hidden">Heat Engines and Efficiency</span>
                                </div>
                                <span class="text-xs text-gray-500">10:05</span>
                            </li>
                        </ul>

                        <div class="flex justify-between items-center">
                            <a href="#simulations" class="text-red-600 font-medium flex items-center gap-2 ar">
                                <span>جرّب محاكاة الحرارة</span>
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <a href="#simulations" class="text-red-600 font-medium flex items-center gap-2 en hidden">
                                <span>Try Heat Simulations</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <button class="bg-red-600 text-white px-3 py-1.5 rounded text-sm" aria-label="بدء الدورة" data-action="start-course" data-course="thermo">
                                <span class="ar">بدء الدورة</span>
                                <span class="en hidden">Start Course</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Course: Electromagnetism -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:border-blue-500 transition-all">
                    <div class="h-48 bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-bolt text-6xl text-blue-600"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h3 class="text-xl font-semibold ar">الكهرومغناطيسية</h3>
                                <h3 class="text-xl font-semibold en hidden">Electromagnetism</h3>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ar">10 درس</span>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full en hidden">10 Lessons</span>
                                <span class="text-yellow-500" aria-label="rating">★ 4.9</span>
                            </div>
                        </div>

                        <p class="text-gray-600 mb-4 ar">
                            استكشف العالم المذهل للكهرباء والمغناطيسية وتفاعلاتهما من خلال تجارب وتطبيقات عملية
                        </p>
                        <p class="text-gray-600 mb-4 en hidden">
                            Explore the fascinating world of electricity and magnetism and their interactions through experiments and practical applications
                        </p>

                        <ul class="space-y-2 mb-4">
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-blue-500"></i>
                                    <span class="ar">المجالات الكهربائية</span>
                                    <span class="en hidden">Electric Fields</span>
                                </div>
                                <span class="text-xs text-gray-500">07:55</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-blue-500"></i>
                                    <span class="ar">المجالات المغناطيسية</span>
                                    <span class="en hidden">Magnetic Fields</span>
                                </div>
                                <span class="text-xs text-gray-500">09:33</span>
                            </li>
                            <li class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-play-circle text-blue-500"></i>
                                    <span class="ar">الدوائر والتيار المتردد</span>
                                    <span class="en hidden">AC Circuits</span>
                                </div>
                                <span class="text-xs text-gray-500">10:12</span>
                            </li>
                        </ul>

                        <div class="flex justify-between items-center">
                            <a href="#simulations" class="text-blue-600 font-medium flex items-center gap-2 ar">
                                <span>جرّب محاكاة الكهرباء</span>
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <a href="#simulations" class="text-blue-600 font-medium flex items-center gap-2 en hidden">
                                <span>Try Electric Simulations</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <button class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm" aria-label="بدء الدورة" data-action="start-course" data-course="em">
                                <span class="ar">بدء الدورة</span>
                                <span class="en hidden">Start Course</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Lessons Cards -->
            <div class="mt-12">
                <h3 class="text-2xl font-bold mb-6 ar">دروس تفاعلية جديدة</h3>
                <h3 class="text-2xl font-bold mb-6 en hidden">New Interactive Lessons</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <a href="kinematics_1d.html" class="block bg-white rounded-xl shadow hover:shadow-lg transition overflow-hidden border border-gray-200">
                        <div class="h-36 bg-indigo-50 flex items-center justify-center">
                            <i class="fas fa-arrows-alt-h text-5xl text-indigo-600"></i>
                        </div>
                        <div class="p-4">
                            <h4 class="font-semibold ar">الحركة الخطية (1D)</h4>
                            <h4 class="font-semibold en hidden">1D Kinematics</h4>
                            <p class="text-sm text-gray-600 ar">محاكاة x–t, v–t, a–t ومسارات متعددة</p>
                            <p class="text-sm text-gray-600 en hidden">x–t, v–t, a–t with multi-paths</p>
                        </div>
                    </a>
                    <a href="newton_laws.html" class="block bg-white rounded-xl shadow hover:shadow-lg transition overflow-hidden border border-gray-200">
                        <div class="h-36 bg-emerald-50 flex items-center justify-center">
                            <i class="fas fa-balance-scale-right text-5xl text-emerald-600"></i>
                        </div>
                        <div class="p-4">
                            <h4 class="font-semibold ar">قوانين نيوتن للحركة</h4>
                            <h4 class="font-semibold en hidden">Newton's Laws</h4>
                            <p class="text-sm text-gray-600 ar">كتلة على سطح مائل مع احتكاك وقوة خارجية</p>
                            <p class="text-sm text-gray-600 en hidden">Inclined plane with friction and external force</p>
                        </div>
                    </a>
                    <a href="mass_spring.html" class="block bg-white rounded-xl shadow hover:shadow-lg transition overflow-hidden border border-gray-200">
                        <div class="h-36 bg-purple-50 flex items-center justify-center">
                            <i class="fas fa-wave-square text-5xl text-purple-600"></i>
                        </div>
                        <div class="p-4">
                            <h4 class="font-semibold ar">كتلة-نابض (SHM)</h4>
                            <h4 class="font-semibold en hidden">Mass-Spring (SHM)</h4>
                            <p class="text-sm text-gray-600 ar">تغيير m و k والتخميد مع رسوم x–t و v–t</p>
                            <p class="text-sm text-gray-600 en hidden">Adjust m, k, damping with x–t & v–t</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3ص font-bold text-center mb-4 ar">لماذا تختار منصتنا؟</h2>
            <h2 class="text-3ص font-bold text-center mb-4 en hidden">Why Choose Our Platform?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-indigo-100 w-16 ه-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-laptop-code text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">محاكاة تفاعلية</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Interactive Simulations</h3>
                    <p class="text-gray-600 ar">تجارب فيزيائية تفاعلية...</p>
                    <p class="text-gray-600 en hidden">Interactive physics experiments...</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-flask text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">مختبرات افتراضية</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Virtual Labs</h3>
                    <p class="text-gray-600 ar">بيئة معملية آمنة...</p>
                    <p class="text-gray-600 en hidden">Safe lab environment...</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-language text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 ar">دعم ثنائي اللغة</h3>
                    <h3 class="text-xl font-semibold mb-2 en hidden">Bilingual Support</h3>
                    <p class="text-gray-600 ar">واجهة مستخدم متكاملة...</p>
                    <p class="text-gray-600 en hidden">Fully integrated Arabic/English interface...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-8 md:mb-0">
                    <h2 class="text-3xl font-bold mb-4 ar">عن منصة فيزيكس</h2>
                    <h2 class="text-3xl font-bold mb-4 en hidden">About Physix Platform</h2>
                    <p class="text-gray-600 mb-4 ar">فيزيكس هي منصة تعليمية مبتكرة...</p>
                    <p class="text-gray-600 mb-4 en hidden">Physix is an innovative educational platform...</p>
                    <p class="text-gray-600 mb-6 ar">مهمتنا هي جعل الفيزياء في متناول الجميع...</p>
                    <p class="text-gray-600 mb-6 en hidden">Our mission is to make physics accessible...</p>
                    <div class="flex space-x-4 ar:space-x-reverse">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow ar">تواصل معنا</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow ar">تعرف على فريقنا</button>
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg shadow en hidden">Contact Us</button>
                        <button class="bg-white hover:bg-gray-100 text-indigo-600 border border-indigo-600 px-6 py-3 rounded-lg shadow en hidden">Meet Our Team</button>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="bg-indigo-100 rounded-xl p-8">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">50+</h3>
                                <p class="text-gray-600 ar">محاكاة تفاعلية</p>
                                <p class="text-gray-600 en hidden">Interactive Simulations</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">15+</h3>
                                <p class="text-gray-600 ar">مختبر افتراضي</p>
                                <p class="text-gray-600 en hidden">Virtual Labs</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">30+</</h3>
                                <p class="text-gray-600 ar">درس تعليمي</p>
                                <p class="text-gray-600 en hidden">Learning Lessons</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow text-center">
                                <h3 class="text-3xl font-bold text-indigo-600 mb-2">10K+</h3>
                                <p class="text-gray-600 ar">طالب مستفيد</p>
                                <p class="text-gray-600 en hidden">Active Students</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4 ar">آراء طلابنا</h2>
            <h2 class="text-3xl font-bold text-center mb-4 en hidden">Student Testimonials</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">أحمد محمد</h4>
                            <p class="text-gray-500 text-sm ar">طالب فيزياء</p>
                            <p class="text-gray-500 text-sm en hidden">Physics Student</p>
                        </div>
                    </div>
                    <p class="text-gray-600 ar">"المحاكاة التفاعلية ساعدتني كثيرًا..."</p>
                    <p class="text-gray-600 en hidden">"The interactive simulations helped me a lot..."</p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">Sarah Johnson</h4>
                            <p class="text-gray-500 text-sm en">High School Teacher</p>
                            <p class="text-gray-500 text-sm ar hidden">معلمة فيزياء</p>
                        </div>
                    </div>
                    <p class="text-gray-600 en">"The virtual labs are an excellent resource..."</p>
                    <p class="text-gray-600 ar hidden">"المختبرات الافتراضية مصدر ممتاز..."</p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">علي عبد الرحمن</h4>
                            <p class="text-gray-500 text-sm ar">مهندس</p>
                            <p class="text-gray-500 text-sm en hidden">Engineer</p>
                        </div>
                    </div>
                    <p class="text-gray-600 ar">"الدروس ثنائية اللغة ساعدتني..."</p>
                    <p class="text-gray-600 en hidden">"The bilingual lessons helped me..."</p>
                    <div class="flex mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-12 bg-indigo-600 text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-4 ar">اشترك في نشرتنا البريدية</h2>
                <h2 class="text-3xl font-bold mb-4 en hidden">Subscribe to Our Newsletter</h2>
                <p class="mb-6 opacity-90 ar">احصل على آخر التحديثات...</p>
                <p class="mb-6 opacity-90 en hidden">Get the latest updates...</p>
                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="بريدك الإلكتروني" class="flex-grow px-4 py-3 rounded-lg text-gray-800 ar" dir="rtl">
                    <input type="email" placeholder="Your Email" class="flex-grow px-4 py-3 rounded-lg text-gray-800 en hidden" dir="ltr">
                    <button class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium shadow ar">اشتراك</button>
                    <button class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium shadow en hidden">Subscribe</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <i class="fas fa-atom text-2xl mr-3"></i>
                        <h3 class="text-xl font-bold ar">فيزيكس</h3>
                        <h3 class="text-xl font-bold en hidden">Physix</h3>
                    </div>
                    <p class="text-gray-400 mb-4 ar">منصة تعليمية تفاعلية...</p>
                    <p class="text-gray-400 mb-4 en hidden">Interactive online physics learning platform...</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">روابط سريعة</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-white ar">الرئيسية</a></li>
                        <li><a href="#simulations" class="text-gray-400 hover:text-white ar">المحاكاة</a></li>
                        <li><a href="#labs" class="text-gray-400 hover:text-white ar">المختبرات</a></li>
                        <li><a href="#courses" class="text-gray-400 hover:text-white ar">الدروس</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white ar">عن المنصة</a></li>
                        <li><a href="#home" class="text-gray-400 hover:text-white en hidden">Home</a></li>
                        <li><a href="#simulations" class="text-gray-400 hover:text-white en hidden">Simulations</a></li>
                        <li><a href="#labs" class="text-gray-400 hover:text-white en hidden">Labs</a></li>
                        <li><a href="#courses" class="text-gray-400 hover:text-white en hidden">Courses</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white en hidden">About</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">فروع الفيزياء</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Physics Branches</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الميكانيكا الكلاسيكية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الديناميكا الحرارية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الكهرومغناطيسية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">البصريات</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white ar">الفيزياء الحديثة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Classical Mechanics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Thermodynamics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Electromagnetism</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Optics</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white en hidden">Modern Physics</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 ar">تواصل معنا</h3>
                    <h3 class="text-lg font-semibold mb-4 en hidden">Contact Us</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-gray-400"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2 text-gray-400"></i>
                            <span class="text-gray-400">+966 12 345 6789</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                            <span class="text-gray-400 ar">الرياض، المملكة العربية السعودية</span>
                            <span class="text-gray-400 en hidden">Riyadh, Saudi Arabia</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p class="ar">&copy; 2025 فيزيكس. جميع الحقوق محفوظة.</p>
                <p class="en hidden">&copy; 2025 Physix. All rights reserved.</p>
                <p class="mt-2 text-sm">
                    <span class="ar">تطوير: د. محمد يعقوب إسماعيل - جامعة السودان للعلوم والتكنولوجيا - كلية الهندسة الطبية</span>
                    <span class="en hidden">Developed by: Dr. Mohammed Yagoub Esmail - Sudan University of Science & Technology - Biomedical Engineering</span>
                </p>
                <p class="mt-1 text-xs">
                    <span class="ar">للتواصل: <EMAIL> | هاتف: +249912867327, +966538076790</span>
                    <span class="en hidden">Contact: <EMAIL> | Phone: +249912867327, +966538076790</span>
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Utilities: HiDPI canvas scaler
        function setupHiDPICanvas(canvas) {
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            const cssWidth = canvas.clientWidth || canvas.offsetWidth;
            const cssHeight = canvas.clientHeight || canvas.offsetHeight;
            if (!cssWidth || !cssHeight) return ratio;
            canvas.width = Math.floor(cssWidth * ratio);
            canvas.height = Math.floor(cssHeight * ratio);
            const ctx = canvas.getContext('2d');
            ctx.setTransform(ratio, 0, 0, ratio, 0, 0);
            return ratio;
        }

        // Persisted settings
        const PREFS_KEYS = { lang: 'physix_lang', dark: 'physix_dark' };

        function applyLanguage(lang) {
            const isAr = lang === 'ar';
            document.documentElement.lang = isAr ? 'ar' : 'en';
            document.documentElement.dir = isAr ? 'rtl' : 'ltr';
            document.querySelectorAll('.ar').forEach(el => el.classList.toggle('hidden', !isAr));
            document.querySelectorAll('.en').forEach(el => el.classList.toggle('hidden', isAr));
        }

        function applyDarkMode(enabled) {
            document.body.classList.toggle('dark-mode', enabled);
            const icon = document.querySelector('#darkModeToggle i');
            if (icon) {
                icon.classList.toggle('fa-moon', !enabled);
                icon.classList.toggle('fa-sun', enabled);
            }
        }

        // Init persisted state
        (function initPrefs() {
            const savedLang = localStorage.getItem(PREFS_KEYS.lang);
            const savedDark = localStorage.getItem(PREFS_KEYS.dark);
            if (savedLang === 'ar' || savedLang === 'en') {
                applyLanguage(savedLang);
            }
            if (savedDark === 'true' || savedDark === 'false') {
                applyDarkMode(savedDark === 'true');
            }
        })();

        // Language toggle (data-attribute driven)
        document.querySelector('[data-action="toggle-language"]')?.addEventListener('click', () => {
            const current = document.documentElement.lang === 'ar' ? 'ar' : 'en';
            const next = current === 'ar' ? 'en' : 'ar';
            applyLanguage(next);
            localStorage.setItem(PREFS_KEYS.lang, next);
        });

        // Dark mode toggle (persist)
        document.querySelector('[data-action="toggle-darkmode"]')?.addEventListener('click', () => {
            const enabled = !document.body.classList.contains('dark-mode');
            applyDarkMode(enabled);
            localStorage.setItem(PREFS_KEYS.dark, String(enabled));
        });

        // Hero Simulation (Projectile-like bounce)
        const heroCanvas = document.getElementById('heroSimulation');
        const heroCtx = heroCanvas.getContext('2d');
        function sizeHero() { setupHiDPICanvas(heroCanvas); }
        sizeHero();

        const gravity = 0.2;
        let heroBall = { x: 50, y: (heroCanvas.clientHeight || 0) - 50, r: 10, vx: 3, vy: -8, color: '#4F46E5' };
        function heroDrawGround() {
            heroCtx.beginPath();
            heroCtx.moveTo(0, heroCanvas.clientHeight - 30);
            heroCtx.lineTo(heroCanvas.clientWidth, heroCanvas.clientHeight - 30);
            heroCtx.strokeStyle = '#6B7280';
            heroCtx.stroke();
            heroCtx.closePath();
        }
        function heroDrawBall() {
            heroCtx.beginPath();
            heroCtx.arc(heroBall.x, heroBall.y, heroBall.r, 0, Math.PI * 2);
            heroCtx.fillStyle = heroBall.color;
            heroCtx.fill();
            heroCtx.closePath();
        }
        let heroRunning = true;
        function heroUpdate() {
            if (!heroRunning) return;
            heroCtx.clearRect(0, 0, heroCanvas.clientWidth, heroCanvas.clientHeight);
            heroBall.x += heroBall.vx;
            heroBall.y += heroBall.vy;
            heroBall.vy += gravity;

            const groundY = (heroCanvas.clientHeight || 0) - 30;
            if (heroBall.y + heroBall.r > groundY) {
                heroBall.y = groundY - heroBall.r;
                heroBall.vy = -heroBall.vy * 0.6;
            }
            if (heroBall.x - heroBall.r > (heroCanvas.clientWidth || 0)) {
                heroBall.x = 50; heroBall.y = (heroCanvas.clientHeight || 0) - 50; heroBall.vx = 3; heroBall.vy = -8;
            }
            heroDrawGround();
            heroDrawBall();
            requestAnimationFrame(heroUpdate);
        }
        heroUpdate();

        document.querySelector('[data-action="hero-play"]')?.addEventListener('click', () => {
            if (!heroRunning) {
                heroRunning = true;
                heroUpdate();
            }
        });

        // Simulation 1 - Projectile Motion (simplified)
        const sim1Canvas = document.getElementById('simulation1');
        const sim1Ctx = sim1Canvas.getContext('2d');
        function sizeSim1() { setupHiDPICanvas(sim1Canvas); }
        sizeSim1();

        let sim1Ball = { x: 50, y: (sim1Canvas.clientHeight || 0) - 50, r: 8, vx: 5, vy: -10, color: '#4F46E5' };
        let sim1Running = false;

        function sim1DrawGround() {
            sim1Ctx.beginPath();
            sim1Ctx.moveTo(0, sim1Canvas.clientHeight - 30);
            sim1Ctx.lineTo(sim1Canvas.clientWidth, sim1Canvas.clientHeight - 30);
            sim1Ctx.strokeStyle = '#6B7280';
            sim1Ctx.stroke();
            sim1Ctx.closePath();
        }
        function sim1DrawBall() {
            sim1Ctx.beginPath();
            sim1Ctx.arc(sim1Ball.x, sim1Ball.y, sim1Ball.r, 0, Math.PI * 2);
            sim1Ctx.fillStyle = sim1Ball.color;
            sim1Ctx.fill();
            sim1Ctx.closePath();
        }
        function sim1Reset() {
            sim1Running = false;
            sim1Ball.x = 50;
            sim1Ball.y = (sim1Canvas.clientHeight || 0) - 50;
            sim1Ball.vx = 5;
            sim1Ball.vy = -10;
            sim1Ctx.clearRect(0, 0, sim1Canvas.clientWidth, sim1Canvas.clientHeight);
            sim1DrawGround();
            sim1DrawBall();
        }
        function sim1Update() {
            if (!sim1Running) return;
            sim1Ctx.clearRect(0, 0, sim1Canvas.clientWidth, sim1Canvas.clientHeight);
            sim1Ball.x += sim1Ball.vx;
            sim1Ball.y += sim1Ball.vy;
            sim1Ball.vy += gravity;

            const groundY = (sim1Canvas.clientHeight || 0) - 30;
            if (sim1Ball.y + sim1Ball.r > groundY) {
                sim1Ball.y = groundY - sim1Ball.r;
                sim1Ball.vy = -sim1Ball.vy * 0.6;
            }
            if (sim1Ball.x - sim1Ball.r > (sim1Canvas.clientWidth || 0)) {
                sim1Reset();
            }
            sim1DrawGround();
            sim1DrawBall();
            requestAnimationFrame(sim1Update);
        }
        // Init drawing for sim1
        sim1DrawGround(); sim1DrawBall();

        // Hook buttons using data-action/data-target
        document.querySelectorAll('[data-action="run-sim"]').forEach(btn => {
            btn.addEventListener('click', () => {
                const targetId = btn.getAttribute('data-target');
                if (targetId === 'simulation1') {
                    if (!sim1Running) {
                        sim1Running = true;
                        sim1Update();
                    }
                }
            });
        });
        document.querySelectorAll('[data-action="reset-sim"]').forEach(btn => {
            btn.addEventListener('click', () => {
                const targetId = btn.getAttribute('data-target');
                if (targetId === 'simulation1') {
                    sim1Reset();
                } else if (targetId === 'simulation2') {
                    voltage = 6; resistance = 10; current = voltage / resistance;
                    const container = document.getElementById('simulation2').closest('.simulation-window');
                    const sliders = container.querySelectorAll('input[type="range"]');
                    sliders.forEach(sl => {
                        if (sl.dataset.param === 'voltage') sl.value = voltage;
                        if (sl.dataset.param === 'resistance') sl.value = resistance;
                    });
                    drawCircuit();
                }
            });
        });

        // Sliders for sim1 parameters (v0, theta) - hooks prepared for future extension
        document.querySelectorAll('#simulation1').forEach(canvas => {
            const container = canvas.closest('.simulation-window');
            const sliders = container?.querySelectorAll('input[type="range"]') || [];
            sliders.forEach(slider => {
                slider.addEventListener('input', () => {
                    // Optional: compute vx, vy from v0 and theta
                    const v0 = Number(container.querySelector('input[data-param="v0"]')?.value || 50);
                    const thetaDeg = Number(container.querySelector('input[data-param="theta"]')?.value || 45);
                    const theta = thetaDeg * Math.PI / 180;
                    const scale = 0.12; // scale factor to map m/s to px/frame roughly
                    sim1Ball.vx = Math.cos(theta) * v0 * scale;
                    sim1Ball.vy = -Math.sin(theta) * v0 * scale;
                });
            });
        });

        // Simulation 2 - Electric Circuit (static compute, redraw on change)
        const sim2Canvas = document.getElementById('simulation2');
        const sim2Ctx = sim2Canvas.getContext('2d');
        function sizeSim2() { setupHiDPICanvas(sim2Canvas); }
        sizeSim2();

        let voltage = 6;
        let resistance = 10;
        let current = voltage / resistance;

        function drawCircuit() {
            sim2Ctx.clearRect(0, 0, sim2Canvas.clientWidth, sim2Canvas.clientHeight);
            // battery
            sim2Ctx.fillStyle = '#EF4444';
            sim2Ctx.fillRect(50, sim2Canvas.clientHeight/2 - 30, 30, 60);
            // resistor
            sim2Ctx.fillStyle = '#10B981';
            sim2Ctx.fillRect(sim2Canvas.clientWidth/2 - 15, sim2Canvas.clientHeight/2 - 20, 30, 40);
            // wires
            sim2Ctx.beginPath();
            sim2Ctx.moveTo(80, sim2Canvas.clientHeight/2);
            sim2Ctx.lineTo(sim2Canvas.clientWidth/2 - 15, sim2Canvas.clientHeight/2);
            sim2Ctx.moveTo(sim2Canvas.clientWidth/2 + 15, sim2Canvas.clientHeight/2);
            sim2Ctx.lineTo(sim2Canvas.clientWidth - 50, sim2Canvas.clientHeight/2);
            sim2Ctx.lineTo(sim2Canvas.clientWidth - 50, sim2Canvas.clientHeight/2 + 60);
            sim2Ctx.lineTo(50, sim2Canvas.clientHeight/2 + 60);
            sim2Ctx.lineTo(50, sim2Canvas.clientHeight/2);
            sim2Ctx.strokeStyle = '#000';
            sim2Ctx.lineWidth = 2;
            sim2Ctx.stroke();
            // values
            sim2Ctx.fillStyle = '#3B82F6';
            sim2Ctx.font = '14px Arial';
            sim2Ctx.fillText(`I = ${current.toFixed(2)} A`, sim2Canvas.clientWidth/2 - 20, sim2Canvas.clientHeight/2 - 30);
            sim2Ctx.fillStyle = '#000';
            sim2Ctx.font = '12px Arial';
            sim2Ctx.fillText(`${voltage}V`, 40, sim2Canvas.clientHeight/2 - 40);
            sim2Ctx.fillText(`${resistance}Ω`, sim2Canvas.clientWidth/2 - 10, sim2Canvas.clientHeight/2 - 40);
        }
        drawCircuit();

        // Update circuit on slider change using data-param
        (function initCircuitSliders(){
            const container = sim2Canvas.closest('.simulation-window');
            if (!container) return;
            container.querySelectorAll('input[type="range"]').forEach(slider => {
                slider.addEventListener('input', () => {
                    if (slider.dataset.param === 'voltage') {
                        voltage = parseInt(slider.value, 10);
                    } else if (slider.dataset.param === 'resistance') {
                        resistance = parseInt(slider.value, 10);
                    }
                    current = voltage / Math.max(resistance, 0.0001);
                    drawCircuit();
                });
            });
        })();

        // Resize handling (debounced), re-apply DPR scaling and redraw
        let resizeTid;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTid);
            resizeTid = setTimeout(() => {
                sizeHero();
                sizeSim1();
                sizeSim2();
                // Reposition states relative to new sizes
                heroBall.y = (heroCanvas.clientHeight || 0) - 50;
                if (!sim1Running) sim1Reset(); else { /* keep running path */ }
                drawCircuit();
            }, 150);
        });

        // Courses: example hooks
        document.querySelectorAll('[data-action="start-course"]').forEach(btn => {
            btn.addEventListener('click', () => {
                const course = btn.getAttribute('data-course');
                // Placeholder navigation/behavior – can be wired to dedicated pages later
                if (course === 'mechanics') {
                    location.hash = 'simulations';
                    // Optionally scroll smoothly
                    document.getElementById('simulations')?.scrollIntoView({ behavior: 'smooth' });
                } else if (course === 'thermo') {
                    location.hash = 'simulations';
                    document.getElementById('simulations')?.scrollIntoView({ behavior: 'smooth' });
                } else if (course === 'em') {
                    location.hash = 'simulations';
                    document.getElementById('simulations')?.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        document.querySelectorAll('[data-action="view-all-courses"]').forEach(btn => {
            btn.addEventListener('click', () => {
                // For now, just keep user on courses section; extend to separate page later
                document.getElementById('courses')?.scrollIntoView({ behavior: 'smooth' });
            });
        });
    </script>
</body>
</html>
