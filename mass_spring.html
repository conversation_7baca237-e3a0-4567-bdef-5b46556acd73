<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>كتلة-نابض (SHM) | فيزيكس</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles/mass_spring.css">
</head>
<body class="bg-gray-50">
  <header class="bg-gradient-to-r from-purple-600 to-fuchsia-600 text-white py-4 shadow-md">
    <div class="container mx-auto px-4 flex justify-between items-center">
      <div class="flex items-center">
        <i class="fas fa-wave-square text-2xl mr-3"></i>
        <h1 class="text-2xl font-bold">الاهتزاز البسيط: كتلة-نابض</h1>
      </div>
      <nav class="flex items-center gap-4">
        <a href="index.html" class="hover:text-fuchsia-100">العودة للرئيسية</a>
        <button id="darkModeToggle" class="bg-black/20 px-3 py-1.5 rounded">الوضع الليلي</button>
      </nav>
    </div>
  </header>

  <main class="container mx-auto px-4 py-6">
    <section class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2 simulation-window bg-white rounded-xl shadow p-4">
        <div class="flex justify-between items-center mb-3">
          <h2 class="text-xl font-semibold">كتلة على نابض أفقي مع تخميد اختياري</h2>
          <button id="resetBtn" class="text-purple-600 text-sm flex items-center gap-1">
            <i class="fas fa-redo"></i><span>إعادة تعيين</span>
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="m">الكتلة m (كغ)</label>
            <input id="m" type="range" min="0.2" max="5" value="1" step="0.1" class="w-full" title="الكتلة">
            <div class="text-xs text-gray-500 mt-1"><span id="mVal">1.0</span> kg</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="k">ثابت النابض k (ن/م)</label>
            <input id="k" type="range" min="5" max="200" value="50" step="5" class="w-full" title="ثابت النابض">
            <div class="text-xs text-gray-500 mt-1"><span id="kVal">50</span> N/m</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="b">تخميد b (كغ/ث)</label>
            <input id="b" type="range" min="0" max="5" value="0.2" step="0.1" class="w-full" title="التخميد">
            <div class="text-xs text-gray-500 mt-1"><span id="bVal">0.2</span> kg/s</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="A">السعة A (م)</label>
            <input id="A" type="range" min="0" max="2" value="0.5" step="0.05" class="w-full" title="السعة">
            <div class="text-xs text-gray-500 mt-1"><span id="AVal">0.50</span> m</div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="phi">الطور φ (°)</label>
            <input id="phi" type="range" min="0" max="360" value="0" step="5" class="w-full" title="الطور">
            <div class="text-xs text-gray-500 mt-1"><span id="phiVal">0</span> deg</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1" for="dt">Δt (ثانية)</label>
            <input id="dt" type="range" min="0.01" max="0.1" value="0.02" step="0.005" class="w-full" title="خطوة الزمن">
            <div class="text-xs text-gray-500 mt-1"><span id="dtVal">0.02</span> s</div>
          </div>
          <div class="flex items-end">
            <button id="playPause" class="bg-purple-600 text-white px-3 py-2 rounded text-sm mr-2">تشغيل</button>
            <button id="stepBtn" class="bg-white border px-3 py-2 rounded text-sm">خطوة +0.05 ث</button>
          </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">مشهد النابض والكتلة</div>
            <canvas id="canvasSystem" class="w-full h-64"></canvas>
          </div>
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">x(t), v(t), الطاقة</div>
            <canvas id="canvasPlots" class="w-full h-64"></canvas>
          </div>
        </div>
      </div>

      <aside class="bg-white rounded-xl shadow p-4">
        <h3 class="text-lg font-semibold mb-3">النتائج</h3>
        <ul class="space-y-2 text-sm text-gray-700">
          <li>التردد الزاوي ω = sqrt(k/m): <span id="omegaVal">0.00</span> rad/s</li>
          <li>ζ = b/(2√(km)) (نسبة التخميد): <span id="zetaVal">0.00</span></li>
          <li>الموضع x(t): <span id="xVal">0.00</span> m</li>
          <li>السرعة v(t): <span id="vVal">0.00</span> m/s</li>
          <li>الطاقة الكلية E(t): <span id="eVal">0.00</span> J</li>
        </ul>
        <div class="mt-4 text-xs text-gray-600 leading-6">
          معادلة الحركة المخمدة: m x'' + b x' + k x = 0.
          في حالة b=0 نحصل على SHM بزاوية ω = √(k/m).
          الطاقة الكلية E = ½ k x² + ½ m v².
        </div>
        <div class="mt-4">
          <a href="index.html" class="text-purple-600 text-sm">عودة إلى المنصة الرئيسية</a>
        </div>
      </aside>
    </section>
  </main>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" defer></script>
  <script src="scripts/mass_spring.js"></script>
</body>
</html>
