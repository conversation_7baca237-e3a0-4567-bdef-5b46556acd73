/**
 * PhysicsHub - Carnot Engine Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: hot/cold temperatures, heat input, cycle speed
 * - p5.js visualization: Carnot cycle diagram with P-V plot
 * - Live dashboard: efficiency, energy values, temperatures
 * - Chart.js: efficiency vs temperature difference
 * - Preset configurations for different engine types
 */

// -------------------- i18n (AR/EN) --------------------
const CARDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "محرك كارنو الحراري",
    lesson_title: "محرك كارنو الحراري: القانون الثاني للديناميكا الحرارية والكفاءة",
    lesson_lead: "استكشف المحرك الحراري المثالي واكتشف حدود الكفاءة في تحويل الحرارة إلى شغل. تعلم كيف تعتمد الكفاءة القصوى على درجات حرارة المستودعين الساخن والبارد فقط.",
    theory_title: "المفهوم الأساسي",
    t1: "محرك كارنو: المحرك الحراري الأكثر كفاءة نظرياً",
    t2: "يعمل بين مستودعين حراريين عند درجتي حرارة ثابتتين",
    t3: "الكفاءة: η = 1 - T_C/T_H (بالكلفن)",
    t4: "الكفاءة تعتمد فقط على درجات الحرارة",
    t5: "لا يمكن الوصول إلى كفاءة 100% (القانون الثاني)",
    lab_title: "المعلمات التفاعلية",
    temp_hot_label: "درجة حرارة المستودع الساخن T_H (K)",
    temp_cold_label: "درجة حرارة المستودع البارد T_C (K)",
    heat_input_label: "الحرارة الداخلة Q_H (J)",
    cycle_speed_label: "سرعة الدورة",
    presets_title: "إعدادات مسبقة",
    preset_steam: "محرك بخاري",
    preset_car: "محرك سيارة",
    preset_power: "محطة طاقة",
    reset: "إعادة ضبط",
    start: "بدء الدورة",
    efficiency_title: "الكفاءة",
    energy_title: "الطاقة",
    temperatures_title: "درجات الحرارة",
    dash_efficiency: "η",
    dash_max_efficiency: "η_max",
    dash_qh: "Q_H",
    dash_qc: "Q_C",
    dash_work: "W",
    dash_th: "T_H",
    dash_tc: "T_C",
    dash_delta_t: "ΔT",
    cta_title: "الهدف التعليمي",
    cta_text: "فهم أن كفاءة المحرك الحراري تعتمد على فرق درجات الحرارة بين المستودعين، وأنه من المستحيل الوصول إلى كفاءة 100% (القانون الثاني للديناميكا الحرارية). كلما زاد الفرق في درجات الحرارة، زادت الكفاءة القصوى الممكنة.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Carnot Heat Engine",
    lesson_title: "Carnot Heat Engine: Second Law of Thermodynamics and Efficiency",
    lesson_lead: "Explore the ideal heat engine and discover the limits of efficiency in converting heat to work. Learn how maximum efficiency depends only on the temperatures of the hot and cold reservoirs.",
    theory_title: "Core Concept",
    t1: "Carnot engine: The most efficient heat engine theoretically possible",
    t2: "Operates between two thermal reservoirs at constant temperatures",
    t3: "Efficiency: η = 1 - T_C/T_H (in Kelvin)",
    t4: "Efficiency depends only on temperatures",
    t5: "100% efficiency is impossible (Second Law)",
    lab_title: "Interactive Parameters",
    temp_hot_label: "Hot reservoir temperature T_H (K)",
    temp_cold_label: "Cold reservoir temperature T_C (K)",
    heat_input_label: "Heat input Q_H (J)",
    cycle_speed_label: "Cycle speed",
    presets_title: "Presets",
    preset_steam: "Steam Engine",
    preset_car: "Car Engine",
    preset_power: "Power Plant",
    reset: "Reset",
    start: "Start Cycle",
    efficiency_title: "Efficiency",
    energy_title: "Energy",
    temperatures_title: "Temperatures",
    dash_efficiency: "η",
    dash_max_efficiency: "η_max",
    dash_qh: "Q_H",
    dash_qc: "Q_C",
    dash_work: "W",
    dash_th: "T_H",
    dash_tc: "T_C",
    dash_delta_t: "ΔT",
    cta_title: "Learning Objective",
    cta_text: "Understand that heat engine efficiency depends on the temperature difference between reservoirs, and that 100% efficiency is impossible (Second Law of Thermodynamics). The greater the temperature difference, the higher the maximum possible efficiency.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let carLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyCarnotI18n() {
  const t = CARDICT[carLang];
  document.documentElement.lang = carLang === "ar" ? "ar" : "en";
  document.documentElement.dir = carLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
}

function setupLangToggleCarnot() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      carLang = carLang === "ar" ? "en" : "ar";
      applyCarnotI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let carParams = {
  tempHot: 600,    // Kelvin
  tempCold: 300,   // Kelvin
  heatInput: 500,  // Joules
  cycleSpeed: 1.0
};

// -------------------- Simulation State --------------------
let engineState = {
  efficiency: 0,
  maxEfficiency: 0,
  heatOutput: 0,
  workOutput: 0,
  isRunning: false,
  cyclePhase: 0, // 0-1 for animation
  pvData: []
};

// Engine presets
const enginePresets = {
  steam: { tempHot: 500, tempCold: 300, heatInput: 400 },
  car: { tempHot: 800, tempCold: 350, heatInput: 600 },
  power: { tempHot: 900, tempCold: 320, heatInput: 800 }
};

// -------------------- p5.js Sketch --------------------
let p5Instance;
let workspace;

function initP5Carnot() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  // Initialize workspace manager
  workspace = initializeWorkspace('carnot_engine');

  p5Instance = new p5((p) => {
    p.setup = () => {
      const dimensions = workspace.getCanvasDimensions();
      const canvas = p.createCanvas(dimensions.width, dimensions.height);
      canvas.parent(container);
      calculateEngineValues();
    };

    p.windowResized = () => {
      const dimensions = workspace.getCanvasDimensions();
      p.resizeCanvas(dimensions.width, dimensions.height);
    };

    p.draw = () => {
      p.background(15, 20, 35);

      // Draw workspace grid
      workspace.drawGrid(p, 0.02);

      // Draw engine diagram
      drawEngineSchematic(p);

      // Draw P-V diagram
      drawPVDiagram(p);

      // Update animation
      if (engineState.isRunning) {
        updateCycleAnimation();
      }
    };
  });
}

function drawEngineSchematic(p) {
  const centerX = p.width * 0.25;
  const centerY = p.height / 2;
  
  // Hot reservoir
  p.fill(220, 38, 38);
  p.noStroke();
  p.rect(centerX - 60, centerY - 80, 120, 30);
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text(`T_H = ${carParams.tempHot}K`, centerX, centerY - 60);
  
  // Engine (cylinder)
  p.fill(139, 92, 246);
  p.rect(centerX - 40, centerY - 20, 80, 40);
  p.fill(255);
  p.text("Engine", centerX, centerY + 5);
  
  // Cold reservoir
  p.fill(59, 130, 246);
  p.rect(centerX - 60, centerY + 50, 120, 30);
  p.fill(255);
  p.text(`T_C = ${carParams.tempCold}K`, centerX, centerY + 70);
  
  // Heat flow arrows
  p.stroke(220, 38, 38);
  p.strokeWeight(3);
  drawArrow(p, centerX, centerY - 50, centerX, centerY - 25);
  p.fill(255);
  p.noStroke();
  p.textAlign(p.LEFT);
  p.text(`Q_H = ${carParams.heatInput}J`, centerX + 50, centerY - 35);
  
  p.stroke(59, 130, 246);
  p.strokeWeight(3);
  drawArrow(p, centerX, centerY + 25, centerX, centerY + 50);
  p.fill(255);
  p.noStroke();
  p.text(`Q_C = ${engineState.heatOutput.toFixed(0)}J`, centerX + 50, centerY + 40);
  
  // Work output arrow
  p.stroke(16, 185, 129);
  p.strokeWeight(3);
  drawArrow(p, centerX + 40, centerY, centerX + 80, centerY);
  p.fill(255);
  p.noStroke();
  p.text(`W = ${engineState.workOutput.toFixed(0)}J`, centerX + 90, centerY + 5);
}

function drawPVDiagram(p) {
  const startX = p.width * 0.6;
  const startY = 50;
  const width = p.width * 0.35;
  const height = p.height - 100;
  
  // Draw axes
  p.stroke(255, 255, 255, 150);
  p.strokeWeight(2);
  p.line(startX, startY + height, startX + width, startY + height); // X-axis
  p.line(startX, startY, startX, startY + height); // Y-axis
  
  // Labels
  p.fill(255);
  p.noStroke();
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text("Volume", startX + width/2, startY + height + 20);
  
  p.push();
  p.translate(startX - 20, startY + height/2);
  p.rotate(-p.PI/2);
  p.text("Pressure", 0, 0);
  p.pop();
  
  // Draw Carnot cycle
  drawCarnotCycle(p, startX, startY, width, height);
}

function drawCarnotCycle(p, x, y, w, h) {
  // Carnot cycle has 4 processes:
  // 1. Isothermal expansion at T_H
  // 2. Adiabatic expansion
  // 3. Isothermal compression at T_C
  // 4. Adiabatic compression
  
  const points = [];
  const steps = 100;
  
  // Generate cycle points
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;
    let px, py;
    
    if (t <= 0.25) {
      // Isothermal expansion at T_H
      const s = t * 4;
      px = x + w * (0.2 + 0.3 * s);
      py = y + h * (0.2 + 0.1 * Math.log(1 + s));
    } else if (t <= 0.5) {
      // Adiabatic expansion
      const s = (t - 0.25) * 4;
      px = x + w * (0.5 + 0.2 * s);
      py = y + h * (0.3 + 0.3 * s);
    } else if (t <= 0.75) {
      // Isothermal compression at T_C
      const s = (t - 0.5) * 4;
      px = x + w * (0.7 - 0.2 * s);
      py = y + h * (0.6 + 0.1 * Math.log(1 + s));
    } else {
      // Adiabatic compression
      const s = (t - 0.75) * 4;
      px = x + w * (0.5 - 0.3 * s);
      py = y + h * (0.7 - 0.5 * s);
    }
    
    points.push({x: px, y: py});
  }
  
  // Draw cycle
  p.stroke(245, 158, 11);
  p.strokeWeight(3);
  p.noFill();
  p.beginShape();
  for (let point of points) {
    p.vertex(point.x, point.y);
  }
  p.endShape(p.CLOSE);
  
  // Draw current position if running
  if (engineState.isRunning) {
    const currentIndex = Math.floor(engineState.cyclePhase * points.length);
    const currentPoint = points[currentIndex] || points[0];
    
    p.fill(255, 107, 53);
    p.noStroke();
    p.circle(currentPoint.x, currentPoint.y, 12);
  }
}

function drawArrow(p, x1, y1, x2, y2) {
  p.line(x1, y1, x2, y2);
  
  const angle = Math.atan2(y2 - y1, x2 - x1);
  const arrowLength = 8;
  
  p.line(x2, y2, 
         x2 - arrowLength * Math.cos(angle - 0.3), 
         y2 - arrowLength * Math.sin(angle - 0.3));
  p.line(x2, y2, 
         x2 - arrowLength * Math.cos(angle + 0.3), 
         y2 - arrowLength * Math.sin(angle + 0.3));
}

function updateCycleAnimation() {
  engineState.cyclePhase += 0.01 * carParams.cycleSpeed;
  if (engineState.cyclePhase >= 1) {
    engineState.cyclePhase = 0;
  }
}

function calculateEngineValues() {
  // Carnot efficiency
  engineState.maxEfficiency = 1 - (carParams.tempCold / carParams.tempHot);
  engineState.efficiency = engineState.maxEfficiency; // Carnot engine is ideal
  
  // Energy calculations
  engineState.workOutput = carParams.heatInput * engineState.efficiency;
  engineState.heatOutput = carParams.heatInput - engineState.workOutput;
  
  updateCarnotDashboard();
  updateEfficiencyChart();
}

// -------------------- Controls --------------------
function bindCarnotControls() {
  const sliders = [
    { id: "tempHot", param: "tempHot", output: "tempHotOut", decimals: 0 },
    { id: "tempCold", param: "tempCold", output: "tempColdOut", decimals: 0 },
    { id: "heatInput", param: "heatInput", output: "heatInputOut", decimals: 0 },
    { id: "cycleSpeed", param: "cycleSpeed", output: "cycleSpeedOut", decimals: 1 }
  ];
  
  sliders.forEach(({ id, param, output, decimals }) => {
    const slider = document.getElementById(id);
    const outputEl = document.getElementById(output);
    
    if (slider && outputEl) {
      slider.addEventListener("input", () => {
        carParams[param] = parseFloat(slider.value);
        outputEl.textContent = carParams[param].toFixed(decimals);
        
        // Ensure T_H > T_C
        if (param === "tempHot" && carParams.tempHot <= carParams.tempCold) {
          carParams.tempHot = carParams.tempCold + 50;
          slider.value = carParams.tempHot;
          outputEl.textContent = carParams.tempHot.toFixed(decimals);
        }
        if (param === "tempCold" && carParams.tempCold >= carParams.tempHot) {
          carParams.tempCold = carParams.tempHot - 50;
          slider.value = carParams.tempCold;
          outputEl.textContent = carParams.tempCold.toFixed(decimals);
        }
        
        calculateEngineValues();
      });
    }
  });
  
  // Preset buttons
  document.querySelectorAll("[data-preset]").forEach(btn => {
    btn.addEventListener("click", () => {
      const preset = btn.getAttribute("data-preset");
      if (enginePresets[preset]) {
        const config = enginePresets[preset];
        
        carParams.tempHot = config.tempHot;
        carParams.tempCold = config.tempCold;
        carParams.heatInput = config.heatInput;
        
        // Update sliders
        document.getElementById("tempHot").value = config.tempHot;
        document.getElementById("tempCold").value = config.tempCold;
        document.getElementById("heatInput").value = config.heatInput;
        
        // Update outputs
        document.getElementById("tempHotOut").textContent = config.tempHot;
        document.getElementById("tempColdOut").textContent = config.tempCold;
        document.getElementById("heatInputOut").textContent = config.heatInput;
        
        calculateEngineValues();
      }
    });
  });
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", () => {
      carParams = {
        tempHot: 600,
        tempCold: 300,
        heatInput: 500,
        cycleSpeed: 1.0
      };
      
      // Update sliders
      document.getElementById("tempHot").value = 600;
      document.getElementById("tempCold").value = 300;
      document.getElementById("heatInput").value = 500;
      document.getElementById("cycleSpeed").value = 1.0;
      
      // Update outputs
      document.getElementById("tempHotOut").textContent = "600";
      document.getElementById("tempColdOut").textContent = "300";
      document.getElementById("heatInputOut").textContent = "500";
      document.getElementById("cycleSpeedOut").textContent = "1.0";
      
      engineState.isRunning = false;
      engineState.cyclePhase = 0;
      calculateEngineValues();
    });
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      engineState.isRunning = !engineState.isRunning;
      startBtn.textContent = engineState.isRunning ? 
        (carLang === "ar" ? "إيقاف الدورة" : "Stop Cycle") : 
        (carLang === "ar" ? "بدء الدورة" : "Start Cycle");
    });
  }
}

function updateCarnotDashboard() {
  // Efficiency values
  const efficiencyEl = document.getElementById("dashEfficiency");
  const maxEfficiencyEl = document.getElementById("dashMaxEfficiency");
  
  if (efficiencyEl) efficiencyEl.textContent = (engineState.efficiency * 100).toFixed(1);
  if (maxEfficiencyEl) maxEfficiencyEl.textContent = (engineState.maxEfficiency * 100).toFixed(1);
  
  // Energy values
  const qhEl = document.getElementById("dashQH");
  const qcEl = document.getElementById("dashQC");
  const workEl = document.getElementById("dashWork");
  
  if (qhEl) qhEl.textContent = carParams.heatInput.toFixed(0);
  if (qcEl) qcEl.textContent = engineState.heatOutput.toFixed(0);
  if (workEl) workEl.textContent = engineState.workOutput.toFixed(0);
  
  // Temperature values
  const thEl = document.getElementById("dashTH");
  const tcEl = document.getElementById("dashTC");
  const deltaTEl = document.getElementById("dashDeltaT");
  
  if (thEl) thEl.textContent = carParams.tempHot.toFixed(0);
  if (tcEl) tcEl.textContent = carParams.tempCold.toFixed(0);
  if (deltaTEl) deltaTEl.textContent = (carParams.tempHot - carParams.tempCold).toFixed(0);
}

// -------------------- Chart --------------------
let efficiencyChart;

function initEfficiencyChart() {
  const ctx = document.getElementById("efficiencyChart");
  if (!ctx) return;
  
  efficiencyChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Efficiency (%)',
        data: [],
        borderColor: '#dc2626',
        backgroundColor: 'rgba(220, 38, 38, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          title: { display: true, text: 'ΔT (K)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Efficiency (%)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' },
          min: 0,
          max: 100
        }
      },
      plugins: {
        legend: { 
          labels: { color: '#a9b4d0' }
        }
      }
    }
  });
}

function updateEfficiencyChart() {
  if (!efficiencyChart) return;
  
  // Generate efficiency curve for different temperature differences
  const data = [];
  const labels = [];
  
  for (let deltaT = 50; deltaT <= 700; deltaT += 50) {
    const tempHot = carParams.tempCold + deltaT;
    const efficiency = (1 - carParams.tempCold / tempHot) * 100;
    
    labels.push(deltaT.toString());
    data.push(efficiency);
  }
  
  efficiencyChart.data.labels = labels;
  efficiencyChart.data.datasets[0].data = data;
  
  efficiencyChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleCarnot();
  applyCarnotI18n();
  bindCarnotControls();
  initP5Carnot();
  initEfficiencyChart();
  calculateEngineValues();
});
