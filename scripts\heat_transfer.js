/**
 * PhysicsHub - Heat Transfer Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: temperatures, materials, geometry
 * - p5.js visualization: heat transfer modes with temperature gradients
 * - Live dashboard: heat flow rate, thermal resistance, material properties
 * - Chart.js: temperature distribution over time
 * - Three heat transfer modes: conduction, convection, radiation
 */

// -------------------- i18n (AR/EN) --------------------
const HTDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "مختبر انتقال الحرارة",
    lesson_title: "مختبر انتقال الحرارة: التوصيل والحمل والإشعاع",
    lesson_lead: "استكشف الطرق الثلاث لانتقال الحرارة من خلال محاكاة تفاعلية. شاهد كيف تنتقل الحرارة عبر المواد الصلبة والسوائل والغازات، وتعلم العوامل المؤثرة على معدل انتقال الحرارة.",
    theory_title: "المفهوم الأساسي",
    t1: "التوصيل: انتقال الحرارة عبر المواد الصلبة",
    t2: "الحمل: انتقال الحرارة عبر السوائل والغازات",
    t3: "الإشعاع: انتقال الحرارة عبر الموجات الكهرومغناطيسية",
    t4: "قانون فورييه: q = -kA(dT/dx)",
    t5: "معدل انتقال الحرارة يعتمد على المادة والمساحة وفرق درجة الحرارة",
    lab_title: "المعلمات التفاعلية",
    mode_title: "طريقة انتقال الحرارة",
    mode_conduction: "التوصيل",
    mode_convection: "الحمل",
    mode_radiation: "الإشعاع",
    temp_hot_label: "درجة الحرارة العالية T₁ (°C)",
    temp_cold_label: "درجة الحرارة المنخفضة T₂ (°C)",
    material_label: "نوع المادة",
    material_copper: "نحاس (k=400)",
    material_aluminum: "ألومنيوم (k=200)",
    material_steel: "فولاذ (k=50)",
    material_glass: "زجاج (k=1)",
    material_wood: "خشب (k=0.1)",
    thickness_label: "سمك المادة L (cm)",
    area_label: "المساحة A (cm²)",
    velocity_label: "سرعة السائل (m/s)",
    emissivity_label: "معامل الانبعاث ε",
    reset: "إعادة ضبط",
    start: "بدء المحاكاة",
    heat_flow_title: "تدفق الحرارة",
    material_props_title: "خصائص المادة",
    efficiency_title: "الكفاءة",
    dash_heat_rate: "q",
    dash_temp_diff: "ΔT",
    dash_conductivity: "k",
    dash_resistance: "R",
    dash_time_constant: "τ",
    dash_steady_state: "حالة مستقرة",
    steady_yes: "نعم",
    steady_no: "لا",
    cta_title: "الهدف التعليمي",
    cta_text: "فهم الطرق الثلاث لانتقال الحرارة وكيفية تأثير خصائص المادة والهندسة على معدل انتقال الحرارة. اكتشاف أن التوصيل يحدث في المواد الصلبة، والحمل في السوائل والغازات، والإشعاع لا يحتاج لوسط مادي.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Heat Transfer Lab",
    lesson_title: "Heat Transfer Lab: Conduction, Convection, and Radiation",
    lesson_lead: "Explore the three methods of heat transfer through interactive simulation. See how heat moves through solids, liquids, and gases, and learn the factors affecting heat transfer rate.",
    theory_title: "Core Concept",
    t1: "Conduction: heat transfer through solid materials",
    t2: "Convection: heat transfer through liquids and gases",
    t3: "Radiation: heat transfer through electromagnetic waves",
    t4: "Fourier's law: q = -kA(dT/dx)",
    t5: "Heat transfer rate depends on material, area, and temperature difference",
    lab_title: "Interactive Parameters",
    mode_title: "Heat Transfer Mode",
    mode_conduction: "Conduction",
    mode_convection: "Convection",
    mode_radiation: "Radiation",
    temp_hot_label: "Hot temperature T₁ (°C)",
    temp_cold_label: "Cold temperature T₂ (°C)",
    material_label: "Material type",
    material_copper: "Copper (k=400)",
    material_aluminum: "Aluminum (k=200)",
    material_steel: "Steel (k=50)",
    material_glass: "Glass (k=1)",
    material_wood: "Wood (k=0.1)",
    thickness_label: "Material thickness L (cm)",
    area_label: "Area A (cm²)",
    velocity_label: "Fluid velocity (m/s)",
    emissivity_label: "Emissivity ε",
    reset: "Reset",
    start: "Start Simulation",
    heat_flow_title: "Heat Flow",
    material_props_title: "Material Properties",
    efficiency_title: "Efficiency",
    dash_heat_rate: "q",
    dash_temp_diff: "ΔT",
    dash_conductivity: "k",
    dash_resistance: "R",
    dash_time_constant: "τ",
    dash_steady_state: "Steady State",
    steady_yes: "Yes",
    steady_no: "No",
    cta_title: "Learning Objective",
    cta_text: "Understand the three methods of heat transfer and how material properties and geometry affect heat transfer rate. Discover that conduction occurs in solids, convection in fluids, and radiation needs no medium.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let htLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyHeatTransferI18n() {
  const t = HTDICT[htLang];
  document.documentElement.lang = htLang === "ar" ? "ar" : "en";
  document.documentElement.dir = htLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  
  // Update material options
  updateMaterialOptions();
  updateSteadyStateText();
}

function setupLangToggleHeatTransfer() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      htLang = htLang === "ar" ? "en" : "ar";
      applyHeatTransferI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let htParams = {
  mode: "conduction", // conduction, convection, radiation
  tempHot: 100,       // °C
  tempCold: 20,       // °C
  material: "copper",
  thickness: 5,       // cm
  area: 50,          // cm²
  fluidVelocity: 1.0, // m/s (for convection)
  emissivity: 0.8     // for radiation
};

// Material properties (thermal conductivity in W/m·K)
const materials = {
  copper: { k: 400, name_ar: "نحاس", name_en: "Copper" },
  aluminum: { k: 200, name_ar: "ألومنيوم", name_en: "Aluminum" },
  steel: { k: 50, name_ar: "فولاذ", name_en: "Steel" },
  glass: { k: 1, name_ar: "زجاج", name_en: "Glass" },
  wood: { k: 0.1, name_ar: "خشب", name_en: "Wood" }
};

// -------------------- Simulation State --------------------
let heatState = {
  heatRate: 0,        // W
  thermalResistance: 0, // K/W
  timeConstant: 0,    // s
  isSteadyState: false,
  isRunning: false,
  temperatureProfile: [],
  time: 0
};

// -------------------- p5.js Sketch --------------------
let p5Instance;
let workspace;

function initP5HeatTransfer() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  // Initialize workspace manager
  workspace = initializeWorkspace('heat_transfer');

  p5Instance = new p5((p) => {
    p.setup = () => {
      const dimensions = workspace.getCanvasDimensions();
      const canvas = p.createCanvas(dimensions.width, dimensions.height);
      canvas.parent(container);
      calculateHeatTransfer();
    };

    p.windowResized = () => {
      const dimensions = workspace.getCanvasDimensions();
      p.resizeCanvas(dimensions.width, dimensions.height);
    };

    p.draw = () => {
      p.background(15, 20, 35);

      // Draw workspace grid and rulers
      workspace.drawGrid(p, 0.03);
      workspace.drawRulers(p, 0.2);

      // Draw heat transfer visualization based on mode
      if (htParams.mode === "conduction") {
        drawConduction(p);
      } else if (htParams.mode === "convection") {
        drawConvection(p);
      } else if (htParams.mode === "radiation") {
        drawRadiation(p);
      }

      // Update simulation
      if (heatState.isRunning) {
        updateHeatTransfer();
      }
    };
  });
}

function drawConduction(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2;
  const blockWidth = 200;
  const blockHeight = 80;
  
  // Draw material block
  p.fill(100, 100, 100);
  p.noStroke();
  p.rect(centerX - blockWidth/2, centerY - blockHeight/2, blockWidth, blockHeight);
  
  // Draw temperature gradient
  const segments = 20;
  for (let i = 0; i < segments; i++) {
    const x = centerX - blockWidth/2 + (i * blockWidth / segments);
    const temp = htParams.tempHot - (i / segments) * (htParams.tempHot - htParams.tempCold);
    const intensity = p.map(temp, htParams.tempCold, htParams.tempHot, 0, 255);
    
    p.fill(intensity, intensity * 0.3, 0, 150);
    p.rect(x, centerY - blockHeight/2, blockWidth / segments, blockHeight);
  }
  
  // Draw heat source and sink
  p.fill(220, 38, 38);
  p.rect(centerX - blockWidth/2 - 20, centerY - blockHeight/2 - 10, 15, blockHeight + 20);
  
  p.fill(59, 130, 246);
  p.rect(centerX + blockWidth/2 + 5, centerY - blockHeight/2 - 10, 15, blockHeight + 20);
  
  // Draw temperature labels
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(14);
  p.text(`${htParams.tempHot}°C`, centerX - blockWidth/2 - 12, centerY - blockHeight/2 - 15);
  p.text(`${htParams.tempCold}°C`, centerX + blockWidth/2 + 12, centerY - blockHeight/2 - 15);
  
  // Draw heat flow arrows
  drawHeatFlowArrows(p, centerX, centerY, blockWidth);
}

function drawConvection(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2;
  
  // Draw fluid container
  p.stroke(255, 255, 255, 100);
  p.strokeWeight(2);
  p.noFill();
  p.rect(centerX - 100, centerY - 80, 200, 160);
  
  // Draw hot surface at bottom
  p.fill(220, 38, 38);
  p.noStroke();
  p.rect(centerX - 100, centerY + 70, 200, 10);
  
  // Draw cold surface at top
  p.fill(59, 130, 246);
  p.rect(centerX - 100, centerY - 90, 200, 10);
  
  // Draw convection currents
  const time = p.millis() * 0.001;
  for (let i = 0; i < 8; i++) {
    const x = centerX - 80 + i * 20;
    const phase = i * 0.5 + time;
    
    // Rising hot current
    p.stroke(220, 38, 38, 150);
    p.strokeWeight(3);
    const y1 = centerY + 60;
    const y2 = centerY - 60;
    const curve = 10 * Math.sin(phase);
    
    p.line(x + curve, y1, x + curve * 0.5, y2);
    
    // Falling cold current
    p.stroke(59, 130, 246, 150);
    p.line(x + 10 - curve, y2, x + 10 - curve * 0.5, y1);
  }
  
  // Temperature labels
  p.fill(255);
  p.noStroke();
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text(`${htParams.tempHot}°C`, centerX, centerY + 95);
  p.text(`${htParams.tempCold}°C`, centerX, centerY - 95);
}

function drawRadiation(p) {
  const centerX = p.width / 2;
  const centerY = p.height / 2;
  
  // Draw hot object
  p.fill(220, 38, 38);
  p.noStroke();
  p.circle(centerX - 80, centerY, 60);
  
  // Draw cold object
  p.fill(59, 130, 246);
  p.circle(centerX + 80, centerY, 60);
  
  // Draw radiation waves
  const time = p.millis() * 0.002;
  for (let i = 0; i < 5; i++) {
    const radius = 30 + i * 15 + time * 50;
    const alpha = p.map(radius, 30, 150, 255, 0);
    
    if (alpha > 0) {
      p.stroke(255, 165, 0, alpha);
      p.strokeWeight(2);
      p.noFill();
      p.circle(centerX - 80, centerY, radius);
    }
  }
  
  // Temperature labels
  p.fill(255);
  p.noStroke();
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text(`${htParams.tempHot}°C`, centerX - 80, centerY + 50);
  p.text(`${htParams.tempCold}°C`, centerX + 80, centerY + 50);
}

function drawHeatFlowArrows(p, centerX, centerY, blockWidth) {
  const arrowCount = 5;
  const time = p.millis() * 0.003;
  
  for (let i = 0; i < arrowCount; i++) {
    const x = centerX - blockWidth/2 + 20 + i * 30;
    const offset = 10 * Math.sin(time + i * 0.5);
    
    p.stroke(255, 165, 0);
    p.strokeWeight(2);
    p.line(x + offset, centerY - 10, x + offset + 15, centerY - 10);
    
    // Arrow head
    p.line(x + offset + 15, centerY - 10, x + offset + 10, centerY - 15);
    p.line(x + offset + 15, centerY - 10, x + offset + 10, centerY - 5);
  }
}

function updateHeatTransfer() {
  heatState.time += 0.1;
  
  // Simple time-dependent temperature profile
  if (heatState.time < heatState.timeConstant * 3) {
    heatState.isSteadyState = false;
  } else {
    heatState.isSteadyState = true;
  }
  
  updateHeatTransferDashboard();
}

function calculateHeatTransfer() {
  const deltaT = htParams.tempHot - htParams.tempCold;
  const area = htParams.area / 10000; // Convert cm² to m²
  const thickness = htParams.thickness / 100; // Convert cm to m
  
  if (htParams.mode === "conduction") {
    const k = materials[htParams.material].k;
    heatState.heatRate = k * area * deltaT / thickness;
    heatState.thermalResistance = thickness / (k * area);
    heatState.timeConstant = 10; // Simplified
  } else if (htParams.mode === "convection") {
    // Simplified convection calculation
    const h = 10 + htParams.fluidVelocity * 20; // Heat transfer coefficient
    heatState.heatRate = h * area * deltaT;
    heatState.thermalResistance = 1 / (h * area);
    heatState.timeConstant = 5;
  } else if (htParams.mode === "radiation") {
    // Stefan-Boltzmann law (simplified)
    const sigma = 5.67e-8; // Stefan-Boltzmann constant
    const T1 = htParams.tempHot + 273.15; // Convert to Kelvin
    const T2 = htParams.tempCold + 273.15;
    heatState.heatRate = htParams.emissivity * sigma * area * (Math.pow(T1, 4) - Math.pow(T2, 4));
    heatState.thermalResistance = 1 / (4 * htParams.emissivity * sigma * area * Math.pow((T1 + T2)/2, 3));
    heatState.timeConstant = 2;
  }
  
  updateHeatTransferDashboard();
  updateTemperatureChart();
}

// -------------------- Controls --------------------
function bindHeatTransferControls() {
  // Mode buttons
  document.querySelectorAll("[data-mode]").forEach(btn => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      document.querySelectorAll("[data-mode]").forEach(b => b.classList.remove("active"));
      // Add active class to clicked button
      btn.classList.add("active");
      
      htParams.mode = btn.getAttribute("data-mode");
      
      // Show/hide mode-specific controls
      document.querySelectorAll(".convection-only").forEach(el => {
        el.style.display = htParams.mode === "convection" ? "block" : "none";
      });
      document.querySelectorAll(".radiation-only").forEach(el => {
        el.style.display = htParams.mode === "radiation" ? "block" : "none";
      });
      
      calculateHeatTransfer();
    });
  });
  
  // Sliders
  const sliders = [
    { id: "tempHot", param: "tempHot", output: "tempHotOut", decimals: 0 },
    { id: "tempCold", param: "tempCold", output: "tempColdOut", decimals: 0 },
    { id: "thickness", param: "thickness", output: "thicknessOut", decimals: 0 },
    { id: "area", param: "area", output: "areaOut", decimals: 0 },
    { id: "fluidVelocity", param: "fluidVelocity", output: "fluidVelocityOut", decimals: 1 },
    { id: "emissivity", param: "emissivity", output: "emissivityOut", decimals: 2 }
  ];
  
  sliders.forEach(({ id, param, output, decimals }) => {
    const slider = document.getElementById(id);
    const outputEl = document.getElementById(output);
    
    if (slider && outputEl) {
      slider.addEventListener("input", () => {
        htParams[param] = parseFloat(slider.value);
        outputEl.textContent = htParams[param].toFixed(decimals);
        
        // Ensure T_hot > T_cold
        if (param === "tempHot" && htParams.tempHot <= htParams.tempCold) {
          htParams.tempHot = htParams.tempCold + 10;
          slider.value = htParams.tempHot;
          outputEl.textContent = htParams.tempHot.toFixed(decimals);
        }
        if (param === "tempCold" && htParams.tempCold >= htParams.tempHot) {
          htParams.tempCold = htParams.tempHot - 10;
          slider.value = htParams.tempCold;
          outputEl.textContent = htParams.tempCold.toFixed(decimals);
        }
        
        calculateHeatTransfer();
      });
    }
  });
  
  // Material selector
  const materialSelect = document.getElementById("material");
  if (materialSelect) {
    materialSelect.addEventListener("change", () => {
      htParams.material = materialSelect.value;
      calculateHeatTransfer();
    });
  }
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", () => {
      htParams = {
        mode: "conduction",
        tempHot: 100,
        tempCold: 20,
        material: "copper",
        thickness: 5,
        area: 50,
        fluidVelocity: 1.0,
        emissivity: 0.8
      };
      
      // Update UI
      document.getElementById("tempHot").value = 100;
      document.getElementById("tempCold").value = 20;
      document.getElementById("material").value = "copper";
      document.getElementById("thickness").value = 5;
      document.getElementById("area").value = 50;
      document.getElementById("fluidVelocity").value = 1.0;
      document.getElementById("emissivity").value = 0.8;
      
      // Update outputs
      document.getElementById("tempHotOut").textContent = "100";
      document.getElementById("tempColdOut").textContent = "20";
      document.getElementById("thicknessOut").textContent = "5";
      document.getElementById("areaOut").textContent = "50";
      document.getElementById("fluidVelocityOut").textContent = "1.0";
      document.getElementById("emissivityOut").textContent = "0.80";
      
      // Reset mode
      document.querySelectorAll("[data-mode]").forEach(b => b.classList.remove("active"));
      document.querySelector("[data-mode='conduction']").classList.add("active");
      
      heatState.isRunning = false;
      heatState.time = 0;
      calculateHeatTransfer();
    });
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      heatState.isRunning = !heatState.isRunning;
      heatState.time = 0;
      startBtn.textContent = heatState.isRunning ? 
        (htLang === "ar" ? "إيقاف المحاكاة" : "Stop Simulation") : 
        (htLang === "ar" ? "بدء المحاكاة" : "Start Simulation");
    });
  }
}

function updateMaterialOptions() {
  const select = document.getElementById("material");
  if (!select) return;
  
  const options = select.querySelectorAll("option");
  options.forEach(option => {
    const material = option.value;
    if (materials[material]) {
      const name = htLang === "ar" ? materials[material].name_ar : materials[material].name_en;
      option.textContent = `${name} (k=${materials[material].k})`;
    }
  });
}

function updateSteadyStateText() {
  const el = document.getElementById("dashSteadyState");
  if (el) {
    const t = HTDICT[htLang];
    el.textContent = heatState.isSteadyState ? t.steady_yes : t.steady_no;
  }
}

function updateHeatTransferDashboard() {
  // Heat flow values
  const heatRateEl = document.getElementById("dashHeatRate");
  const tempDiffEl = document.getElementById("dashTempDiff");
  
  if (heatRateEl) heatRateEl.textContent = heatState.heatRate.toFixed(1);
  if (tempDiffEl) tempDiffEl.textContent = (htParams.tempHot - htParams.tempCold).toFixed(0);
  
  // Material properties
  const conductivityEl = document.getElementById("dashConductivity");
  const resistanceEl = document.getElementById("dashResistance");
  
  if (conductivityEl) {
    if (htParams.mode === "conduction") {
      conductivityEl.textContent = materials[htParams.material].k.toFixed(1);
    } else {
      conductivityEl.textContent = "N/A";
    }
  }
  if (resistanceEl) resistanceEl.textContent = heatState.thermalResistance.toFixed(4);
  
  // Efficiency values
  const timeConstantEl = document.getElementById("dashTimeConstant");
  
  if (timeConstantEl) timeConstantEl.textContent = heatState.timeConstant.toFixed(0);
  
  updateSteadyStateText();
}

// -------------------- Chart --------------------
let temperatureChart;

function initTemperatureChart() {
  const ctx = document.getElementById("temperatureChart");
  if (!ctx) return;
  
  temperatureChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Hot Side',
          data: [],
          borderColor: '#dc2626',
          backgroundColor: 'rgba(220, 38, 38, 0.1)',
          tension: 0.4
        },
        {
          label: 'Cold Side',
          data: [],
          borderColor: '#2563eb',
          backgroundColor: 'rgba(37, 99, 235, 0.1)',
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          title: { display: true, text: 'Time (s)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Temperature (°C)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          labels: { color: '#a9b4d0' }
        }
      }
    }
  });
}

function updateTemperatureChart() {
  if (!temperatureChart) return;
  
  // Generate temperature profile over time
  const data = [];
  const labels = [];
  
  for (let t = 0; t <= 20; t += 1) {
    labels.push(t.toString());
    
    // Simplified temperature evolution
    const factor = 1 - Math.exp(-t / heatState.timeConstant);
    const hotTemp = htParams.tempHot - (htParams.tempHot - htParams.tempCold) * 0.1 * factor;
    const coldTemp = htParams.tempCold + (htParams.tempHot - htParams.tempCold) * 0.1 * factor;
    
    data.push({ hot: hotTemp, cold: coldTemp });
  }
  
  temperatureChart.data.labels = labels;
  temperatureChart.data.datasets[0].data = data.map(d => d.hot);
  temperatureChart.data.datasets[1].data = data.map(d => d.cold);
  
  temperatureChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleHeatTransfer();
  applyHeatTransferI18n();
  bindHeatTransferControls();
  initP5HeatTransfer();
  initTemperatureChart();
  calculateHeatTransfer();
});
