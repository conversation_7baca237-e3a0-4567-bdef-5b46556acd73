<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | القوى والحركة الدائرية المنتظمة</title>
  <link rel="stylesheet" href="styles/circular_motion.css"/>
  <!-- p5.js for simulation -->
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <!-- Chart.js for live chart -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">Interactive Physics Platform</small>
      </div>
      <ul class="nav-links">
        <li><a href="#mechanics" data-i18n="nav_mechanics">الميكانيكا الكلاسيكية</a></li>
        <li><a href="#thermo" data-i18n="nav_thermo">الديناميكا الحرارية</a></li>
        <li><a href="#em" data-i18n="nav_em">الكهرومغناطيسية</a></li>
        <li><a href="#modern" data-i18n="nav_modern">الفيزياء الحديثة</a></li>
        <li><a href="#labs" data-i18n="nav_labs">مركز المختبرات</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="#" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">القوى والحركة الدائرية المنتظمة</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">القوى والحركة الدائرية: فهم الجاذبية المدارية</h1>
      <p class="lead" data-i18n="lesson_lead">
        في الحركة الدائرية المنتظمة، يتحرك الجسم بسرعة ثابتة المقدار على مسار دائري. على الرغم من ثبات السرعة، فإن الاتجاه يتغير باستمرار، مما يعني وجود تسارع مركزي نحو مركز الدائرة. القوة المسؤولة عن هذا التسارع تسمى القوة المركزية.
      </p>
    </section>

    <section class="theory card" id="theory">
      <h2 data-i18n="theory_title">الشرح النظري</h2>
      <div class="theory-grid">
        <div>
          <p data-i18n="theory_text_1">
            العلاقة الأساسية للحركة الدائرية المنتظمة هي:
          </p>
          <div class="formula" aria-label="Fc = m v^2 / r">
            F<span class="sub">c</span> = (m v<sup>2</sup>) / r
          </div>
          <ul class="bullets">
            <li data-i18n="theory_item_m">m: الكتلة (kg)</li>
            <li data-i18n="theory_item_v">v: السرعة (m/s)</li>
            <li data-i18n="theory_item_r">r: نصف القطر (m)</li>
            <li data-i18n="theory_item_fc">Fc: القوة المركزية (N)</li>
          </ul>
          <p data-i18n="theory_text_2">
            تظهر القوة المركزية في حالات عديدة مثل دوران الأقمار الصناعية، دوران السيارات في المنحنيات، وحركة الإلكترونات حول النواة في نماذج مبسطة.
          </p>
        </div>
        <div class="media">
          <figure class="gif-box" aria-label="centripetal direction">
            <div class="circle-scene">
              <div class="orbit"></div>
              <div class="object"></div>
              <div class="arrow" title="Fc"></div>
            </div>
            <figcaption data-i18n="gif_caption">اتجاه القوة المركزية نحو المركز</figcaption>
          </figure>
          <div class="video-box">
            <video controls preload="metadata">
              <source src="" type="video/mp4"/>
              <!-- Placeholder – the user can replace with a short 3-min video -->
            </video>
            <small data-i18n="video_note">ملاحظة: أضف فيديو قصير (≤ 3 دقائق) يشرح أمثلة عملية.</small>
          </div>
        </div>
      </div>
    </section>

    <section class="calculator card" id="calculator">
      <h2 data-i18n="calc_title">حاسبة القوة المركزية</h2>
      <div class="calc-grid">
        <div class="inputs">
          <div class="field">
            <label for="mass" data-i18n="input_m">الكتلة m (kg)</label>
            <div class="control">
              <input type="number" id="mass" step="0.1" min="0" value="10"/>
              <input type="range" id="massRange" min="0" max="200" step="0.1" value="10"/>
            </div>
          </div>
          <div class="field">
            <label for="velocity" data-i18n="input_v">السرعة v (m/s)</label>
            <div class="control">
              <input type="number" id="velocity" step="0.1" min="0" value="5"/>
              <input type="range" id="velocityRange" min="0" max="100" step="0.1" value="5"/>
            </div>
          </div>
          <div class="field">
            <label for="radius" data-i18n="input_r">نصف القطر r (m)</label>
            <div class="control">
              <input type="number" id="radius" step="0.1" min="0.1" value="3"/>
              <input type="range" id="radiusRange" min="0.1" max="100" step="0.1" value="3"/>
            </div>
          </div>
          <div class="presets">
            <span class="preset-label" data-i18n="presets_label">أمثلة سريعة:</span>
            <button class="btn preset" data-preset="satellite" data-i18n="preset_satellite">قمر صناعي</button>
            <button class="btn preset" data-preset="car" data-i18n="preset_car">سيارة</button>
            <button class="btn preset" data-preset="ball" data-i18n="preset_ball">كرة مربوطة</button>
          </div>
          <button class="btn outline" id="showSteps" data-i18n="show_steps">إظهار خطوات الحل</button>
        </div>
        <div class="outputs">
          <div class="result-box">
            <div class="result-title" data-i18n="result_fc">القوة المركزية Fc</div>
            <div class="result-value"><span id="fcValue">0.0</span> <span> N</span></div>
          </div>
          <div class="steps-box" id="stepsBox" hidden>
            <h3 data-i18n="steps_title">الخطوات</h3>
            <ol id="stepsList" class="steps-list"></ol>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card" id="lab">
      <h2 data-i18n="lab_title">محاكي المدارات الدائرية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="field">
            <label for="simMass" data-i18n="sim_m">الكتلة m (kg)</label>
            <input type="range" id="simMass" min="1" max="100" step="1" value="10"/>
            <output id="simMassOut">10</output>
          </div>
          <div class="field">
            <label for="simVel" data-i18n="sim_v">السرعة v (m/s)</label>
            <input type="range" id="simVel" min="0" max="50" step="0.1" value="10"/>
            <output id="simVelOut">10.0</output>
          </div>
          <div class="field">
            <label for="simRadius" data-i18n="sim_r">نصف القطر r (m)</label>
            <input type="range" id="simRadius" min="50" max="250" step="1" value="120"/>
            <output id="simRadiusOut">120</output>
          </div>
          <div class="field">
            <label for="simSpeed" data-i18n="sim_speed">سرعة المحاكاة</label>
            <input type="range" id="simSpeed" min="0.25" max="2" step="0.25" value="1"/>
            <output id="simSpeedOut">1.00x</output>
          </div>
          <div class="toggles">
            <label class="toggle">
              <input type="checkbox" id="showTrace" checked/>
              <span data-i18n="toggle_trace">عرض المسار</span>
            </label>
            <label class="toggle">
              <input type="checkbox" id="showFcVector" checked/>
              <span data-i18n="toggle_fc">عرض متجه Fc</span>
            </label>
          </div>
        </div>
        <div class="lab-visuals">
          <div class="canvas-wrap">
            <div id="p5-container" class="p5-container" aria-label="Orbit simulation canvas"></div>
          </div>
          <div class="dash">
            <div class="dash-row">
              <div class="dash-item"><span data-i18n="dash_m">m</span> = <span id="dashM">10</span> kg</div>
              <div class="dash-item"><span data-i18n="dash_v">v</span> = <span id="dashV">10.0</span> m/s</div>
              <div class="dash-item"><span data-i18n="dash_r">r</span> = <span id="dashR">120</span> m</div>
              <div class="dash-item"><span data-i18n="dash_fc">Fc</span> = <span id="dashFc">0.83</span> N</div>
              <div class="dash-item"><span data-i18n="dash_f">f</span> = <span id="dashF">0.013</span> Hz</div>
              <div class="dash-item"><span data-i18n="dash_T">T</span> = <span id="dashT">78.54</span> s</div>
            </div>
            <canvas id="fcChart" height="140"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card" id="cta">
      <h2 data-i18n="cta_title">جرّب بنفسك!</h2>
      <p data-i18n="cta_text">
        استخدم الضوابط لتغيير المعلمات ولاحظ كيف تتغير القوة المركزية والزمن الدوري. ما الذي يحدث إذا ضاعفت السرعة؟
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a>
          |
          <span data-i18n="footer_phone">Phone</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/circular_motion.js"></script>
</body>
</html>
