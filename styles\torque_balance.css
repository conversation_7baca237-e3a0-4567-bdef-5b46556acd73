:root {
  --bg: #0b1020;
  --card: #121933;
  --accent: #f59e0b;
  --accent-2: #fbbf24;
  --text: #e9eefb;
  --muted: #a9b4d0;
  --danger: #ff5c5c;
  --success: #4ade80;
  --beam-color: #8b5cf6;
  --fulcrum-color: #6b7280;
  --weight-color: #ef4444;
  --torque-color: #10b981;
  --shadow: 0 10px 30px rgba(0,0,0,0.35);
}

* { box-sizing: border-box; }
html, body {
  margin: 0;
  padding: 0;
  background: linear-gradient(180deg, #0b1020, #0b1329 40%, #0b1020);
  color: var(--text);
  font-family: "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, "Noto <PERSON> Arabic", "Cairo", sans-serif;
}

a { color: var(--accent); text-decoration: none; }
a:hover { text-decoration: underline; }

.site-header {
  position: sticky; top: 0; z-index: 1000;
  background: rgba(11,16,32,0.7);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255,255,255,0.06);
}

.navbar {
  display: flex; justify-content: space-between; align-items: center;
  padding: 1rem 2rem; max-width: 1200px; margin: 0 auto;
}

.brand { display: flex; flex-direction: column; }
.brand-title { font-size: 1.5rem; font-weight: 700; color: var(--accent); }
.brand-sub { font-size: 0.75rem; color: var(--muted); margin-top: -2px; }

.nav-links {
  display: flex; list-style: none; gap: 1.5rem; margin: 0; padding: 0;
}
.nav-links a { color: var(--text); font-weight: 500; }
.nav-links a:hover { color: var(--accent); }

.lang-toggle {
  background: var(--accent); color: white; border: none;
  padding: 0.5rem 1rem; border-radius: 6px; font-weight: 500;
  cursor: pointer; transition: all 0.2s;
}
.lang-toggle:hover { background: var(--accent-2); }

.container { max-width: 1200px; margin: 0 auto; padding: 2rem; }

.breadcrumbs {
  display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;
  font-size: 0.875rem; color: var(--muted);
}
.breadcrumbs a { color: var(--accent); }
.sep { color: var(--muted); }

.lesson-hero {
  text-align: center; margin-bottom: 3rem;
}
.lesson-hero h1 {
  font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--accent), var(--accent-2));
  -webkit-background-clip: text; -webkit-text-fill-color: transparent;
  background-clip: text;
}
.lead {
  font-size: 1.125rem; line-height: 1.6; color: var(--muted);
  max-width: 800px; margin: 0 auto;
}

.card {
  background: var(--card); border-radius: 12px; padding: 2rem;
  margin-bottom: 2rem; box-shadow: var(--shadow);
  border: 1px solid rgba(255,255,255,0.05);
}

.card h2 {
  font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem;
  color: var(--accent);
}

.theory-grid {
  display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;
  align-items: start;
}

.bullets {
  list-style: none; padding: 0; margin: 0;
}
.bullets li {
  padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.05);
  font-size: 0.95rem; line-height: 1.4;
}
.bullets li:last-child { border-bottom: none; }

.formula {
  background: rgba(245,158,11,0.1); border: 1px solid var(--accent);
  padding: 1rem; border-radius: 8px; margin-top: 1rem;
  font-family: 'Courier New', monospace; font-size: 1.1rem;
  text-align: center; color: var(--accent);
}

.diagram-box {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.legend {
  display: flex; gap: 1rem; margin-bottom: 1rem; flex-wrap: wrap;
}
.legend-item {
  display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem;
}
.dot {
  width: 12px; height: 12px; border-radius: 50%; display: inline-block;
}
.dot.beam { background: var(--beam-color); }
.dot.fulcrum { background: var(--fulcrum-color); }
.dot.weight { background: var(--weight-color); }
.dot.torque { background: var(--torque-color); }

.p5-container {
  width: 100%; height: 300px; border-radius: 6px;
  background: rgba(0,0,0,0.2); position: relative;
}

.lab-grid {
  display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;
}

.lab-controls {
  display: flex; flex-direction: column; gap: 1.5rem;
}

.weights-section {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.weights-section h3 {
  margin: 0 0 1rem 0; font-size: 1.1rem; color: var(--accent);
}

.weight-control {
  background: rgba(255,255,255,0.02); border-radius: 6px;
  padding: 1rem; margin-bottom: 1rem; border: 1px solid rgba(255,255,255,0.05);
}
.weight-control:last-child { margin-bottom: 0; }

.weight-control h4 {
  margin: 0 0 0.75rem 0; font-size: 0.95rem; color: var(--accent-2);
}

.field {
  display: flex; flex-direction: column; gap: 0.5rem; margin-bottom: 1rem;
}
.field:last-child { margin-bottom: 0; }

.field label {
  font-weight: 500; color: var(--text); font-size: 0.875rem;
}

input[type="range"] {
  width: 100%; height: 6px; border-radius: 3px;
  background: rgba(255,255,255,0.1); outline: none;
  -webkit-appearance: none; appearance: none;
}
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none; appearance: none;
  width: 18px; height: 18px; border-radius: 50%;
  background: var(--accent); cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
input[type="range"]::-moz-range-thumb {
  width: 18px; height: 18px; border-radius: 50%;
  background: var(--accent); cursor: pointer; border: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

output {
  font-weight: 600; color: var(--accent); font-size: 0.875rem;
}

.btn {
  padding: 0.75rem 1.5rem; border-radius: 6px; border: none;
  font-weight: 500; cursor: pointer; transition: all 0.2s;
  background: var(--accent); color: white;
}
.btn:hover { background: var(--accent-2); transform: translateY(-1px); }
.btn.outline {
  background: transparent; border: 1px solid var(--accent); color: var(--accent);
}
.btn.outline:hover { background: var(--accent); color: white; }

.buttons {
  display: flex; gap: 1rem;
}

.lab-visuals {
  display: flex; flex-direction: column;
}

.dash {
  background: rgba(255,255,255,0.02); border-radius: 8px;
  padding: 1rem; border: 1px solid rgba(255,255,255,0.05);
}

.dash-section {
  margin-bottom: 1.5rem;
}
.dash-section:last-of-type {
  margin-bottom: 1rem;
}

.dash-section h4 {
  margin: 0 0 0.5rem 0; font-size: 1rem; color: var(--accent);
}

.dash-row {
  display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem; margin-bottom: 0.5rem;
}
.dash-row:last-child { margin-bottom: 0; }

.dash-item {
  text-align: center; font-size: 0.875rem;
}
.dash-item span:first-child {
  font-weight: 600; color: var(--muted);
}
.dash-item span:last-child {
  font-weight: 700; color: var(--accent); font-size: 1rem;
}

.site-footer {
  background: var(--card); border-top: 1px solid rgba(255,255,255,0.05);
  padding: 2rem; margin-top: 3rem;
}

.footer-content {
  max-width: 1200px; margin: 0 auto;
  display: flex; justify-content: space-between; align-items: center;
  flex-wrap: wrap; gap: 1rem; font-size: 0.875rem;
}

.footer-content strong { color: var(--accent); }
.footer-content a { color: var(--accent); }

/* Balance indicator styles */
.balanced {
  color: var(--success) !important;
}

.unbalanced {
  color: var(--danger) !important;
}

@media (max-width: 768px) {
  .theory-grid, .lab-grid {
    grid-template-columns: 1fr;
  }
  .navbar {
    flex-direction: column; gap: 1rem; padding: 1rem;
  }
  .nav-links {
    gap: 1rem;
  }
  .lesson-hero h1 {
    font-size: 2rem;
  }
  .container {
    padding: 1rem;
  }
  .footer-content {
    flex-direction: column; text-align: center;
  }
  .dash-row {
    grid-template-columns: 1fr;
  }
}
