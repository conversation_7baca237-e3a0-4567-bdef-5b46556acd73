/**
 * PhysicsHub - Projectile Motion Lab
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: v0, angle, mass, gravity presets, air resistance (Cd, A, rho)
 * - p5.js visualization: trajectory with/without drag, velocity components vectors (vx, vy), drag vector
 * - Live dashboard: vx, vy, Hmax, Range, Time of flight
 * - Chart.js: live range vs time trace
 * - Reset/Restart controls
 */

// -------------------- i18n (AR/EN) --------------------
const PDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    lesson_breadcrumb: "محاكي المقذوفات",
    lesson_title: "محاكي المقذوفات: علم الحركة في بعدين",
    lesson_lead:
      "استكشف حركة المقذوف بزاوية ابتدائية وسرعة ابتدائية، وشاهد استقلال المركبتين الأفقية والرأسية. جرّب تغيير الجاذبية لمحاكاة كواكب مختلفة، وفعّل مقاومة الهواء لملاحظة تأثيرها على المدى والارتفاع.",
    theory_title: "المفهوم الأساسي",
    v0_label: "السرعة الابتدائية v0 (m/s)",
    angle_label: "زاوية القذف θ (°)",
    mass_label: "الكتلة m (kg)",
    g_label: "الجاذبية g (m/s²)",
    air_label: "مقاومة الهواء",
    cd_label: "معامل السحب Cd",
    A_label: "المساحة A (m²)",
    rho_label: "كثافة الهواء ρ (kg/m³)",
    reset: "إعادة ضبط",
    restart: "بدء/إعادة تشغيل",
    lab_title: "المعلمات التفاعلية",
    dash_H: "Hmax",
    dash_R: "R",
    dash_T: "T",
    cta_title: "الهدف التعليمي",
    cta_text:
      "فهم استقلالية الحركة الأفقية والرأسية، تحديد الزاوية المثلى لتحقيق أقصى مدى، وملاحظة تأثير مقاومة الهواء والجاذبية على حركة المقذوف.",
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    lesson_breadcrumb: "Projectile Simulator",
    lesson_title: "Projectile Simulator: 2D Kinematics",
    lesson_lead:
      "Explore projectile motion with initial speed and launch angle, and see independence of horizontal and vertical motion. Try different gravities for other planets and enable air resistance to observe its effect on range and height.",
    theory_title: "Core Concept",
    v0_label: "Initial speed v0 (m/s)",
    angle_label: "Launch angle θ (°)",
    mass_label: "Mass m (kg)",
    g_label: "Gravity g (m/s²)",
    air_label: "Air resistance",
    cd_label: "Drag coefficient Cd",
    A_label: "Cross-section A (m²)",
    rho_label: "Air density ρ (kg/m³)",
    reset: "Reset",
    restart: "Start/Restart",
    lab_title: "Interactive parameters",
    dash_H: "Hmax",
    dash_R: "R",
    dash_T: "T",
    cta_title: "Learning objective",
    cta_text:
      "Understand independence of horizontal and vertical motion, find the optimal angle for maximum range, and observe effects of air resistance and gravity.",
  },
};

let pjLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyProjectileI18n() {
  const t = PDICT[pjLang];
  document.documentElement.lang = pjLang === "ar" ? "ar" : "en";
  document.documentElement.dir = pjLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
  // Update titles for sliders
  const ids = [
    ["v0", "v0_label"],
    ["angle", "angle_label"],
    ["mass", "mass_label"],
    ["gravity", "g_label"],
    ["cd", "cd_label"],
    ["area", "A_label"],
    ["rho", "rho_label"],
  ];
  ids.forEach(([id, key]) => {
    const el = document.getElementById(id);
    if (el && t[key]) el.setAttribute("title", t[key]);
  });
}

function setupLangToggleProjectile() {
  const btn = document.getElementById("langToggle");
  if (!btn) return;
  btn.addEventListener("click", () => {
    pjLang = pjLang === "ar" ? "en" : "ar";
    applyProjectileI18n();
    // chart axis
    if (rangeChart) {
      if (pjLang === "ar") {
        rangeChart.options.scales.x.title.text = "الزمن (ث)";
        rangeChart.options.scales.y.title.text = "المسافة الأفقية x (م)";
        rangeChart.data.datasets[0].label = "x(t)";
      } else {
        rangeChart.options.scales.x.title.text = "t (s)";
        rangeChart.options.scales.y.title.text = "Horizontal distance x (m)";
        rangeChart.data.datasets[0].label = "x(t)";
      }
      rangeChart.update();
    }
  });
}

// -------------------- State & DOM --------------------
const $ = (id) => document.getElementById(id);

const params = {
  v0: 30,
  angleDeg: 45,
  m: 1.0,
  g: 9.8,
  air: false,
  Cd: 0.47,
  A: 0.01,
  rho: 1.225,
};

let sim = {
  running: false,
  t: 0,
  pos: { x: 0, y: 0 },
  vel: { x: 0, y: 0 },
  path: [],
};

function resetSimulation() {
  sim.t = 0;
  const angle = (params.angleDeg * Math.PI) / 180;
  sim.pos = { x: 0, y: 0 };
  sim.vel = {
    x: params.v0 * Math.cos(angle),
    y: params.v0 * Math.sin(angle),
  };
  sim.path = [{ x: 0, y: 0 }];
  // dashboard precompute without drag
  const T = (2 * params.v0 * Math.sin(angle)) / params.g;
  const H = (params.v0 * params.v0 * Math.sin(angle) * Math.sin(angle)) / (2 * params.g);
  const R = (params.v0 * params.v0 * Math.sin(2 * angle)) / params.g;
  updateDashboard(sim.vel.x, sim.vel.y, H, R, T);
  rangeChartReset();
}

function restartSimulation() {
  resetSimulation();
  sim.running = true;
}

function bindControls() {
  const v0 = $("v0");
  const angle = $("angle");
  const mass = $("mass");
  const gravity = $("gravity");
  const air = $("air");
  const cd = $("cd");
  const area = $("area");
  const rho = $("rho");

  const v0Out = $("v0Out");
  const angleOut = $("angleOut");
  const massOut = $("massOut");
  const gravityOut = $("gravityOut");
  const cdOut = $("cdOut");
  const areaOut = $("areaOut");
  const rhoOut = $("rhoOut");

  const gPresets = document.querySelectorAll(".preset[data-g]");

  v0.addEventListener("input", () => {
    params.v0 = parseFloat(v0.value);
    v0Out.textContent = params.v0.toFixed(0);
    resetSimulation();
  });
  angle.addEventListener("input", () => {
    params.angleDeg = parseFloat(angle.value);
    angleOut.textContent = `${params.angleDeg.toFixed(1)}°`;
    resetSimulation();
  });
  mass.addEventListener("input", () => {
    params.m = parseFloat(mass.value);
    massOut.textContent = params.m.toFixed(1);
    resetSimulation();
  });
  gravity.addEventListener("input", () => {
    params.g = parseFloat(gravity.value);
    gravityOut.textContent = params.g.toFixed(2);
    resetSimulation();
  });
  air.addEventListener("change", () => {
    params.air = air.checked;
    resetSimulation();
  });
  cd.addEventListener("input", () => {
    params.Cd = parseFloat(cd.value);
    cdOut.textContent = params.Cd.toFixed(2);
    resetSimulation();
  });
  area.addEventListener("input", () => {
    params.A = parseFloat(area.value);
    areaOut.textContent = params.A.toFixed(3);
    resetSimulation();
  });
  rho.addEventListener("input", () => {
    params.rho = parseFloat(rho.value);
    rhoOut.textContent = params.rho.toFixed(3);
    resetSimulation();
  });

  gPresets.forEach((btn) => {
    btn.addEventListener("click", () => {
      const g = parseFloat(btn.getAttribute("data-g"));
      params.g = g;
      gravity.value = g;
      gravity.dispatchEvent(new Event("input"));
    });
  });

  $("reset").addEventListener("click", () => {
    // defaults
    params.v0 = 30; v0.value = 30; v0.dispatchEvent(new Event("input"));
    params.angleDeg = 45; angle.value = 45; angle.dispatchEvent(new Event("input"));
    params.m = 1.0; mass.value = 1.0; mass.dispatchEvent(new Event("input"));
    params.g = 9.8; gravity.value = 9.8; gravity.dispatchEvent(new Event("input"));
    params.air = false; $("air").checked = false;
    params.Cd = 0.47; $("cd").value = 0.47; $("cd").dispatchEvent(new Event("input"));
    params.A = 0.01; $("area").value = 0.01; $("area").dispatchEvent(new Event("input"));
    params.rho = 1.225; $("rho").value = 1.225; $("rho").dispatchEvent(new Event("input"));
    sim.running = false;
    resetSimulation();
  });

  $("restart").addEventListener("click", () => {
    restartSimulation();
  });

  // initialize outputs
  v0.dispatchEvent(new Event("input"));
  angle.dispatchEvent(new Event("input"));
  mass.dispatchEvent(new Event("input"));
  gravity.dispatchEvent(new Event("input"));
  $("cd").dispatchEvent(new Event("input"));
  $("area").dispatchEvent(new Event("input"));
  $("rho").dispatchEvent(new Event("input"));
}

// -------------------- Physics update --------------------
function stepPhysics(dt) {
  // dt in seconds
  // Using ground y=0; positive y upwards. Stop when projectile hits ground (y < 0 after positive).
  // With drag: Fd = 0.5 * rho * Cd * A * v^2 directed opposite velocity.
  const g = params.g;
  let ax = 0;
  let ay = -g;

  if (params.air) {
    const vx = sim.vel.x;
    const vy = sim.vel.y;
    const vmag = Math.hypot(vx, vy);
    if (vmag > 1e-6) {
      const Fd = 0.5 * params.rho * params.Cd * params.A * vmag * vmag;
      // Drag acceleration a_d = -Fd/m * v_hat
      ax += (-Fd / params.m) * (vx / vmag);
      ay += (-Fd / params.m) * (vy / vmag);
    }
  }

  sim.vel.x += ax * dt;
  sim.vel.y += ay * dt;
  sim.pos.x += sim.vel.x * dt;
  sim.pos.y += sim.vel.y * dt;

  sim.t += dt;
  sim.path.push({ x: sim.pos.x, y: sim.pos.y });

  // Stop when hits ground (after launch)
  if (sim.pos.y < 0 && sim.t > 0.02) {
    sim.running = false;
  }

  // Update dashboard continuously (approx, Hmax and R computed analytically for no-drag case)
  const angle = (params.angleDeg * Math.PI) / 180;
  const T = (2 * params.v0 * Math.sin(angle)) / params.g;
  const H = (params.v0 * params.v0 * Math.sin(angle) * Math.sin(angle)) / (2 * params.g);
  const R = (params.v0 * params.v0 * Math.sin(2 * angle)) / params.g;
  updateDashboard(sim.vel.x, sim.vel.y, H, R, T);
  pushRangeChartPoint(sim.t, sim.pos.x);
}

// -------------------- p5.js rendering --------------------
let p5Instance;
let workspace;

function initP5Projectile() {
  const container = document.getElementById("p5-container");

  // Initialize workspace manager
  workspace = initializeWorkspace('projectiles');

  const s = (p) => {
    let dimensions = workspace.getCanvasDimensions();
    let w = dimensions.width;
    let h = dimensions.height;

    // world to screen mapping with workspace scaling
    function worldToScreen(wx, wy) {
      // Use workspace bounds for consistent scaling
      const bounds = workspace.getPhysicsBounds();
      const scaleX = bounds.width / 120; // fit 120m horizontally
      const scaleY = bounds.height / 60; // fit 60m vertically
      const scale = Math.min(scaleX, scaleY);
      const sx = bounds.left + wx * scale;
      // invert y: screen y increases downward
      const sy = bounds.bottom - wy * scale;
      return [sx, sy, scale];
    }

    p.setup = function () {
      const cnv = p.createCanvas(w, h);
      cnv.parent(container);
      p.frameRate(60);
    };

    p.windowResized = function () {
      dimensions = workspace.getCanvasDimensions();
      w = dimensions.width;
      h = dimensions.height;
      p.resizeCanvas(w, h);
    };

    p.draw = function () {
      p.background(8, 12, 30);

      // Draw workspace grid and rulers
      workspace.drawGrid(p, 0.05);
      workspace.drawRulers(p, 0.3);

      // ground line
      p.stroke(100, 140, 200, 120);
      p.strokeWeight(2);
      const [gx1, gy] = worldToScreen(0, 0);
      const [gx2] = worldToScreen(120, 0);
      p.line(gx1, gy, gx2, gy);

      // draw path
      p.noFill();
      p.stroke(80, 220, 180, 160);
      p.strokeWeight(2);
      p.beginShape();
      for (const pt of sim.path) {
        const [sx, sy] = worldToScreen(pt.x, pt.y);
        p.vertex(sx, sy);
      }
      p.endShape();

      // draw projectile
      const [sx, sy] = worldToScreen(sim.pos.x, sim.pos.y);
      p.noStroke();
      p.fill(58, 160, 255);
      p.circle(sx, sy, 10);

      // velocity vectors (vx, vy)
      p.strokeWeight(3);
      // vx in accent
      p.stroke(58, 160, 255);
      p.line(sx, sy, sx + sim.vel.x * 0.6, sy); // scaled for visibility
      // vy in accent-2
      p.stroke(123, 211, 137);
      p.line(sx, sy, sx, sy - sim.vel.y * 0.6);

      // drag vector if enabled
      if (params.air) {
        const vmag = Math.hypot(sim.vel.x, sim.vel.y);
        if (vmag > 1e-6) {
          const k = 0.4; // visual scale
          p.stroke(255, 92, 92);
          p.line(sx, sy, sx - k * sim.vel.x, sy + k * sim.vel.y);
        }
      }

      // step physics if running
      if (sim.running) {
        // fixed dt for stability
        let dt = 1 / 120;
        // multiple substeps to improve drag stability
        for (let i = 0; i < 2; i++) stepPhysics(dt / 2);
      }
    };
  };

  if (p5Instance) p5Instance.remove();
  p5Instance = new p5(s);
}

// -------------------- Dashboard --------------------
function updateDashboard(vx, vy, H, R, T) {
  $("dashVx").textContent = (vx ?? 0).toFixed(2);
  $("dashVy").textContent = (vy ?? 0).toFixed(2);
  $("dashH").textContent = isFinite(H) ? H.toFixed(2) : "∞";
  $("dashR").textContent = isFinite(R) ? R.toFixed(2) : "∞";
  $("dashT").textContent = isFinite(T) ? T.toFixed(2) : "∞";
}

// -------------------- Chart.js --------------------
let rangeChart;
function initRangeChart() {
  const ctx = document.getElementById("chartRange");
  rangeChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: [],
      datasets: [
        {
          label: "x(t)",
          data: [],
          borderColor: "rgba(58,160,255,1)",
          backgroundColor: "rgba(58,160,255,0.2)",
          tension: 0.25,
          pointRadius: 0,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          title: { display: true, text: pjLang === "ar" ? "الزمن (ث)" : "t (s)" },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          title: { display: true, text: pjLang === "ar" ? "المسافة الأفقية x (م)" : "Horizontal distance x (m)" },
          ticks: { color: "#a9b4d0" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
      plugins: {
        legend: { labels: { color: "#e9eefb" } },
      },
    },
  });
}
function rangeChartReset() {
  if (!rangeChart) return;
  rangeChart.data.labels = [];
  rangeChart.data.datasets[0].data = [];
  rangeChart.update("none");
}
function pushRangeChartPoint(t, x) {
  if (!rangeChart) return;
  rangeChart.data.labels.push(t.toFixed(2));
  rangeChart.data.datasets[0].data.push(x);
  if (rangeChart.data.labels.length > 300) {
    rangeChart.data.labels.shift();
    rangeChart.data.datasets[0].data.shift();
  }
  rangeChart.update("none");
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleProjectile();
  applyProjectileI18n();
  bindControls();
  initP5Projectile();
  initRangeChart();
  resetSimulation();
});
