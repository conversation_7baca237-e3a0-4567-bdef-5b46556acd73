/**
 * PhysicsHub - Collision Lab: Conservation of Momentum
 * Standalone interactive experiment with defined learning objective
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 *
 * Features:
 * - i18n AR/EN toggle with RTL/LTR dir
 * - Interactive parameters: masses, velocities, collision type
 * - p5.js visualization: 1D collision simulation with momentum vectors
 * - Live dashboard: momentum and kinetic energy before/after collision
 * - Chart.js: momentum conservation visualization
 * - Collision types: elastic, inelastic, perfectly inelastic
 */

// -------------------- i18n (AR/EN) --------------------
const CDICT = {
  ar: {
    brand: "PhysicsHub",
    brand_sub: "منصة فيزياء تفاعلية",
    home: "الصفحة الرئيسية",
    nav_home: "الصفحة الرئيسية",
    nav_prev: "المختبر السابق",
    lesson_breadcrumb: "مختبر التصادم وحفظ الزخم",
    lesson_title: "مختبر التصادم وحفظ الزخم: التصادمات المرنة وغير المرنة",
    lesson_lead: "استكشف مبدأ حفظ الزخم في التصادمات المختلفة. قارن بين التصادمات المرنة وغير المرنة وشاهد كيف يُحفظ الزخم دائماً بينما الطاقة الحركية قد تُحفظ أو تضيع حسب نوع التصادم.",
    theory_title: "المفهوم الأساسي",
    t1: "الزخم: p = mv (كتلة × سرعة)",
    t2: "حفظ الزخم: p_قبل = p_بعد (في نظام معزول)",
    t3: "التصادم المرن: تُحفظ الطاقة الحركية والزخم",
    t4: "التصادم غير المرن: يُحفظ الزخم فقط",
    t5: "التصادم غير المرن تماماً: الأجسام تلتصق معاً",
    lab_title: "المعلمات التفاعلية",
    object1_title: "الجسم الأول",
    object2_title: "الجسم الثاني",
    mass1_label: "الكتلة m₁ (kg)",
    mass2_label: "الكتلة m₂ (kg)",
    velocity1_label: "السرعة الابتدائية v₁ (m/s)",
    velocity2_label: "السرعة الابتدائية v₂ (m/s)",
    collision_type_label: "نوع التصادم",
    elastic: "مرن",
    inelastic: "غير مرن",
    perfectly_inelastic: "غير مرن تماماً",
    restitution_label: "معامل الاسترداد e",
    reset: "إعادة ضبط",
    start: "بدء التصادم",
    before_collision: "قبل التصادم",
    after_collision: "بعد التصادم",
    dash_p1: "p₁",
    dash_p2: "p₂",
    dash_ptotal: "p_total",
    dash_ke: "KE",
    cta_title: "الهدف التعليمي",
    cta_text: "التحقق من أن الزخم محفوظ دائماً في نظام معزول، بينما الطاقة الحركية تُحفظ فقط في التصادمات المرنة. في التصادمات غير المرنة، جزء من الطاقة الحركية يتحول إلى أشكال أخرى من الطاقة.",
    footer_author_label: "المؤلف وحقوق النشر:",
    footer_contact_label: "للتواصل:",
    footer_phone: "الهاتف"
  },
  en: {
    brand: "PhysicsHub",
    brand_sub: "Interactive Physics Platform",
    home: "Home",
    nav_home: "Home",
    nav_prev: "Previous Lab",
    lesson_breadcrumb: "Collision Lab and Momentum Conservation",
    lesson_title: "Collision Lab and Momentum Conservation: Elastic and Inelastic Collisions",
    lesson_lead: "Explore the principle of momentum conservation in different collisions. Compare elastic and inelastic collisions and see how momentum is always conserved while kinetic energy may be conserved or lost depending on collision type.",
    theory_title: "Core Concept",
    t1: "Momentum: p = mv (mass × velocity)",
    t2: "Conservation of momentum: p_before = p_after (in isolated system)",
    t3: "Elastic collision: Both kinetic energy and momentum are conserved",
    t4: "Inelastic collision: Only momentum is conserved",
    t5: "Perfectly inelastic collision: Objects stick together",
    lab_title: "Interactive Parameters",
    object1_title: "Object 1",
    object2_title: "Object 2",
    mass1_label: "Mass m₁ (kg)",
    mass2_label: "Mass m₂ (kg)",
    velocity1_label: "Initial velocity v₁ (m/s)",
    velocity2_label: "Initial velocity v₂ (m/s)",
    collision_type_label: "Collision Type",
    elastic: "Elastic",
    inelastic: "Inelastic",
    perfectly_inelastic: "Perfectly Inelastic",
    restitution_label: "Coefficient of restitution e",
    reset: "Reset",
    start: "Start Collision",
    before_collision: "Before Collision",
    after_collision: "After Collision",
    dash_p1: "p₁",
    dash_p2: "p₂",
    dash_ptotal: "p_total",
    dash_ke: "KE",
    cta_title: "Learning Objective",
    cta_text: "Verify that momentum is always conserved in an isolated system, while kinetic energy is conserved only in elastic collisions. In inelastic collisions, part of the kinetic energy is converted to other forms of energy.",
    footer_author_label: "Author and Copyright:",
    footer_contact_label: "Contact:",
    footer_phone: "Phone"
  }
};

let cLang = document.documentElement.lang === "ar" ? "ar" : "en";

function applyCollisionI18n() {
  const t = CDICT[cLang];
  document.documentElement.lang = cLang === "ar" ? "ar" : "en";
  document.documentElement.dir = cLang === "ar" ? "rtl" : "ltr";
  document.querySelectorAll("[data-i18n]").forEach((el) => {
    const key = el.getAttribute("data-i18n");
    if (t[key]) el.textContent = t[key];
  });
}

function setupLangToggleCollision() {
  const btn = document.getElementById("langToggle");
  if (btn) {
    btn.addEventListener("click", () => {
      cLang = cLang === "ar" ? "en" : "ar";
      applyCollisionI18n();
    });
  }
}

// -------------------- Simulation Parameters --------------------
let cParams = {
  mass1: 2.0,
  mass2: 1.0,
  velocity1: 5.0,
  velocity2: 0.0,
  collisionType: "elastic",
  restitution: 1.0
};

// -------------------- Simulation State --------------------
let objects = {
  obj1: { x: 100, y: 150, vx: 0, vy: 0, radius: 20, color: [59, 130, 246] },
  obj2: { x: 400, y: 150, vx: 0, vy: 0, radius: 15, color: [239, 68, 68] }
};

let collisionState = {
  hasCollided: false,
  isRunning: false,
  beforeCollision: { p1: 0, p2: 0, pTotal: 0, ke: 0 },
  afterCollision: { p1: 0, p2: 0, pTotal: 0, ke: 0 }
};

// -------------------- p5.js Sketch --------------------
let p5Instance;

function initP5Collision() {
  const container = document.getElementById("p5-container");
  if (!container) return;

  p5Instance = new p5((p) => {
    p.setup = () => {
      const canvas = p.createCanvas(container.offsetWidth, container.offsetHeight);
      canvas.parent(container);
      resetCollision();
    };

    p.windowResized = () => {
      p.resizeCanvas(container.offsetWidth, container.offsetHeight);
    };

    p.draw = () => {
      p.background(15, 20, 35);
      
      // Draw track
      drawTrack(p);
      
      // Draw objects
      drawObjects(p);
      
      // Draw momentum vectors
      drawMomentumVectors(p);
      
      // Update simulation
      if (collisionState.isRunning && !collisionState.hasCollided) {
        updateCollision();
      }
    };
  });
}

function drawTrack(p) {
  // Draw horizontal line
  p.stroke(255, 255, 255, 100);
  p.strokeWeight(2);
  p.line(50, 150, p.width - 50, 150);
  
  // Draw scale marks
  p.stroke(255, 255, 255, 50);
  p.strokeWeight(1);
  for (let x = 100; x < p.width - 50; x += 50) {
    p.line(x, 145, x, 155);
  }
}

function drawObjects(p) {
  // Object 1
  p.fill(objects.obj1.color[0], objects.obj1.color[1], objects.obj1.color[2]);
  p.noStroke();
  p.circle(objects.obj1.x, objects.obj1.y, objects.obj1.radius * 2);
  
  // Object 2
  p.fill(objects.obj2.color[0], objects.obj2.color[1], objects.obj2.color[2]);
  p.circle(objects.obj2.x, objects.obj2.y, objects.obj2.radius * 2);
  
  // Labels
  p.fill(255);
  p.textAlign(p.CENTER);
  p.textSize(12);
  p.text("1", objects.obj1.x, objects.obj1.y + 4);
  p.text("2", objects.obj2.x, objects.obj2.y + 4);
}

function drawMomentumVectors(p) {
  const scale = 10; // Scale factor for vector visualization
  
  // Object 1 momentum vector
  if (objects.obj1.vx !== 0) {
    p.stroke(16, 185, 129);
    p.strokeWeight(3);
    const endX = objects.obj1.x + objects.obj1.vx * scale;
    p.line(objects.obj1.x, objects.obj1.y - 30, endX, objects.obj1.y - 30);
    
    // Arrow head
    if (objects.obj1.vx > 0) {
      p.line(endX, objects.obj1.y - 30, endX - 5, objects.obj1.y - 35);
      p.line(endX, objects.obj1.y - 30, endX - 5, objects.obj1.y - 25);
    } else {
      p.line(endX, objects.obj1.y - 30, endX + 5, objects.obj1.y - 35);
      p.line(endX, objects.obj1.y - 30, endX + 5, objects.obj1.y - 25);
    }
  }
  
  // Object 2 momentum vector
  if (objects.obj2.vx !== 0) {
    p.stroke(16, 185, 129);
    p.strokeWeight(3);
    const endX = objects.obj2.x + objects.obj2.vx * scale;
    p.line(objects.obj2.x, objects.obj2.y - 30, endX, objects.obj2.y - 30);
    
    // Arrow head
    if (objects.obj2.vx > 0) {
      p.line(endX, objects.obj2.y - 30, endX - 5, objects.obj2.y - 35);
      p.line(endX, objects.obj2.y - 30, endX - 5, objects.obj2.y - 25);
    } else {
      p.line(endX, objects.obj2.y - 30, endX + 5, objects.obj2.y - 35);
      p.line(endX, objects.obj2.y - 30, endX + 5, objects.obj2.y - 25);
    }
  }
}

function resetCollision() {
  // Reset positions
  objects.obj1.x = 100;
  objects.obj1.y = 150;
  objects.obj2.x = 400;
  objects.obj2.y = 150;
  
  // Set initial velocities
  objects.obj1.vx = cParams.velocity1;
  objects.obj1.vy = 0;
  objects.obj2.vx = cParams.velocity2;
  objects.obj2.vy = 0;
  
  // Update object sizes based on mass
  objects.obj1.radius = Math.max(10, Math.min(30, cParams.mass1 * 8));
  objects.obj2.radius = Math.max(10, Math.min(30, cParams.mass2 * 8));
  
  // Reset collision state
  collisionState.hasCollided = false;
  collisionState.isRunning = false;
  
  // Calculate initial values
  calculateBeforeCollision();
  updateCollisionDashboard();
}

function calculateBeforeCollision() {
  collisionState.beforeCollision.p1 = cParams.mass1 * cParams.velocity1;
  collisionState.beforeCollision.p2 = cParams.mass2 * cParams.velocity2;
  collisionState.beforeCollision.pTotal = collisionState.beforeCollision.p1 + collisionState.beforeCollision.p2;
  collisionState.beforeCollision.ke = 0.5 * cParams.mass1 * cParams.velocity1 * cParams.velocity1 + 
                                     0.5 * cParams.mass2 * cParams.velocity2 * cParams.velocity2;
}

function updateCollision() {
  const dt = 0.02;
  
  // Update positions
  objects.obj1.x += objects.obj1.vx * dt * 50; // Scale for visualization
  objects.obj2.x += objects.obj2.vx * dt * 50;
  
  // Check for collision
  const distance = Math.abs(objects.obj1.x - objects.obj2.x);
  const minDistance = objects.obj1.radius + objects.obj2.radius;
  
  if (distance <= minDistance && !collisionState.hasCollided) {
    performCollision();
    collisionState.hasCollided = true;
    calculateAfterCollision();
    updateCollisionDashboard();
  }
  
  // Stop if objects move off screen
  if (objects.obj1.x > 600 || objects.obj2.x < 0 || objects.obj1.x < 0 || objects.obj2.x > 600) {
    collisionState.isRunning = false;
  }
}

function performCollision() {
  const m1 = cParams.mass1;
  const m2 = cParams.mass2;
  const u1 = objects.obj1.vx;
  const u2 = objects.obj2.vx;
  const e = cParams.restitution;
  
  let v1, v2;
  
  if (cParams.collisionType === "perfectly_inelastic") {
    // Perfectly inelastic: objects stick together
    v1 = v2 = (m1 * u1 + m2 * u2) / (m1 + m2);
  } else {
    // General collision formula using coefficient of restitution
    v1 = ((m1 - e * m2) * u1 + m2 * (1 + e) * u2) / (m1 + m2);
    v2 = ((m2 - e * m1) * u2 + m1 * (1 + e) * u1) / (m1 + m2);
  }
  
  objects.obj1.vx = v1;
  objects.obj2.vx = v2;
}

function calculateAfterCollision() {
  collisionState.afterCollision.p1 = cParams.mass1 * objects.obj1.vx;
  collisionState.afterCollision.p2 = cParams.mass2 * objects.obj2.vx;
  collisionState.afterCollision.pTotal = collisionState.afterCollision.p1 + collisionState.afterCollision.p2;
  collisionState.afterCollision.ke = 0.5 * cParams.mass1 * objects.obj1.vx * objects.obj1.vx + 
                                     0.5 * cParams.mass2 * objects.obj2.vx * objects.obj2.vx;
}

// -------------------- Controls --------------------
function bindCollisionControls() {
  // Mass and velocity sliders
  const sliders = [
    { id: "mass1", param: "mass1", output: "mass1Out", decimals: 1 },
    { id: "mass2", param: "mass2", output: "mass2Out", decimals: 1 },
    { id: "velocity1", param: "velocity1", output: "velocity1Out", decimals: 1 },
    { id: "velocity2", param: "velocity2", output: "velocity2Out", decimals: 1 },
    { id: "restitution", param: "restitution", output: "restitutionOut", decimals: 2 }
  ];
  
  sliders.forEach(({ id, param, output, decimals }) => {
    const slider = document.getElementById(id);
    const outputEl = document.getElementById(output);
    
    if (slider && outputEl) {
      slider.addEventListener("input", () => {
        cParams[param] = parseFloat(slider.value);
        outputEl.textContent = cParams[param].toFixed(decimals);
        resetCollision();
      });
    }
  });
  
  // Collision type buttons
  document.querySelectorAll("[data-type]").forEach(btn => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      document.querySelectorAll("[data-type]").forEach(b => b.classList.remove("active"));
      // Add active class to clicked button
      btn.classList.add("active");
      
      const type = btn.getAttribute("data-type");
      cParams.collisionType = type;
      
      // Update restitution coefficient
      if (type === "elastic") {
        cParams.restitution = 1.0;
      } else if (type === "perfectly_inelastic") {
        cParams.restitution = 0.0;
      } else {
        cParams.restitution = 0.5;
      }
      
      const restitutionSlider = document.getElementById("restitution");
      const restitutionOut = document.getElementById("restitutionOut");
      if (restitutionSlider && restitutionOut) {
        restitutionSlider.value = cParams.restitution;
        restitutionOut.textContent = cParams.restitution.toFixed(2);
      }
      
      resetCollision();
    });
  });
  
  // Control buttons
  const resetBtn = document.getElementById("reset");
  const startBtn = document.getElementById("start");
  
  if (resetBtn) {
    resetBtn.addEventListener("click", resetCollision);
  }
  
  if (startBtn) {
    startBtn.addEventListener("click", () => {
      if (!collisionState.isRunning) {
        collisionState.isRunning = true;
        startBtn.textContent = cLang === "ar" ? "جاري التصادم..." : "Collision in progress...";
      } else {
        resetCollision();
        startBtn.textContent = cLang === "ar" ? "بدء التصادم" : "Start Collision";
      }
    });
  }
}

function updateCollisionDashboard() {
  // Before collision
  const dashP1Before = document.getElementById("dashP1Before");
  const dashP2Before = document.getElementById("dashP2Before");
  const dashPTotalBefore = document.getElementById("dashPTotalBefore");
  const dashKEBefore = document.getElementById("dashKEBefore");
  
  if (dashP1Before) dashP1Before.textContent = collisionState.beforeCollision.p1.toFixed(1);
  if (dashP2Before) dashP2Before.textContent = collisionState.beforeCollision.p2.toFixed(1);
  if (dashPTotalBefore) dashPTotalBefore.textContent = collisionState.beforeCollision.pTotal.toFixed(1);
  if (dashKEBefore) dashKEBefore.textContent = collisionState.beforeCollision.ke.toFixed(1);
  
  // After collision
  const dashP1After = document.getElementById("dashP1After");
  const dashP2After = document.getElementById("dashP2After");
  const dashPTotalAfter = document.getElementById("dashPTotalAfter");
  const dashKEAfter = document.getElementById("dashKEAfter");
  
  if (collisionState.hasCollided) {
    if (dashP1After) dashP1After.textContent = collisionState.afterCollision.p1.toFixed(1);
    if (dashP2After) dashP2After.textContent = collisionState.afterCollision.p2.toFixed(1);
    if (dashPTotalAfter) dashPTotalAfter.textContent = collisionState.afterCollision.pTotal.toFixed(1);
    if (dashKEAfter) dashKEAfter.textContent = collisionState.afterCollision.ke.toFixed(1);
  } else {
    if (dashP1After) dashP1After.textContent = "--";
    if (dashP2After) dashP2After.textContent = "--";
    if (dashPTotalAfter) dashPTotalAfter.textContent = "--";
    if (dashKEAfter) dashKEAfter.textContent = "--";
  }
}

// -------------------- Chart --------------------
let momentumChart;

function initMomentumChart() {
  const ctx = document.getElementById("momentumChart");
  if (!ctx) return;
  
  momentumChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: ['Before', 'After'],
      datasets: [
        {
          label: 'p₁',
          data: [0, 0],
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: '#3b82f6',
          borderWidth: 1
        },
        {
          label: 'p₂',
          data: [0, 0],
          backgroundColor: 'rgba(239, 68, 68, 0.8)',
          borderColor: '#ef4444',
          borderWidth: 1
        },
        {
          label: 'Total',
          data: [0, 0],
          backgroundColor: 'rgba(16, 185, 129, 0.8)',
          borderColor: '#10b981',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: { 
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        },
        y: { 
          title: { display: true, text: 'Momentum (kg⋅m/s)', color: '#a9b4d0' },
          grid: { color: 'rgba(255,255,255,0.1)' },
          ticks: { color: '#a9b4d0' }
        }
      },
      plugins: {
        legend: { 
          labels: { color: '#a9b4d0' }
        }
      }
    }
  });
}

function updateMomentumChart() {
  if (!momentumChart) return;
  
  const beforeData = [
    collisionState.beforeCollision.p1,
    collisionState.beforeCollision.p2,
    collisionState.beforeCollision.pTotal
  ];
  
  const afterData = collisionState.hasCollided ? [
    collisionState.afterCollision.p1,
    collisionState.afterCollision.p2,
    collisionState.afterCollision.pTotal
  ] : [0, 0, 0];
  
  momentumChart.data.datasets[0].data = [beforeData[0], afterData[0]];
  momentumChart.data.datasets[1].data = [beforeData[1], afterData[1]];
  momentumChart.data.datasets[2].data = [beforeData[2], afterData[2]];
  
  momentumChart.update('none');
}

// -------------------- Bootstrap --------------------
window.addEventListener("DOMContentLoaded", () => {
  setupLangToggleCollision();
  applyCollisionI18n();
  bindCollisionControls();
  initP5Collision();
  initMomentumChart();
  resetCollision();
  
  // Update chart periodically
  setInterval(() => {
    if (collisionState.hasCollided) {
      updateMomentumChart();
    }
  }, 100);
});
