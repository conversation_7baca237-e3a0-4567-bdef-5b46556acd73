<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | قانون الغاز المثالي (PV=nRT)</title>
  <link rel="stylesheet" href="styles/workspace-config.css"/>
  <link rel="stylesheet" href="styles/ideal_gas.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
  <script src="scripts/workspace-config.js"></script>
</head>
<body>
<header class="site-header">
  <nav class="navbar">
    <div class="brand">
      <span class="brand-title" data-i18n="brand">PhysicsHub</span>
      <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
    </div>
    <ul class="nav-links">
      <li><a href="projectiles.html" data-i18n="nav_prev">المختبر السابق</a></li>
    </ul>
    <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
  </nav>
</header>

<main class="container">
  <section class="breadcrumbs">
    <a href="#" data-i18n="home">الصفحة الرئيسية</a>
    <span class="sep">/</span>
    <span data-i18n="lesson_breadcrumb">قانون الغاز المثالي</span>
  </section>

  <section class="lesson-hero">
    <h1 data-i18n="lesson_title">قانون الغاز المثالي: PV = nRT</h1>
    <p class="lead" data-i18n="lesson_lead">
      استكشف العلاقات بين الضغط والحجم ودرجة الحرارة وعدد الجسيمات. يمكنك تثبيت أحد المتغيرات لتوليد مخططات P-V أو V-T أو P-T، ومراقبة سلوك الغاز المثالي في وعاء بمكبس متحرك.
    </p>
  </section>

  <section class="theory card">
    <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
    <div class="theory-grid">
      <div>
        <ul class="bullets">
          <li data-i18n="t1">معادلة الحالة: PV = nRT</li>
          <li data-i18n="t2">قانون بويل: عند ثبوت T وn ⇒ P ∝ 1/V</li>
          <li data-i18n="t3">قانون شارل: عند ثبوت P وn ⇒ V ∝ T</li>
          <li data-i18n="t4">قانون جاي-لوساك: عند ثبوت V وn ⇒ P ∝ T</li>
        </ul>
        <div class="formula">P = (n R T) / V</div>
      </div>
      <div class="media">
        <div class="diagram-box">
          <div class="legend">
            <span class="legend-item"><i class="dot P"></i> P</span>
            <span class="legend-item"><i class="dot V"></i> V</span>
            <span class="legend-item"><i class="dot T"></i> T</span>
          </div>
          <div id="p5-container" class="p5-container gas-workspace" aria-label="Ideal gas container with piston"></div>
        </div>
      </div>
    </div>
  </section>

  <section class="lab card">
    <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
    <div class="lab-grid">
      <div class="lab-controls">
        <div class="field">
          <label for="n" data-i18n="n_label">عدد المولات n (mol)</label>
          <input type="range" id="n" min="0.1" max="10" step="0.1" value="1"/>
          <output id="nOut">1.0</output>
        </div>
        <div class="field">
          <label for="T" data-i18n="T_label">درجة الحرارة T (K)</label>
          <input type="range" id="T" min="150" max="600" step="1" value="300"/>
          <output id="TOut">300</output>
        </div>
        <div class="field">
          <label for="V" data-i18n="V_label">الحجم V (m³)</label>
          <input type="range" id="V" min="0.01" max="1.0" step="0.01" value="0.1"/>
          <output id="VOut">0.10</output>
        </div>
        <div class="field">
          <label for="mode" data-i18n="mode_label">وضع الرسم</label>
          <select id="mode">
            <option value="pv" selected data-i18n="mode_pv">P مقابل V (ثبوت T,n)</option>
            <option value="vt" data-i18n="mode_vt">V مقابل T (ثبوت P,n)</option>
            <option value="pt" data-i18n="mode_pt">P مقابل T (ثبوت V,n)</option>
          </select>
        </div>
        <div class="buttons">
          <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
          <button id="restart" class="btn" data-i18n="restart">بدء/إعادة تشغيل</button>
        </div>
      </div>

      <div class="lab-visuals">
        <div class="dash">
          <div class="dash-row">
            <div class="dash-item"><span>P</span> = <span id="dashP">24942</span> Pa</div>
            <div class="dash-item"><span>V</span> = <span id="dashV">0.10</span> m³</div>
            <div class="dash-item"><span>T</span> = <span id="dashT">300</span> K</div>
            <div class="dash-item"><span>n</span> = <span id="dashN">1.00</span> mol</div>
          </div>
          <canvas id="chart" height="140"></canvas>
        </div>
      </div>
    </div>
  </section>

  <section class="cta card">
    <h2 data-i18n="cta_title">الهدف التعليمي</h2>
    <p data-i18n="cta_text">
      استكشاف العلاقات بين المتغيرات: بويل (P-V)، شارل (V-T)، وجاي-لوساك (P-T) عمليًا عبر ضبط ثبات متغيرين ودراسة تغير الثالث.
    </p>
  </section>
</main>

<footer class="site-footer">
  <div class="footer-content">
    <div class="left">
      <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
      <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
    </div>
    <div class="right">
      <strong data-i18n="footer_contact_label">للتواصل:</strong>
      <span>
        Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
        <span data-i18n="footer_phone">الهاتف</span>:
        <a href="tel:+249912867327">+249912867327</a>,
        <a href="tel:+966538076790">+966538076790</a>
      </span>
    </div>
  </div>
</footer>

<script src="scripts/ideal_gas.js"></script>
</body>
</html>
