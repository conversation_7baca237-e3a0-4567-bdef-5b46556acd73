/**
 * Pendulum Simulation - Arabic UI
 * Numerical integration (semi-implicit Euler) with light damping.
 * Also renders: angle vs time and phase space (theta-omega).
 */
(function () {
  'use strict';

  // DOM refs
  const $ = (id) => document.getElementById(id);
  const len = $('len');
  const lenVal = $('lenVal');
  const grav = $('grav');
  const gravVal = $('gravVal');
  const theta0 = $('theta0');
  const theta0Val = $('theta0Val');
  const damp = $('damp');
  const dampVal = $('dampVal');
  const speed = $('speed');
  const speedVal = $('speedVal');

  const startBtn = $('startBtn');
  const pauseBtn = $('pauseBtn');
  const resetBtn = $('resetBtn');

  const TsmallEl = $('Tsmall');
  const TnumEl = $('Tnum');
  const EmEl = $('Em');

  const pendulumCanvas = $('pendulumCanvas');
  const angleCanvas = $('angleCanvas');
  const phaseCanvas = $('phaseCanvas');

  const pctx = pendulumCanvas.getContext('2d');
  const actx = angleCanvas.getContext('2d');
  const phctx = phaseCanvas.getContext('2d');

  // State
  let L = parseFloat(len.value);
  let g = parseFloat(grav.value);
  let theta = deg2rad(parseFloat(theta0.value)); // rad
  let omega = 0; // rad/s
  let b = parseFloat(damp.value); // damping factor (relative)
  let simSpeed = parseFloat(speed.value); // multiplier

  let running = false;
  let lastTime = 0;
  let time = 0;

  // For measuring numeric period
  let lastZeroCrossT = null;
  let prevTheta = theta;
  let Tnum = null;

  // For angle-time chart
  const angleHistory = [];
  const maxTimeWindow = 12; // seconds history
  const sampleEvery = 1 / 60; // s

  // Constants for drawing
  const origin = { x: pendulumCanvas.width / 2, y: 40 };
  const pxPerMeter = 160; // map meters to pixels for string length
  const bobRadius = 12;

  function deg2rad(d) { return (d * Math.PI) / 180; }
  function rad2deg(r) { return (r * 180) / Math.PI; }

  function syncInputs(range, number) {
    const link = (src, dst) => {
      src.addEventListener('input', () => {
        dst.value = src.value;
        onParamsChanged();
      });
      dst.addEventListener('input', () => {
        src.value = dst.value;
        onParamsChanged();
      });
    };
    link(range, number);
  }

  function onParamsChanged() {
    L = clamp(parseFloat(len.value), 0.1, 2.5);
    len.value = L.toFixed(2);
    lenVal.value = L.toFixed(2);

    g = clamp(parseFloat(grav.value), 1, 20);
    grav.value = g.toFixed(2);
    gravVal.value = g.toFixed(2);

    const thDeg = clamp(parseFloat(theta0.value), -60, 60);
    theta0.value = thDeg.toFixed(0);
    theta0Val.value = thDeg.toFixed(0);

    b = clamp(parseFloat(damp.value), 0, 0.2);
    damp.value = b.toFixed(3);
    dampVal.value = b.toFixed(3);

    simSpeed = clamp(parseFloat(speed.value), 0.1, 2);
    speed.value = simSpeed.toFixed(1);
    speedVal.value = simSpeed.toFixed(1);

    // Update small-angle period
    const Tsmall = 2 * Math.PI * Math.sqrt(L / g);
    TsmallEl.textContent = Tsmall.toFixed(3);
  }

  function clamp(v, min, max) {
    if (Number.isNaN(v)) return min;
    return Math.max(min, Math.min(max, v));
  }

  function resetSimulation() {
    theta = deg2rad(parseFloat(theta0.value));
    omega = 0;
    time = 0;
    lastTime = 0;
    lastZeroCrossT = null;
    prevTheta = theta;
    Tnum = null;
    angleHistory.length = 0;
    TnumEl.textContent = '—';
    updateEnergyDisplay();
    renderAll();
  }

  function updateEnergyDisplay() {
    // Scaled mechanical energy (no mass, just relative): E = (1/2)(L^2)ω^2 + gL(1 - cosθ)
    const kinetic = 0.5 * (L * L) * (omega * omega);
    const potential = g * L * (1 - Math.cos(theta));
    const E = kinetic + potential;
    EmEl.textContent = E.toFixed(3);
  }

  function integrate(dt) {
    // Equation: θ'' + (g/L) sinθ + b ω = 0 -> ω' = -(g/L)sinθ - b ω
    const alpha = -(g / L) * Math.sin(theta) - b * omega;
    // semi-implicit Euler
    omega += alpha * dt;
    theta += omega * dt;

    // Simple bounds to avoid drift to NaN
    if (!Number.isFinite(theta)) theta = 0;
    if (!Number.isFinite(omega)) omega = 0;

    // Detect zero crossing to estimate period (crossing near θ=0 with negative-to-positive change)
    if ((prevTheta < 0 && theta >= 0) || (prevTheta > 0 && theta <= 0)) {
      if (lastZeroCrossT !== null) {
        const T = time - lastZeroCrossT;
        if (T > 0.2) { // ignore tiny spurious intervals
          Tnum = T;
          TnumEl.textContent = Tnum.toFixed(3);
        }
      }
      lastZeroCrossT = time;
    }
    prevTheta = theta;

    updateEnergyDisplay();
  }

  function updateAngleHistory(dt) {
    // Keep a rolling window of (t, theta)
    angleHistory.push({ t: time, th: theta });
    // Remove old points
    while (angleHistory.length && (time - angleHistory[0].t) > maxTimeWindow) {
      angleHistory.shift();
    }
  }

  function drawPendulum() {
    const w = pendulumCanvas.width;
    const h = pendulumCanvas.height;

    pctx.clearRect(0, 0, w, h);

    // Anchor
    pctx.fillStyle = '#cfe6ff';
    pctx.beginPath();
    pctx.arc(origin.x, origin.y, 5, 0, Math.PI * 2);
    pctx.fill();

    // Bob position
    const lengthPx = Math.max(40, L * pxPerMeter);
    const x = origin.x + lengthPx * Math.sin(theta);
    const y = origin.y + lengthPx * Math.cos(theta);

    // String
    const grad = pctx.createLinearGradient(origin.x, origin.y, x, y);
    grad.addColorStop(0, '#7fb8ff');
    grad.addColorStop(1, '#2b8fe0');
    pctx.strokeStyle = grad;
    pctx.lineWidth = 3;
    pctx.beginPath();
    pctx.moveTo(origin.x, origin.y);
    pctx.lineTo(x, y);
    pctx.stroke();

    // Bob
    const bobGrad = pctx.createRadialGradient(x - 6, y - 6, 4, x, y, bobRadius + 4);
    bobGrad.addColorStop(0, '#ffffff');
    bobGrad.addColorStop(1, '#49b5ff');
    pctx.fillStyle = bobGrad;
    pctx.beginPath();
    pctx.arc(x, y, bobRadius, 0, Math.PI * 2);
    pctx.fill();

    // Shadow
    pctx.fillStyle = 'rgba(0,0,0,.25)';
    pctx.beginPath();
    pctx.ellipse(x + 4, y + 8, bobRadius * 0.9, bobRadius * 0.4, 0, 0, Math.PI * 2);
    pctx.fill();

    // Label L
    pctx.fillStyle = '#c9d7e7';
    pctx.font = '12px Cairo, sans-serif';
    pctx.fillText(`L = ${L.toFixed(2)} m`, origin.x + 8, origin.y + 16);
  }

  function drawAngleChart() {
    const w = angleCanvas.width;
    const h = angleCanvas.height;
    actx.clearRect(0, 0, w, h);

    // Axes
    drawAxes(actx, w, h);

    if (angleHistory.length < 2) return;

    // Map t to x, θ to y
    const t0 = Math.max(0, time - maxTimeWindow);
    const t1 = time;
    const xScale = (x) => ((x - t0) / (t1 - t0)) * (w - 40) + 30;

    // Set vertical scale based on +- max(|theta|) or +-1.4 rad default
    let maxAbs = 1.4;
    for (const p of angleHistory) maxAbs = Math.max(maxAbs, Math.abs(p.th));
    const yScale = (th) => (h - 20) - ((th + maxAbs) / (2 * maxAbs)) * (h - 40);

    // Line
    actx.strokeStyle = '#49b5ff';
    actx.lineWidth = 2;
    actx.beginPath();
    for (let i = 0; i < angleHistory.length; i++) {
      const pt = angleHistory[i];
      const x = xScale(pt.t);
      const y = yScale(pt.th);
      if (i === 0) actx.moveTo(x, y);
      else actx.lineTo(x, y);
    }
    actx.stroke();

    // Labels
    actx.fillStyle = '#c9d7e7';
    actx.font = '12px Cairo, sans-serif';
    actx.fillText('الزمن (ث)', w - 60, h - 6);
    actx.fillText('الزاوية θ (راديان)', 10, 14);
  }

  function drawPhaseChart() {
    const w = phaseCanvas.width;
    const h = phaseCanvas.height;
    phctx.clearRect(0, 0, w, h);

    drawAxes(phctx, w, h);

    // Draw single point for current (θ, ω) and a short trail
    const trail = 180; // last N samples
    const arr = angleHistory.slice(-trail);
    if (!arr.length) return;

    // Build omega history by approximating derivative from history
    const points = [];
    for (let i = 1; i < arr.length; i++) {
      const dt = arr[i].t - arr[i - 1].t || sampleEvery;
      const wApprox = (arr[i].th - arr[i - 1].th) / dt;
      points.push({ th: arr[i].th, om: wApprox });
    }
    if (!points.length) return;

    // Scales
    let maxTh = 1.4;
    let maxOm = 2.5;
    for (const p of points) {
      maxTh = Math.max(maxTh, Math.abs(p.th));
      maxOm = Math.max(maxOm, Math.abs(p.om));
    }
    const xScale = (th) => ((th + maxTh) / (2 * maxTh)) * (w - 40) + 30;
    const yScale = (om) => (h - 20) - ((om + maxOm) / (2 * maxOm)) * (h - 40);

    // Trail
    phctx.strokeStyle = '#2ec4b6';
    phctx.lineWidth = 1.75;
    phctx.beginPath();
    for (let i = 0; i < points.length; i++) {
      const x = xScale(points[i].th);
      const y = yScale(points[i].om);
      if (i === 0) phctx.moveTo(x, y);
      else phctx.lineTo(x, y);
    }
    phctx.stroke();

    // Current marker
    const x = xScale(theta);
    const y = yScale(omega);
    phctx.fillStyle = '#ffd166';
    phctx.beginPath();
    phctx.arc(x, y, 4, 0, Math.PI * 2);
    phctx.fill();

    phctx.fillStyle = '#c9d7e7';
    phctx.font = '12px Cairo, sans-serif';
    phctx.fillText('θ (راديان)', 10, 14);
    phctx.fillText('ω (راديان/ث)', w - 110, 14);
  }

  function drawAxes(ctx, w, h) {
    ctx.save();
    ctx.strokeStyle = 'rgba(255,255,255,.18)';
    ctx.lineWidth = 1;
    // Border
    ctx.strokeRect(20, 10, w - 40, h - 20);
    // Grid
    ctx.strokeStyle = 'rgba(255,255,255,.08)';
    for (let i = 1; i < 6; i++) {
      const x = 20 + (i * (w - 40)) / 6;
      const y = 10 + (i * (h - 20)) / 6;
      ctx.beginPath();
      ctx.moveTo(x, 10);
      ctx.lineTo(x, h - 10);
      ctx.moveTo(20, y);
      ctx.lineTo(w - 20, y);
      ctx.stroke();
    }
    ctx.restore();
  }

  function renderAll() {
    drawPendulum();
    drawAngleChart();
    drawPhaseChart();
  }

  function loop(ts) {
    if (!running) return;
    if (!lastTime) lastTime = ts;
    const rawDt = (ts - lastTime) / 1000;
    lastTime = ts;

    // Simulated dt
    const dt = clamp(rawDt * simSpeed, 0.001, 0.05);

    // Integrate; substeps for stability at high speeds
    const steps = Math.max(1, Math.min(4, Math.floor(simSpeed * 2)));
    const h = dt / steps;
    for (let i = 0; i < steps; i++) {
      time += h;
      integrate(h);
      // Sample angle history at roughly sampleEvery cadence
      if (!angleHistory.length || time - angleHistory[angleHistory.length - 1].t >= sampleEvery) {
        updateAngleHistory(h);
      }
    }

    renderAll();
    requestAnimationFrame(loop);
  }

  // Event bindings
  syncInputs(len, lenVal);
  syncInputs(grav, gravVal);
  syncInputs(theta0, theta0Val);
  syncInputs(damp, dampVal);
  syncInputs(speed, speedVal);

  startBtn.addEventListener('click', () => {
    if (!running) {
      running = true;
      lastTime = 0;
      requestAnimationFrame(loop);
    }
  });

  pauseBtn.addEventListener('click', () => {
    running = false;
  });

  resetBtn.addEventListener('click', () => {
    running = false;
    resetSimulation();
  });

  // Update derived values on manual edits
  [len, lenVal, grav, gravVal, theta0, theta0Val, damp, dampVal, speed, speedVal].forEach(el => {
    el.addEventListener('input', () => {
      onParamsChanged();
    });
  });

  // Initialize UI values and scene
  onParamsChanged();
  resetSimulation();
  renderAll();

})();
