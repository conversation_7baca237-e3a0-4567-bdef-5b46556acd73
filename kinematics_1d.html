<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>الحركة الخطية (1D Kinematics) | فيزيكس</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles/kinematics_1d.css">
</head>
<body class="bg-gray-50">
  <header class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 shadow-md">
    <div class="container mx-auto px-4 flex justify-between items-center">
      <div class="flex items-center">
        <i class="fas fa-atom text-2xl mr-3"></i>
        <h1 class="text-2xl font-bold">الحركة الخطية</h1>
      </div>
      <nav class="flex items-center gap-4">
        <a href="index.html" class="hover:text-indigo-200">العودة للرئيسية</a>
        <button id="darkModeToggle" class="bg-gray-900/30 px-3 py-1.5 rounded">الوضع الليلي</button>
      </nav>
    </div>
  </header>

  <main class="container mx-auto px-4 py-6">
    <section class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2 simulation-window bg-white rounded-xl shadow p-4">
        <div class="flex justify-between items-center mb-3">
          <h2 class="text-xl font-semibold">محاكاة الحركة الخطية (x–t, v–t, a–t)</h2>
          <button id="resetBtn" class="text-indigo-600 text-sm flex items-center gap-1">
            <i class="fas fa-redo"></i><span>إعادة تعيين</span>
          </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm text-gray-600 mb-1">الإزاحة الابتدائية x₀ (م)</label>
            <input id="x0" type="range" min="-20" max="20" value="0" step="1" class="w-full">
            <div class="text-xs text-gray-500 mt-1"><span id="x0Val">0</span> m</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1">السرعة الابتدائية v₀ (م/ث)</label>
            <input id="v0" type="range" min="-20" max="20" value="8" step="1" class="w-full">
            <div class="text-xs text-gray-500 mt-1"><span id="v0Val">8</span> m/s</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 mb-1">العجلة a (م/ث²)</label>
            <input id="a" type="range" min="-10" max="10" value="0" step="1" class="w-full">
            <div class="text-xs text-gray-500 mt-1"><span id="aVal">0</span> m/s²</div>
          </div>
        </div>

        <div class="bg-gray-100 rounded-lg p-3 mb-3">
          <div class="flex flex-wrap gap-2">
            <button id="playPause" class="bg-indigo-600 text-white px-3 py-1.5 rounded text-sm">
              تشغيل
            </button>
            <button id="stepBtn" class="bg-white border px-3 py-1.5 rounded text-sm">
              خطوة +0.1 ث
            </button>
            <label class="text-sm text-gray-700 flex items-center gap-2">
              Δt:
              <input id="dt" type="range" min="0.02" max="0.2" step="0.01" value="0.05">
              <span id="dtVal" class="text-xs text-gray-600">0.05 s</span>
            </label>
            <label class="text-sm text-gray-700 flex items-center gap-2">
              مسارات متعددة:
              <input id="multiPath" type="checkbox">
            </label>
            <button id="savePath" class="bg-emerald-600 text-white px-3 py-1.5 rounded text-sm">
              حفظ المسار الحالي
            </button>
            <button id="clearPaths" class="bg-rose-600 text-white px-3 py-1.5 rounded text-sm">
              مسح المسارات
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-4">
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">موضع الجسيم</div>
            <canvas id="canvasMotion" class="w-full h-56"></canvas>
          </div>
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">مخطط x–t</div>
            <canvas id="canvasXT" class="w-full h-56"></canvas>
          </div>
          <div class="bg-white rounded border p-2">
            <div class="text-sm font-medium mb-1">مخطط v–t / a–t</div>
            <canvas id="canvasVT" class="w-full h-56"></canvas>
          </div>
        </div>
      </div>

      <aside class="bg-white rounded-xl shadow p-4">
        <h3 class="text-lg font-semibold mb-3">النتائج</h3>
        <ul class="space-y-2 text-sm text-gray-700">
          <li>الزمن t: <span id="tVal">0.00</span> s</li>
          <li>الموضع x(t): <span id="xVal">0.00</span> m</li>
          <li>السرعة v(t): <span id="vVal">0.00</span> m/s</li>
          <li>العجلة a: <span id="aOut">0.00</span> m/s²</li>
        </ul>
        <div class="mt-4 text-xs text-gray-600">
          معادلات الحركة: x(t)=x₀+v₀t+½at²، v(t)=v₀+at.
        </div>
        <div class="mt-4">
          <a href="index.html" class="text-indigo-600 text-sm">عودة إلى المنصة الرئيسية</a>
        </div>
      </aside>
    </section>
  </main>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" defer></script>
  <script src="scripts/kinematics_1d.js"></script>
</body>
</html>
