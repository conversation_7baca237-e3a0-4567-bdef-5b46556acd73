/**
 * PhysicsHub - Workspace Configuration
 * Standardized workspace dimensions and scaling for all labs
 * Author: Dr. <PERSON>, SUST - BME, @ 2025.
 */

:root {
  /* Workspace dimensions - standardized across all labs */
  --workspace-width: 600px;
  --workspace-height: 400px;
  --workspace-min-width: 400px;
  --workspace-min-height: 300px;
  --workspace-max-width: 800px;
  --workspace-max-height: 500px;
  
  /* Scaling factors */
  --scale-factor: 1.0;
  --mobile-scale-factor: 0.8;
  --tablet-scale-factor: 0.9;
  
  /* Grid and measurement units */
  --grid-size: 20px;
  --measurement-unit: 1px; /* 1 pixel = 1 cm in simulation */
  
  /* Physics simulation bounds */
  --physics-bounds-padding: 20px;
  --physics-world-width: calc(var(--workspace-width) - 2 * var(--physics-bounds-padding));
  --physics-world-height: calc(var(--workspace-height) - 2 * var(--physics-bounds-padding));
}

/* Standardized p5 container */
.p5-container {
  width: var(--workspace-width);
  height: var(--workspace-height);
  min-width: var(--workspace-min-width);
  min-height: var(--workspace-min-height);
  max-width: var(--workspace-max-width);
  max-height: var(--workspace-max-height);
  border-radius: 8px;
  background: rgba(0,0,0,0.3);
  position: relative;
  margin: 0 auto;
  border: 2px solid rgba(255,255,255,0.1);
  box-shadow: inset 0 2px 10px rgba(0,0,0,0.3);
}

/* Workspace overlay for measurements and grid */
.p5-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: var(--grid-size) var(--grid-size);
  pointer-events: none;
  border-radius: 6px;
}

/* Workspace info overlay */
.workspace-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0,0,0,0.7);
  color: rgba(255,255,255,0.8);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  pointer-events: none;
  z-index: 10;
}

/* Scale indicator */
.scale-indicator {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0,0,0,0.7);
  color: rgba(255,255,255,0.8);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  pointer-events: none;
  z-index: 10;
}

.scale-indicator::before {
  content: '';
  display: block;
  width: 50px;
  height: 2px;
  background: rgba(255,255,255,0.8);
  margin-bottom: 3px;
}

/* Responsive scaling */
@media (max-width: 768px) {
  :root {
    --workspace-width: calc(100vw - 40px);
    --workspace-height: calc(60vw);
    --scale-factor: var(--mobile-scale-factor);
  }
  
  .p5-container {
    max-width: calc(100vw - 40px);
    max-height: calc(60vw);
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  :root {
    --scale-factor: var(--tablet-scale-factor);
  }
}

/* Lab-specific workspace adjustments */
.projectiles-workspace {
  --workspace-height: 450px;
  --physics-world-width: 580px;
  --physics-world-height: 410px;
}

.collision-workspace {
  --workspace-width: 700px;
  --workspace-height: 350px;
}

.energy-workspace {
  --workspace-width: 650px;
  --workspace-height: 450px;
}

.torque-workspace {
  --workspace-width: 550px;
  --workspace-height: 350px;
}

.pendulum-workspace {
  --workspace-width: 500px;
  --workspace-height: 500px;
}

.gas-workspace {
  --workspace-width: 500px;
  --workspace-height: 500px;
}

.carnot-workspace {
  --workspace-width: 650px;
  --workspace-height: 400px;
}

.heat-transfer-workspace {
  --workspace-width: 600px;
  --workspace-height: 400px;
}

.states-matter-workspace {
  --workspace-width: 550px;
  --workspace-height: 450px;
}

.specific-heat-workspace {
  --workspace-width: 600px;
  --workspace-height: 400px;
}

/* Physics coordinate system helpers */
.physics-coords {
  position: relative;
}

.physics-coords::after {
  content: '';
  position: absolute;
  bottom: var(--physics-bounds-padding);
  left: var(--physics-bounds-padding);
  width: calc(100% - 2 * var(--physics-bounds-padding));
  height: calc(100% - 2 * var(--physics-bounds-padding));
  border: 1px solid rgba(255,255,255,0.2);
  pointer-events: none;
}

/* Measurement rulers */
.ruler-x {
  position: absolute;
  bottom: 5px;
  left: var(--physics-bounds-padding);
  right: var(--physics-bounds-padding);
  height: 15px;
  background: linear-gradient(to right, 
    transparent 0%, 
    rgba(255,255,255,0.1) 2%, 
    transparent 4%);
  background-size: var(--grid-size) 100%;
  pointer-events: none;
}

.ruler-y {
  position: absolute;
  top: var(--physics-bounds-padding);
  bottom: var(--physics-bounds-padding);
  left: 5px;
  width: 15px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    rgba(255,255,255,0.1) 2%, 
    transparent 4%);
  background-size: 100% var(--grid-size);
  pointer-events: none;
}

/* Animation performance optimizations */
.p5-container canvas {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .p5-container {
    image-rendering: auto;
  }
}

/* Accessibility improvements */
.p5-container:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Loading state */
.p5-container.loading {
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.p5-container.loading::after {
  content: 'Loading simulation...';
  color: rgba(255,255,255,0.7);
  font-size: 14px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Error state */
.p5-container.error {
  background: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.3);
}

.p5-container.error::after {
  content: 'Simulation error. Please refresh.';
  color: #dc2626;
  font-size: 14px;
}
