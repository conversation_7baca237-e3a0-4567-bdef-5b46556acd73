<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
    <style>
        /* Custom styles for RTL support */
        [dir="rtl"] .rtl-mirror {
            transform: scaleX(-1);
        }
        .slider-thumb::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }
        .slider-thumb::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="flex justify-between items-center p-4 bg-white shadow-md">
        <a href="#" class="text-xl font-bold text-blue-600">PhysicsPlatform</a>
        <button id="langToggle" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition">
            العربية
        </button>
    </nav>

    <main class="container mx-auto p-6 space-y-12">
        <!-- Page Header -->
        <section class="bg-white p-6 rounded-lg shadow">
            <h1 id="pageTitle" class="text-3xl font-bold mb-4">Forces & Uniform Circular Motion</h1>
            <p id="pageDescription" class="text-lg text-gray-700">
                When an object travels in a circle at constant speed it still accelerates toward the center. This acceleration requires a net force: the centripetal force.
            </p>
        </section>

        <!-- Interactive Calculator -->
        <section class="bg-white p-6 rounded-lg shadow">
            <div id="calculatorContainer">
                <h2 id="calculatorTitle" class="text-2xl font-bold mb-4 text-center">Banked-Curve Speed Calculator</h2>
                
                <div class="space-y-6 max-w-md mx-auto">
                    <div>
                        <label for="radiusSlider" id="radiusLabel" class="block mb-2">Radius (m): <span id="radiusValue">50</span></label>
                        <input type="range" id="radiusSlider" min="10" max="200" value="50" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb">
                    </div>
                    
                    <div>
                        <label for="angleSlider" id="angleLabel" class="block mb-2">Banking angle (°): <span id="angleValue">15</span></label>
                        <input type="range" id="angleSlider" min="0" max="45" value="15" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb">
                    </div>
                    
                    <div>
                        <label for="muSlider" id="muLabel" class="block mb-2">Friction coefficient μ: <span id="muValue">0.4</span></label>
                        <input type="range" id="muSlider" min="0" max="1" step="0.01" value="0.4" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb">
                    </div>
                    
                    <div class="p-4 bg-blue-50 rounded-lg">
                        <p id="vMaxLabel" class="font-semibold">Maximum safe speed:</p>
                        <p id="vMaxValue" class="text-2xl font-bold text-blue-700">17.15 m/s</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Virtual Lab -->
        <section class="bg-white p-6 rounded-lg shadow">
            <div id="labContainer" class="flex flex-col items-center">
                <h2 id="labTitle" class="text-2xl font-bold mb-4">Loop-the-Loop Virtual Lab</h2>
                <div id="p5Canvas" class="border border-gray-200 rounded-lg"></div>
            </div>
        </section>
    </main>

    <script>
        // Translation data
        const translations = {
            en: {
                pageTitle: "Forces & Uniform Circular Motion",
                pageDescription: "When an object travels in a circle at constant speed it still accelerates toward the center. This acceleration requires a net force: the centripetal force.",
                calculatorTitle: "Banked-Curve Speed Calculator",
                radiusLabel: "Radius (m):",
                angleLabel: "Banking angle (°):",
                muLabel: "Friction coefficient μ:",
                vMaxLabel: "Maximum safe speed:",
                labTitle: "Loop-the-Loop Virtual Lab",
                langToggle: "العربية"
            },
            ar: {
                pageTitle: "القوى والحركة الدائرية المنتظمة",
                pageDescription: "عندما يتحرك جسم في دائرة بسرعة ثابتة فإنه لا يزال يتسارع نحو المركز. يتطلب هذا التسارع قوة محصلة: القوة الجاذبة المركزية.",
                calculatorTitle: "حاسبة السرعة على المنعطف المائل",
                radiusLabel: "نصف القطر (م):",
                angleLabel: "زاوية الميل (°):",
                muLabel: "معامل الاحتكاك μ:",
                vMaxLabel: "السرعة القصوى الآمنة:",
                labTitle: "المختبر الافتراضي – الحلقة العمودية",
                langToggle: "English"
            }
        };

        // Current language state
        let currentLang = 'en';

        // DOM elements
        const langToggleBtn = document.getElementById('langToggle');
        const pageTitle = document.getElementById('pageTitle');
        const pageDescription = document.getElementById('pageDescription');
        const calculatorTitle = document.getElementById('calculatorTitle');
        const radiusLabel = document.getElementById('radiusLabel');
        const angleLabel = document.getElementById('angleLabel');
        const muLabel = document.getElementById('muLabel');
        const vMaxLabel = document.getElementById('vMaxLabel');
        const labTitle = document.getElementById('labTitle');
        const calculatorContainer = document.getElementById('calculatorContainer');
        const labContainer = document.getElementById('labContainer');

        // Toggle language function
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'ar' : 'en';
            updateTranslations();
            updateLayoutDirection();
        }

        // Update all text elements with translations
        function updateTranslations() {
            const t = translations[currentLang];
            
            pageTitle.textContent = t.pageTitle;
            pageDescription.textContent = t.pageDescription;
            calculatorTitle.textContent = t.calculatorTitle;
            radiusLabel.textContent = t.radiusLabel;
            angleLabel.textContent = t.angleLabel;
            muLabel.textContent = t.muLabel;
            vMaxLabel.textContent = t.vMaxLabel;
            labTitle.textContent = t.labTitle;
            langToggleBtn.textContent = t.langToggle;
        }

        // Update layout direction based on language
        function updateLayoutDirection() {
            if (currentLang === 'ar') {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
                calculatorContainer.classList.add('rtl-mirror');
                labContainer.classList.add('rtl-mirror');
            } else {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
                calculatorContainer.classList.remove('rtl-mirror');
                labContainer.classList.remove('rtl-mirror');
            }
        }

        // Initialize calculator
        function initCalculator() {
            const radiusSlider = document.getElementById('radiusSlider');
            const angleSlider = document.getElementById('angleSlider');
            const muSlider = document.getElementById('muSlider');
            const radiusValue = document.getElementById('radiusValue');
            const angleValue = document.getElementById('angleValue');
            const muValue = document.getElementById('muValue');
            const vMaxValue = document.getElementById('vMaxValue');

            function calculateVMax() {
                const r = parseFloat(radiusSlider.value);
                const θ = parseFloat(angleSlider.value);
                const μ = parseFloat(muSlider.value);
                
                const θRad = θ * Math.PI / 180;
                const g = 9.8;
                const vMax = Math.sqrt(r * g * (Math.tan(θRad) + μ) / (1 - μ * Math.tan(θRad)));
                
                vMaxValue.textContent = vMax.toFixed(2) + ' m/s';
            }

            radiusSlider.addEventListener('input', function() {
                radiusValue.textContent = this.value;
                calculateVMax();
            });

            angleSlider.addEventListener('input', function() {
                angleValue.textContent = this.value;
                calculateVMax();
            });

            muSlider.addEventListener('input', function() {
                muValue.textContent = this.value;
                calculateVMax();
            });

            // Initial calculation
            calculateVMax();
        }

        // Initialize p5.js sketch
        function initP5Sketch() {
            return new p5(function(p) {
                let r = 80, cart, angle = 0, speed = 1.5;
                
                p.setup = function() {
                    const canvas = p.createCanvas(400, 400);
                    canvas.parent('p5Canvas');
                    cart = { x: 0, y: 0 };
                };
                
                p.draw = function() {
                    p.background(240);
                    p.translate(p.width/2, p.height/2);
                    p.noFill();
                    p.circle(0, 0, 2*r);
                    cart.x = r * p.cos(angle);
                    cart.y = r * p.sin(angle);
                    p.fill('red');
                    p.circle(cart.x, cart.y, 20);
                    angle += speed / r;
                };
            });
        }

        // Event listeners
        langToggleBtn.addEventListener('click', toggleLanguage);

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            updateTranslations();
            initCalculator();
            initP5Sketch();
        });
    </script>
</body>
</html>