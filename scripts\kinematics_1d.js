// Kinematics 1D Simulation
(function () {
  const $ = (id) => document.getElementById(id);

  // Inputs
  const x0El = $('x0');
  const v0El = $('v0');
  const aEl = $('a');
  const dtEl = $('dt');
  const playPauseBtn = $('playPause');
  const stepBtn = $('stepBtn');
  const resetBtn = $('resetBtn');
  const multiPathEl = $('multiPath');
  const savePathBtn = $('savePath');
  const clearPathsBtn = $('clearPaths');

  // Outputs
  const x0Val = $('x0Val');
  const v0Val = $('v0Val');
  const aVal = $('aVal');
  const dtVal = $('dtVal');
  const tVal = $('tVal');
  const xVal = $('xVal');
  const vVal = $('vVal');
  const aOut = $('aOut');

  // Canvases
  const canvasMotion = $('canvasMotion');
  const canvasXT = $('canvasXT');
  const canvasVT = $('canvasVT');

  // Utilities: HiDPI scaling
  function setupHiDPICanvas(canvas) {
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    const cssWidth = canvas.clientWidth || canvas.offsetWidth;
    const cssHeight = canvas.clientHeight || canvas.offsetHeight;
    if (!cssWidth || !cssHeight) return ratio;
    canvas.width = Math.floor(cssWidth * ratio);
    canvas.height = Math.floor(cssHeight * ratio);
    const ctx = canvas.getContext('2d');
    ctx.setTransform(ratio, 0, 0, ratio, 0, 0);
    return ratio;
  }

  const ctxMotion = canvasMotion.getContext('2d');
  const ctxXT = canvasXT.getContext('2d');
  const ctxVT = canvasVT.getContext('2d');

  function resizeAll() {
    setupHiDPICanvas(canvasMotion);
    setupHiDPICanvas(canvasXT);
    setupHiDPICanvas(canvasVT);
    drawStaticGrids();
  }

  window.addEventListener('resize', () => {
    clearTimeout(resizeTid);
    resizeTid = setTimeout(resizeAll, 150);
  });
  let resizeTid;

  // State
  let running = false;
  let t = 0;
  let x0 = parseFloat(x0El.value);
  let v0 = parseFloat(v0El.value);
  let acc = parseFloat(aEl.value);
  let dt = parseFloat(dtEl.value);
  let pathSamples = []; // {t,x,v}
  let savedPaths = []; // array of arrays for multi-path comparison

  // Scales (simple pixels-per-unit assumptions)
  const pxPerMeterX = 8; // motion view scale (horizontal)
  const pxPerMeterY = 8; // used in plots
  const maxTime = 20; // seconds scale for plots

  function updateLabels() {
    x0Val.textContent = x0.toFixed(0);
    v0Val.textContent = v0.toFixed(0);
    aVal.textContent = acc.toFixed(0);
    dtVal.textContent = `${dt.toFixed(2)} s`;
    const x = x0 + v0 * t + 0.5 * acc * t * t;
    const v = v0 + acc * t;
    tVal.textContent = t.toFixed(2);
    xVal.textContent = x.toFixed(2);
    vVal.textContent = v.toFixed(2);
    aOut.textContent = acc.toFixed(2);
  }

  function clearCanvas(ctx, canvas) {
    ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight);
  }

  function drawGrid(ctx, canvas, options = {}) {
    const { step = 40, color = '#E5E7EB' } = options;
    ctx.save();
    ctx.strokeStyle = color;
    ctx.lineWidth = 1;

    for (let x = 0; x < canvas.clientWidth; x += step) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.clientHeight);
      ctx.stroke();
    }
    for (let y = 0; y < canvas.clientHeight; y += step) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.clientWidth, y);
      ctx.stroke();
    }
    ctx.restore();
  }

  function drawAxes(ctx, canvas, originX, originY) {
    ctx.save();
    ctx.strokeStyle = '#9CA3AF';
    ctx.lineWidth = 1.5;
    // X axis
    ctx.beginPath();
    ctx.moveTo(0, originY);
    ctx.lineTo(canvas.clientWidth, originY);
    ctx.stroke();
    // Y axis
    ctx.beginPath();
    ctx.moveTo(originX, 0);
    ctx.lineTo(originX, canvas.clientHeight);
    ctx.stroke();
    ctx.restore();
  }

  function drawStaticGrids() {
    // Motion view
    clearCanvas(ctxMotion, canvasMotion);
    drawGrid(ctxMotion, canvasMotion);
    const originMotionX = 40;
    const originMotionY = canvasMotion.clientHeight - 30;
    drawAxes(ctxMotion, canvasMotion, originMotionX, originMotionY);

    // x-t view
    clearCanvas(ctxXT, canvasXT);
    drawGrid(ctxXT, canvasXT);
    const originXTX = 40;
    const originXTY = canvasXT.clientHeight - 30;
    drawAxes(ctxXT, canvasXT, originXTX, originXTY);

    // v/a - t view
    clearCanvas(ctxVT, canvasVT);
    drawGrid(ctxVT, canvasVT);
    const originVTX = 40;
    const originVTY = canvasVT.clientHeight - 30;
    drawAxes(ctxVT, canvasVT, originVTX, originVTY);
  }

  function worldToMotion(xMeters) {
    const originX = 40;
    const originY = canvasMotion.clientHeight - 30;
    return { x: originX + xMeters * pxPerMeterX, y: originY };
  }

  function drawParticle(xMeters) {
    const p = worldToMotion(xMeters);
    const r = 6;
    ctxMotion.save();
    ctxMotion.fillStyle = '#4F46E5';
    ctxMotion.beginPath();
    ctxMotion.arc(p.x, p.y - 12, r, 0, Math.PI * 2);
    ctxMotion.fill();
    // A simple track line
    ctxMotion.strokeStyle = '#4F46E5';
    ctxMotion.lineWidth = 2;
    ctxMotion.beginPath();
    ctxMotion.moveTo(40, p.y);
    ctxMotion.lineTo(canvasMotion.clientWidth - 10, p.y);
    ctxMotion.stroke();
    ctxMotion.restore();
  }

  function pushSample() {
    const x = x0 + v0 * t + 0.5 * acc * t * t;
    const v = v0 + acc * t;
    pathSamples.push({ t, x, v, a: acc });
  }

  function drawXT(samples, color = '#111827') {
    const paddingLeft = 40;
    const paddingBottom = 30;
    const width = canvasXT.clientWidth - paddingLeft - 10;
    const height = canvasXT.clientHeight - paddingBottom - 10;

    ctxXT.save();
    ctxXT.strokeStyle = color;
    ctxXT.lineWidth = 2;
    ctxXT.beginPath();
    for (let i = 0; i < samples.length; i++) {
      const s = samples[i];
      const tx = paddingLeft + (s.t / maxTime) * width;
      const ty = canvasXT.clientHeight - paddingBottom - s.x * pxPerMeterY * 0.5; // scale down for visibility
      if (i === 0) ctxXT.moveTo(tx, ty);
      else ctxXT.lineTo(tx, ty);
    }
    ctxXT.stroke();
    ctxXT.restore();
  }

  function drawVT(samples, colorV = '#2563EB', colorA = '#10B981') {
    const paddingLeft = 40;
    const paddingBottom = 30;
    const width = canvasVT.clientWidth - paddingLeft - 10;
    const height = canvasVT.clientHeight - paddingBottom - 10;

    // v-t
    ctxVT.save();
    ctxVT.strokeStyle = colorV;
    ctxVT.lineWidth = 2;
    ctxVT.beginPath();
    for (let i = 0; i < samples.length; i++) {
      const s = samples[i];
      const tx = paddingLeft + (s.t / maxTime) * width;
      const ty = canvasVT.clientHeight - paddingBottom - s.v * pxPerMeterY * 1.5; // amplify velocity scale
      if (i === 0) ctxVT.moveTo(tx, ty);
      else ctxVT.lineTo(tx, ty);
    }
    ctxVT.stroke();
    ctxVT.restore();

    // a-t (horizontal line)
    ctxVT.save();
    ctxVT.strokeStyle = colorA;
    ctxVT.setLineDash([5, 4]);
    ctxVT.lineWidth = 1.5;
    ctxVT.beginPath();
    const baseY = canvasVT.clientHeight - paddingBottom - acc * pxPerMeterY * 1.5;
    ctxVT.moveTo(paddingLeft, baseY);
    ctxVT.lineTo(canvasVT.clientWidth - 10, baseY);
    ctxVT.stroke();
    ctxVT.restore();
  }

  function redrawAll() {
    drawStaticGrids();

    // Motion
    const x = x0 + v0 * t + 0.5 * acc * t * t;
    drawParticle(x);

    // Plots (existing and saved)
    drawXT(pathSamples, '#111827');
    savedPaths.forEach((arr, idx) => {
      const hue = 200 + (idx * 50) % 160;
      drawXT(arr, `hsl(${hue} 70% 40%)`);
    });

    drawVT(pathSamples, '#2563EB', '#10B981');
  }

  function reset() {
    running = false;
    t = 0;
    pathSamples = [];
    updateLabels();
    redrawAll();
  }

  function step(simDt) {
    t += simDt;
    if (t > maxTime) t = 0;
    pushSample();
    updateLabels();
    redrawAll();
  }

  let rafId = null;
  function loop() {
    if (!running) return;
    step(dt);
    rafId = requestAnimationFrame(loop);
  }

  // Initial setup
  resizeAll();
  updateLabels();
  redrawAll();

  // Event listeners
  x0El.addEventListener('input', () => {
    x0 = parseFloat(x0El.value);
    x0Val.textContent = x0.toFixed(0);
    if (!running) { updateLabels(); redrawAll(); }
  });
  v0El.addEventListener('input', () => {
    v0 = parseFloat(v0El.value);
    v0Val.textContent = v0.toFixed(0);
    if (!running) { updateLabels(); redrawAll(); }
  });
  aEl.addEventListener('input', () => {
    acc = parseFloat(aEl.value);
    aVal.textContent = acc.toFixed(0);
    if (!running) { updateLabels(); redrawAll(); }
  });
  dtEl.addEventListener('input', () => {
    dt = parseFloat(dtEl.value);
    dtVal.textContent = `${dt.toFixed(2)} s`;
  });

  playPauseBtn.addEventListener('click', () => {
    running = !running;
    playPauseBtn.textContent = running ? 'إيقاف' : 'تشغيل';
    if (running) {
      loop();
    } else if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  });

  stepBtn.addEventListener('click', () => {
    step(0.1);
  });

  resetBtn.addEventListener('click', () => {
    reset();
  });

  savePathBtn.addEventListener('click', () => {
    if (!multiPathEl.checked) return;
    if (pathSamples.length) {
      // clone current samples
      savedPaths.push(pathSamples.map(s => ({ ...s })));
      // restart current
      t = 0;
      pathSamples = [];
      updateLabels();
      redrawAll();
    }
  });

  clearPathsBtn.addEventListener('click', () => {
    savedPaths = [];
    redrawAll();
  });

  // Accessibility: ensure inputs have title for lint warnings
  x0El.title = 'الإزاحة الابتدائية x0';
  v0El.title = 'السرعة الابتدائية v0';
  aEl.title = 'العجلة a';
  dtEl.title = 'خطوة الزمن Δt';
})();
