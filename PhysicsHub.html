<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhysicsHub - القوى والحركة الدائرية المنتظمة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        
        body {
            font-family: 'Tajawal', sans-serif;
        }
        
        .rtl-support {
            direction: rtl;
            text-align: right;
        }
        
        .circular-motion-canvas {
            border: 2px solid #3b82f6;
            border-radius: 8px;
            background-color: #f8fafc;
        }
        
        .nav-link:hover {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }
        
        .experiment-card {
            transition: transform 0.3s ease;
        }
        
        .experiment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .slider-thumb::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }
        
        .dark-mode {
            background-color: #1e293b;
            color: #f8fafc;
        }
        
        .dark-mode .circular-motion-canvas {
            background-color: #334155;
            border-color: #60a5fa;
        }
    </style>
</head>
<body class="bg-gray-50 rtl-support">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <a href="#" class="flex items-center space-x-2">
                    <i class="fas fa-atom text-3xl text-blue-500"></i>
                    <span class="text-xl font-bold text-gray-800">PhysicsHub</span>
                </a>
            </div>
            
            <div class="hidden md:flex items-center space-x-6 space-x-reverse">
                <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">الصفحة الرئيسية</a>
                <div class="relative group">
                    <button class="nav-link text-gray-700 hover:text-blue-500 font-medium flex items-center">
                        الميكانيكا الكلاسيكية
                        <i class="fas fa-chevron-down mr-1 text-sm"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-blue-50">علم الحركة</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-blue-50">القوى والحركة الدائرية</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-blue-50">الشغل والطاقة</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-blue-50">علم السكون والعزم</a>
                    </div>
                </div>
                <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">الديناميكا الحرارية</a>
                <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">الكهرومغناطيسية</a>
                <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">الفيزياء الحديثة</a>
                <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">مركز المختبرات</a>
            </div>
            
            <div class="flex items-center space-x-4 space-x-reverse">
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-moon text-gray-600"></i>
                </button>
                <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-sign-in-alt mr-2"></i>تسجيل الدخول
                </button>
                <button class="md:hidden text-gray-700">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <div class="container mx-auto px-4 py-3 text-sm text-gray-600">
        <a href="#" class="hover:text-blue-500">الصفحة الرئيسية</a>
        <span class="mx-2">/</span>
        <a href="#" class="hover:text-blue-500">الميكانيكا الكلاسيكية</a>
        <span class="mx-2">/</span>
        <span class="text-blue-500">القوى والحركة الدائرية المنتظمة</span>
    </div>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">القوى والحركة الدائرية المنتظمة</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">فهم الجاذبية المدارية والقوة المركزية في الأنظمة الدائرية</p>
        </div>

        <!-- Theory Section -->
        <section class="mb-16 bg-white rounded-xl shadow-md overflow-hidden">
            <div class="p-6 bg-blue-500 text-white">
                <h2 class="text-2xl font-bold">الشرح النظري المبسط</h2>
            </div>
            <div class="p-6">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">تعريف الحركة الدائرية المنتظمة (UCM)</h3>
                        <p class="text-gray-700 mb-6">
                            الحركة الدائرية المنتظمة هي حركة جسم بسرعة ثابتة على مسار دائري. على الرغم من ثبات مقدار السرعة، إلا أن اتجاه السرعة يتغير باستمرار بسبب تغير اتجاه الحركة، مما يؤدي إلى تسارع مركزي.
                        </p>
                        
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">القوة المركزية (Centripetal Force)</h3>
                        <p class="text-gray-700 mb-6">
                            القوة المركزية هي القوة اللازمة لإبقاء الجسم في مسار دائري، ويكون اتجاهها دائمًا نحو مركز الدائرة. هذه القوة ليست نوعًا جديدًا من القوى، بل يمكن أن تكون أي قوة موجودة في الطبيعة (مثل قوة الشد في خيط، قوة الجاذبية، قوة الاحتكاك، إلخ).
                        </p>
                        
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
                            <h4 class="font-bold text-blue-700 mb-2">العلاقة الرياضية:</h4>
                            <p class="text-lg text-center">
                                \( F_c = \frac{mv^2}{r} \)
                            </p>
                            <p class="text-gray-700 mt-2">
                                حيث:
                                <br>\( F_c \) = القوة المركزية (نيوتن)
                                <br>\( m \) = كتلة الجسم (كجم)
                                <br>\( v \) = السرعة الخطية (م/ث)
                                <br>\( r \) = نصف قطر المسار الدائري (م)
                            </p>
                        </div>
                    </div>
                    
                    <div>
                        <div class="bg-gray-100 rounded-lg p-4 mb-6">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/Uniform_circular_motion.svg/1200px-Uniform_circular_motion.svg.png" 
                                 alt="Uniform Circular Motion Diagram" class="w-full rounded-lg">
                            <p class="text-center text-sm text-gray-600 mt-2">توضيح للقوة المركزية والسرعة في الحركة الدائرية</p>
                        </div>
                        
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-800 mb-2">أمثلة من الحياة اليومية:</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-2">
                                <li>سيارة تدور حول منعطف (قوة الاحتكاك هي القوة المركزية)</li>
                                <li>قمر صناعي يدور حول الأرض (قوة الجاذبية هي القوة المركزية)</li>
                                <li>حجر مربوط بخيط يدور في مسار دائري (قوة الشد في الخيط هي القوة المركزية)</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                            <h4 class="font-bold text-yellow-700 mb-2">ملاحظة مهمة:</h4>
                            <p class="text-gray-700">
                                القوة الطاردة المركزية هي قوة وهمية تظهر فقط في الإطار المرجعي الدوار. في التحليل الفيزيائي الحقيقي (في إطار مرجعي قصوري)، لا وجود لها. القوة الحقيقية هي القوة المركزية التي توجه نحو الداخل.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Calculator -->
        <section class="mb-16 bg-white rounded-xl shadow-md overflow-hidden">
            <div class="p-6 bg-green-500 text-white">
                <h2 class="text-2xl font-bold">حاسبة القوة المركزية المدارية</h2>
            </div>
            <div class="p-6">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <div class="mb-6">
                            <label for="mass" class="block text-gray-700 font-medium mb-2">كتلة الجسم (كجم)</label>
                            <input type="range" id="mass" min="1" max="100" value="10" class="w-full slider-thumb mb-2">
                            <input type="number" id="mass-value" value="10" class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                        
                        <div class="mb-6">
                            <label for="velocity" class="block text-gray-700 font-medium mb-2">السرعة الخطية (م/ث)</label>
                            <input type="range" id="velocity" min="1" max="50" value="10" class="w-full slider-thumb mb-2">
                            <input type="number" id="velocity-value" value="10" class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                        
                        <div class="mb-6">
                            <label for="radius" class="block text-gray-700 font-medium mb-2">نصف قطر المسار (م)</label>
                            <input type="range" id="radius" min="1" max="20" value="5" class="w-full slider-thumb mb-2">
                            <input type="number" id="radius-value" value="5" class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                        
                        <div class="flex space-x-4 space-x-reverse">
                            <button id="calculate-btn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium flex-1">
                                احسب القوة المركزية
                            </button>
                            <button id="reset-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-2 rounded-lg font-medium">
                                إعادة تعيين
                            </button>
                        </div>
                        
                        <div class="mt-6 space-x-4 space-x-reverse flex">
                            <button class="preset-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-1 rounded-lg text-sm" data-m="1000" data-v="20" data-r="150">
                                سيارة (1000 كجم)
                            </button>
                            <button class="preset-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-1 rounded-lg text-sm" data-m="100" data-v="1000" data-r="10000">
                                قمر صناعي
                            </button>
                            <button class="preset-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-1 rounded-lg text-sm" data-m="0.1" data-v="5" data-r="0.5">
                                حجر مربوط
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-6">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4">نتائج الحساب</h3>
                            <div id="result-container" class="hidden">
                                <p class="text-lg mb-4">
                                    القوة المركزية: <span id="force-value" class="font-bold text-green-600">0</span> نيوتن
                                </p>
                                <p class="text-gray-700 mb-2">
                                    المعادلة المستخدمة:
                                </p>
                                <p class="text-center bg-white p-2 rounded-lg mb-4">
                                    \( F_c = \frac{mv^2}{r} = \frac{<span id="mass-display">0</span> \times <span id="velocity-display">0</span>^2}{<span id="radius-display">0</span>} \)
                                </p>
                                <p id="real-world-equivalent" class="text-gray-700 italic">
                                    هذه القوة تعادل وزن جسم كتلته <span id="equivalent-mass">0</span> كجم على سطح الأرض.
                                </p>
                            </div>
                            <div id="initial-message" class="text-gray-500 text-center py-8">
                                <i class="fas fa-calculator text-4xl mb-4"></i>
                                <p>أدخل القيم واضغط على "احسب" لرؤية النتائج</p>
                            </div>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <canvas id="force-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Virtual Lab -->
        <section class="mb-16 bg-white rounded-xl shadow-md overflow-hidden">
            <div class="p-6 bg-purple-500 text-white">
                <h2 class="text-2xl font-bold">محاكي المدارات الدائرية</h2>
            </div>
            <div class="p-6">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <div class="mb-6">
                            <canvas id="circular-motion-canvas" width="500" height="500" class="circular-motion-canvas w-full"></canvas>
                        </div>
                        
                        <div class="flex justify-center space-x-4 space-x-reverse mb-6">
                            <button id="start-btn" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg font-medium">
                                <i class="fas fa-play mr-2"></i>تشغيل
                            </button>
                            <button id="pause-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium">
                                <i class="fas fa-pause mr-2"></i>إيقاف
                            </button>
                            <button id="reset-lab-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-2 rounded-lg font-medium">
                                <i class="fas fa-redo mr-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <div class="mb-6">
                            <label for="lab-mass" class="block text-gray-700 font-medium mb-2">كتلة الجسم (كجم)</label>
                            <input type="range" id="lab-mass" min="1" max="20" value="5" class="w-full slider-thumb">
                        </div>
                        
                        <div class="mb-6">
                            <label for="lab-radius" class="block text-gray-700 font-medium mb-2">نصف قطر المدار (م)</label>
                            <input type="range" id="lab-radius" min="50" max="200" value="100" class="w-full slider-thumb">
                        </div>
                        
                        <div class="mb-6">
                            <label for="lab-velocity" class="block text-gray-700 font-medium mb-2">السرعة الزاوية (راديان/ث)</label>
                            <input type="range" id="lab-velocity" min="0.1" max="2" step="0.1" value="0.5" class="w-full slider-thumb">
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                            <h3 class="font-semibold text-gray-800 mb-2">البيانات الحية</h3>
                            <table class="w-full text-sm">
                                <tr class="border-b">
                                    <td class="py-2 font-medium">القوة المركزية:</td>
                                    <td id="lab-force" class="text-right">0 نيوتن</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 font-medium">السرعة الخطية:</td>
                                    <td id="lab-linear-velocity" class="text-right">0 م/ث</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 font-medium">السرعة الزاوية:</td>
                                    <td id="lab-angular-velocity" class="text-right">0 راديان/ث</td>
                                </tr>
                                <tr>
                                    <td class="py-2 font-medium">التسارع المركزي:</td>
                                    <td id="lab-acceleration" class="text-right">0 م/ث²</td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                            <h3 class="font-semibold text-blue-800 mb-2">تجارب مقترحة</h3>
                            <ul class="list-disc list-inside text-gray-700 space-y-2">
                                <li>ماذا يحدث عند مضاعفة السرعة؟ (يزداد Fc أربع مرات)</li>
                                <li>تأثير الكتلة على القوة المطلوبة (Fc ∝ m)</li>
                                <li>تأثير نصف القطر على القوة المطلوبة (Fc ∝ 1/r)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Content -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">محتويات ذات صلة</h2>
            <div class="grid md:grid-cols-3 gap-6">
                <div class="experiment-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg">
                    <img src="https://www.physicsclassroom.com/images/curriculum/circmotn/cmu1.gif" alt="Circular Motion" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-lg mb-2">علم الحركة الدائرية</h3>
                        <p class="text-gray-600 mb-4">شرح مفصل للحركة الدائرية والتسارع المركزي</p>
                        <a href="#" class="text-blue-500 hover:text-blue-700 font-medium">استكشف الآن →</a>
                    </div>
                </div>
                
                <div class="experiment-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg">
                    <img src="https://www.physicsclassroom.com/Class/momentum/u4l2a1.gif" alt="Momentum" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-lg mb-2">الزخم الزاوي</h3>
                        <p class="text-gray-600 mb-4">كيف يحفظ الزخم الزاوي في الأنظمة الدوارة</p>
                        <a href="#" class="text-blue-500 hover:text-blue-700 font-medium">استكشف الآن →</a>
                    </div>
                </div>
                
                <div class="experiment-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg">
                    <img src="https://www.physicsclassroom.com/images/curriculum/newtlaws/newton3c.gif" alt="Gravity" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-lg mb-2">قانون الجاذبية</h3>
                        <p class="text-gray-600 mb-4">كيف تحافظ الجاذبية على الأجرام في مداراتها</p>
                        <a href="#" class="text-blue-500 hover:text-blue-700 font-medium">استكشف الآن →</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">PhysicsHub</h3>
                    <p class="text-gray-300">
                        منصة تعليمية متكاملة لتعليم الفيزياء بطريقة تفاعلية وسهلة الفهم.
                    </p>
                    <div class="flex space-x-4 space-x-reverse mt-4">
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">الصفحة الرئيسية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الميكانيكا الكلاسيكية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الديناميكا الحرارية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الكهرومغناطيسية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الفيزياء الحديثة</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">مركز المختبرات</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">جميع المحاكيات</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">جميع الحاسبات</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">التجارب التفاعلية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">أدوات المعلمين</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">اتصل بنا</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-gray-300"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2 text-gray-300"></i>
                            <span>+249912867327</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2 text-gray-300"></i>
                            <span>+966538076790</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>جميع الحقوق محفوظة &copy; 2025 PhysicsHub. تم التطوير بواسطة د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - كلية الهندسة الطبية.</p>
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;
        
        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            html.classList.toggle('dark-mode');
            
            if (html.classList.contains('dark')) {
                themeToggle.innerHTML = '<i class="fas fa-sun text-yellow-300"></i>';
            } else {
                themeToggle.innerHTML = '<i class="fas fa-moon text-gray-600"></i>';
            }
        });

        // Force Calculator
        const massInput = document.getElementById('mass');
        const massValue = document.getElementById('mass-value');
        const velocityInput = document.getElementById('velocity');
        const velocityValue = document.getElementById('velocity-value');
        const radiusInput = document.getElementById('radius');
        const radiusValue = document.getElementById('radius-value');
        const calculateBtn = document.getElementById('calculate-btn');
        const resetBtn = document.getElementById('reset-btn');
        const resultContainer = document.getElementById('result-container');
        const initialMessage = document.getElementById('initial-message');
        const forceValue = document.getElementById('force-value');
        const massDisplay = document.getElementById('mass-display');
        const velocityDisplay = document.getElementById('velocity-display');
        const radiusDisplay = document.getElementById('radius-display');
        const equivalentMass = document.getElementById('equivalent-mass');
        const presetBtns = document.querySelectorAll('.preset-btn');

        // Sync slider and number inputs
        massInput.addEventListener('input', () => {
            massValue.value = massInput.value;
        });
        
        massValue.addEventListener('input', () => {
            massInput.value = massValue.value;
        });
        
        velocityInput.addEventListener('input', () => {
            velocityValue.value = velocityInput.value;
        });
        
        velocityValue.addEventListener('input', () => {
            velocityInput.value = velocityValue.value;
        });
        
        radiusInput.addEventListener('input', () => {
            radiusValue.value = radiusInput.value;
        });
        
        radiusValue.addEventListener('input', () => {
            radiusInput.value = radiusValue.value;
        });

        // Calculate centripetal force
        calculateBtn.addEventListener('click', () => {
            const m = parseFloat(massValue.value);
            const v = parseFloat(velocityValue.value);
            const r = parseFloat(radiusValue.value);
            
            const Fc = (m * Math.pow(v, 2)) / r;
            const equivalent = Fc / 9.8; // Convert to equivalent mass on Earth
            
            forceValue.textContent = Fc.toFixed(2);
            massDisplay.textContent = m;
            velocityDisplay.textContent = v;
            radiusDisplay.textContent = r;
            equivalentMass.textContent = equivalent.toFixed(2);
            
            initialMessage.classList.add('hidden');
            resultContainer.classList.remove('hidden');
            
            updateChart(m, v, r);
        });

        // Reset calculator
        resetBtn.addEventListener('click', () => {
            massInput.value = 10;
            massValue.value = 10;
            velocityInput.value = 10;
            velocityValue.value = 10;
            radiusInput.value = 5;
            radiusValue.value = 5;
            
            initialMessage.classList.remove('hidden');
            resultContainer.classList.add('hidden');
        });

        // Preset buttons
        presetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const m = btn.dataset.m;
                const v = btn.dataset.v;
                const r = btn.dataset.r;
                
                massInput.value = m;
                massValue.value = m;
                velocityInput.value = v;
                velocityValue.value = v;
                radiusInput.value = r;
                radiusValue.value = r;
            });
        });

        // Chart for calculator
        const ctx = document.getElementById('force-chart').getContext('2d');
        let forceChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['القوة المركزية'],
                datasets: [{
                    label: 'القوة (نيوتن)',
                    data: [0],
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function updateChart(m, v, r) {
            const Fc = (m * Math.pow(v, 2)) / r;
            
            forceChart.data.datasets[0].data = [Fc];
            forceChart.update();
        }

        // Virtual Lab Simulation
        const canvas = document.getElementById('circular-motion-canvas');
        const ctxLab = canvas.getContext('2d');
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const resetLabBtn = document.getElementById('reset-lab-btn');
        const labMassInput = document.getElementById('lab-mass');
        const labRadiusInput = document.getElementById('lab-radius');
        const labVelocityInput = document.getElementById('lab-velocity');
        const labForceDisplay = document.getElementById('lab-force');
        const labLinearVelocityDisplay = document.getElementById('lab-linear-velocity');
        const labAngularVelocityDisplay = document.getElementById('lab-angular-velocity');
        const labAccelerationDisplay = document.getElementById('lab-acceleration');
        
        let animationId = null;
        let isRunning = false;
        let angle = 0;
        
        // Canvas dimensions
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        
        function drawSimulation() {
            // Clear canvas
            ctxLab.clearRect(0, 0, canvas.width, canvas.height);
            
            // Get current values
            const mass = parseFloat(labMassInput.value);
            const radius = parseFloat(labRadiusInput.value) / 2; // Scale down for canvas
            const angularVelocity = parseFloat(labVelocityInput.value);
            
            // Calculate derived values
            const linearVelocity = radius * angularVelocity;
            const centripetalForce = mass * Math.pow(linearVelocity, 2) / radius;
            const centripetalAcceleration = Math.pow(linearVelocity, 2) / radius;
            
            // Update displays
            labForceDisplay.textContent = centripetalForce.toFixed(2) + " نيوتن";
            labLinearVelocityDisplay.textContent = linearVelocity.toFixed(2) + " م/ث";
            labAngularVelocityDisplay.textContent = angularVelocity.toFixed(2) + " راديان/ث";
            labAccelerationDisplay.textContent = centripetalAcceleration.toFixed(2) + " م/ث²";
            
            // Draw circle path
            ctxLab.beginPath();
            ctxLab.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctxLab.strokeStyle = '#3b82f6';
            ctxLab.lineWidth = 2;
            ctxLab.stroke();
            
            // Draw center point
            ctxLab.beginPath();
            ctxLab.arc(centerX, centerY, 5, 0, Math.PI * 2);
            ctxLab.fillStyle = '#ef4444';
            ctxLab.fill();
            
            // Calculate object position
            const objectX = centerX + radius * Math.cos(angle);
            const objectY = centerY + radius * Math.sin(angle);
            
            // Draw object
            ctxLab.beginPath();
            ctxLab.arc(objectX, objectY, 15, 0, Math.PI * 2);
            ctxLab.fillStyle = '#10b981';
            ctxLab.fill();
            
            // Draw velocity vector (tangent to circle)
            ctxLab.beginPath();
            ctxLab.moveTo(objectX, objectY);
            ctxLab.lineTo(
                objectX + 30 * -Math.sin(angle),
                objectY + 30 * Math.cos(angle)
            );
            ctxLab.strokeStyle = '#f59e0b';
            ctxLab.lineWidth = 3;
            ctxLab.stroke();
            
            // Draw force vector (toward center)
            ctxLab.beginPath();
            ctxLab.moveTo(objectX, objectY);
            ctxLab.lineTo(
                objectX + 30 * -Math.cos(angle),
                objectY + 30 * -Math.sin(angle)
            );
            ctxLab.strokeStyle = '#ef4444';
            ctxLab.lineWidth = 3;
            ctxLab.stroke();
            
            // Update angle for next frame
            if (isRunning) {
                angle += angularVelocity * 0.05; // Slow down rotation for visibility
                
                if (angle > Math.PI * 2) {
                    angle -= Math.PI * 2;
                }
            }
            
            // Continue animation
            animationId = requestAnimationFrame(drawSimulation);
        }
        
        // Start simulation
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                isRunning = true;
                drawSimulation();
            }
        });
        
        // Pause simulation
        pauseBtn.addEventListener('click', () => {
            isRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        });
        
        // Reset simulation
        resetLabBtn.addEventListener('click', () => {
            isRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            angle = 0;
            labMassInput.value = 5;
            labRadiusInput.value = 100;
            labVelocityInput.value = 0.5;
            drawSimulation();
        });
        
        // Update simulation when inputs change
        labMassInput.addEventListener('input', () => {
            if (!isRunning) drawSimulation();
        });
        
        labRadiusInput.addEventListener('input', () => {
            if (!isRunning) drawSimulation();
        });
        
        labVelocityInput.addEventListener('input', () => {
            if (!isRunning) drawSimulation();
        });
        
        // Initial draw
        drawSimulation();
    </script>
</body>
</html>