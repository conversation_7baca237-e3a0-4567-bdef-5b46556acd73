<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PhysicsHub | محرك كارنو الحراري</title>
  <link rel="stylesheet" href="styles/workspace-config.css"/>
  <link rel="stylesheet" href="styles/carnot_engine.css"/>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
  <script src="scripts/workspace-config.js"></script>
</head>
<body>
  <header class="site-header">
    <nav class="navbar">
      <div class="brand">
        <span class="brand-title" data-i18n="brand">PhysicsHub</span>
        <small class="brand-sub" data-i18n="brand_sub">منصة فيزياء تفاعلية</small>
      </div>
      <ul class="nav-links">
        <li><a href="index.html" data-i18n="nav_home">الصفحة الرئيسية</a></li>
        <li><a href="ideal_gas.html" data-i18n="nav_prev">المختبر السابق</a></li>
      </ul>
      <button id="langToggle" class="lang-toggle" aria-label="Toggle language">AR/EN</button>
    </nav>
  </header>

  <main class="container">
    <section class="breadcrumbs">
      <a href="index.html" data-i18n="home">الصفحة الرئيسية</a>
      <span class="sep">/</span>
      <span data-i18n="lesson_breadcrumb">محرك كارنو الحراري</span>
    </section>

    <section class="lesson-hero">
      <h1 data-i18n="lesson_title">محرك كارنو الحراري: القانون الثاني للديناميكا الحرارية والكفاءة</h1>
      <p class="lead" data-i18n="lesson_lead">
        استكشف المحرك الحراري المثالي واكتشف حدود الكفاءة في تحويل الحرارة إلى شغل. تعلم كيف تعتمد الكفاءة القصوى على درجات حرارة المستودعين الساخن والبارد فقط.
      </p>
    </section>

    <section class="theory card">
      <h2 data-i18n="theory_title">المفهوم الأساسي</h2>
      <div class="theory-grid">
        <div>
          <ul class="bullets">
            <li data-i18n="t1">محرك كارنو: المحرك الحراري الأكثر كفاءة نظرياً</li>
            <li data-i18n="t2">يعمل بين مستودعين حراريين عند درجتي حرارة ثابتتين</li>
            <li data-i18n="t3">الكفاءة: η = 1 - T_C/T_H (بالكلفن)</li>
            <li data-i18n="t4">الكفاءة تعتمد فقط على درجات الحرارة</li>
            <li data-i18n="t5">لا يمكن الوصول إلى كفاءة 100% (القانون الثاني)</li>
          </ul>
          <div class="formula">η = (T_H - T_C) / T_H = 1 - T_C/T_H</div>
        </div>
        <div class="media">
          <div class="diagram-box">
            <div class="legend">
              <span class="legend-item"><i class="dot hot"></i> Hot Reservoir</span>
              <span class="legend-item"><i class="dot cold"></i> Cold Reservoir</span>
              <span class="legend-item"><i class="dot work"></i> Work Output</span>
              <span class="legend-item"><i class="dot cycle"></i> Cycle</span>
            </div>
            <div id="p5-container" class="p5-container carnot-workspace" aria-label="Carnot engine simulation"></div>
          </div>
        </div>
      </div>
    </section>

    <section class="lab card">
      <h2 data-i18n="lab_title">المعلمات التفاعلية</h2>
      <div class="lab-grid">
        <div class="lab-controls">
          <div class="field">
            <label for="tempHot" data-i18n="temp_hot_label">درجة حرارة المستودع الساخن T_H (K)</label>
            <input type="range" id="tempHot" min="400" max="1000" step="10" value="600"/>
            <output id="tempHotOut">600</output>
          </div>
          <div class="field">
            <label for="tempCold" data-i18n="temp_cold_label">درجة حرارة المستودع البارد T_C (K)</label>
            <input type="range" id="tempCold" min="200" max="500" step="10" value="300"/>
            <output id="tempColdOut">300</output>
          </div>
          <div class="field">
            <label for="heatInput" data-i18n="heat_input_label">الحرارة الداخلة Q_H (J)</label>
            <input type="range" id="heatInput" min="100" max="1000" step="10" value="500"/>
            <output id="heatInputOut">500</output>
          </div>
          <div class="field">
            <label for="cycleSpeed" data-i18n="cycle_speed_label">سرعة الدورة</label>
            <input type="range" id="cycleSpeed" min="0.1" max="2" step="0.1" value="1"/>
            <output id="cycleSpeedOut">1.0</output>
          </div>
          
          <div class="presets">
            <h3 data-i18n="presets_title">إعدادات مسبقة</h3>
            <div class="preset-buttons">
              <button class="btn preset" data-preset="steam" data-i18n="preset_steam">محرك بخاري</button>
              <button class="btn preset" data-preset="car" data-i18n="preset_car">محرك سيارة</button>
              <button class="btn preset" data-preset="power" data-i18n="preset_power">محطة طاقة</button>
            </div>
          </div>
          
          <div class="buttons">
            <button id="reset" class="btn outline" data-i18n="reset">إعادة ضبط</button>
            <button id="start" class="btn" data-i18n="start">بدء الدورة</button>
          </div>
        </div>

        <div class="lab-visuals">
          <div class="dash">
            <div class="dash-section">
              <h4 data-i18n="efficiency_title">الكفاءة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_efficiency">η</span> = <span id="dashEfficiency">50.0</span>%
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_max_efficiency">η_max</span> = <span id="dashMaxEfficiency">50.0</span>%
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="energy_title">الطاقة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_qh">Q_H</span> = <span id="dashQH">500</span> J
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_qc">Q_C</span> = <span id="dashQC">250</span> J
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_work">W</span> = <span id="dashWork">250</span> J
                </div>
              </div>
            </div>
            
            <div class="dash-section">
              <h4 data-i18n="temperatures_title">درجات الحرارة</h4>
              <div class="dash-row">
                <div class="dash-item">
                  <span data-i18n="dash_th">T_H</span> = <span id="dashTH">600</span> K
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_tc">T_C</span> = <span id="dashTC">300</span> K
                </div>
                <div class="dash-item">
                  <span data-i18n="dash_delta_t">ΔT</span> = <span id="dashDeltaT">300</span> K
                </div>
              </div>
            </div>
            
            <canvas id="efficiencyChart" height="120"></canvas>
          </div>
        </div>
      </div>
    </section>

    <section class="cta card">
      <h2 data-i18n="cta_title">الهدف التعليمي</h2>
      <p data-i18n="cta_text">
        فهم أن كفاءة المحرك الحراري تعتمد على فرق درجات الحرارة بين المستودعين، وأنه من المستحيل الوصول إلى كفاءة 100% (القانون الثاني للديناميكا الحرارية). كلما زاد الفرق في درجات الحرارة، زادت الكفاءة القصوى الممكنة.
      </p>
    </section>
  </main>

  <footer class="site-footer">
    <div class="footer-content">
      <div class="left">
        <strong data-i18n="footer_author_label">المؤلف وحقوق النشر:</strong>
        <span>Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025.</span>
      </div>
      <div class="right">
        <strong data-i18n="footer_contact_label">للتواصل:</strong>
        <span>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          <span data-i18n="footer_phone">الهاتف</span>:
          <a href="tel:+249912867327">+249912867327</a>,
          <a href="tel:+966538076790">+966538076790</a>
        </span>
      </div>
    </div>
  </footer>

  <script src="scripts/carnot_engine.js"></script>
</body>
</html>
